## Install magento-media-migrator to the production old environment
```
cd ./.migration/chart
helm dependency update && helm upgrade -i magento-media-migrator --namespace web-pool . -f values.production.old.yaml
```

## Remove magento-media-migrator from the production old environment
```
cd ./.migration/chart
helm uninstall magento-media-migrator --namespace  web-pool
```


## Install magento-media-migrator to the production old staging
```
cd ./.migration/chart
helm dependency update && helm upgrade -i magento-media-migrator --namespace stage-pool . -f values.staging.old.yaml
```

## Remove magento-media-migrator from the production old staging
```
cd ./.migration/chart
helm uninstall magento-media-migrator --namespace  stage-pool
```


## Install magento-media-migrator to the development environment
```
cd ./.migration/chart
helm dependency update && helm upgrade -i magento-media-migrator --namespace magento-development . -f values.development.yaml
```

## Remove magento-media-migrator from the development environment
```
cd ./.migration/chart
helm uninstall magento-media-migrator --namespace magento-development
```

## Install magento-media-migrator to the production environment
```
cd ./.migration/chart
helm dependency update && helm upgrade -i magento-media-migrator --namespace magento-production . -f values.production.yaml
```

## Remove magento-media-migrator from the production environment
```
cd ./.migration/chart
helm uninstall magento-media-migrator --namespace magento-production
```
