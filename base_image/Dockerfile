FROM php:8.3-fpm

RUN apt-get update && \
    apt-get install -y \
      build-essential \
      cron \
      curl \
      default-mysql-client \
      git \
      htop \
      libcurl3-dev \
      libcurl4 \
      libcurl4-openssl-dev \
      libfreetype6 \
      libfreetype6-dev \
      libgcrypt-dev \
      libicu-dev \
      libjpeg62-turbo \
      libjpeg62-turbo-dev \
      libmagickwand-dev \
      libmcrypt-dev \
      libmcrypt4 \
      libmemcached-dev \
      libonig-dev \
      libpng-dev \
      libwebp-dev \
      libxslt-dev \
      libxslt1-dev \
      libzip-dev \
      memcached \
      net-tools \
      nfs-common \
      nginx \
      python-is-python3 \
      sendmail \
      sendmail-bin \
      sudo \
      unzip \
      vim \
      wget \
      zip \
      zlib1g-dev

RUN docker-php-ext-configure gd --enable-gd --with-freetype --with-jpeg && \
    docker-php-ext-install -j "$(nproc)" gd && \
    docker-php-ext-install pdo_mysql && \
    docker-php-ext-install dom && \
    docker-php-ext-install mbstring && \
    docker-php-ext-install zip && \
    docker-php-ext-install intl && \
    docker-php-ext-install pcntl && \
    docker-php-ext-install xsl && \
    docker-php-ext-install soap && \
    docker-php-ext-install sockets && \
    docker-php-ext-configure opcache --enable-opcache && \
    docker-php-ext-install opcache && \
    docker-php-ext-install bcmath

RUN curl -sS https://getcomposer.org/installer | php
RUN mv composer.phar /usr/local/bin/composer

RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash
RUN apt-get install -y nodejs
