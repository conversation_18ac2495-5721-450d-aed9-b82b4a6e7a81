version: v1.0
name: KEH-MAGENTO CI/CD PIPELINE
agent:
  machine:
    type: e1-standard-8
    os_image: ubuntu2004
blocks:
  - name: PRE CHECKS
    dependencies: []
    task:
      jobs:
        - name: "Pre check template"
          commands:
            - checkout
            - env
  - name: BUMP VERSION
    run:
      when: "branch =~ '^release/v'"
    dependencies: ["PRE CHECKS"]
    task:
      jobs:
        - name: "Auto bump version"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - bumpVersionPatch
      secrets:
        - name: github-semaphore
  - name: BUILD AND PUSH BASE IMAGE
    run:
      when: "change_in('/base_image/Dockerfile', { default_branch: 'develop', pipeline_file: 'ignore' })"
    dependencies: ["BUMP VERSION"]
    task:
      jobs:
        - name: "base_image - build and push"
          commands:
            - checkout
            - cd /home/<USER>/keh-magento/base_image
            - gcloud auth activate-service-account <EMAIL> --key-file=/home/<USER>/gcloud_secret_keh_development.json --project=keh-development
            - gcloud -q auth configure-docker us-central1-docker.pkg.dev
            - docker build -t us-central1-docker.pkg.dev/keh-development/keh-magento-base-image/base_image:${SEMAPHORE_GIT_SHA} -t us-central1-docker.pkg.dev/keh-development/keh-magento-base-image/base_image:latest .
            - docker push -a us-central1-docker.pkg.dev/keh-development/keh-magento-base-image/base_image
      secrets:
        - name: keh-development
  - name: BUILD AND PUSH
    dependencies: ["BUILD AND PUSH BASE IMAGE"]
    task:
      jobs:
        - name: "magento image - build and push"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - runForBumpVersionOrMainBranch
            - export TAG=${SEMAPHORE_GIT_SHA}
            - if [[ "${SEMAPHORE_GIT_BRANCH}" == *"release/v"* ]]; then export TAG=$(cat .version); fi
            - echo ${TAG}
            - gcloud auth activate-service-account <EMAIL> --key-file=/home/<USER>/gcloud_secret_keh_development.json --project=keh-development
            - gcloud -q auth configure-docker us-central1-docker.pkg.dev
            - docker build --no-cache --build-arg BASE_IMAGE=us-central1-docker.pkg.dev/keh-development/keh-magento-base-image/base_image:latest -t us-central1-docker.pkg.dev/keh-development/keh-magento/keh-magento:${TAG} .
            - docker push us-central1-docker.pkg.dev/keh-development/keh-magento/keh-magento:${TAG}
      secrets:
        - name: keh-development
  - name: TESTS
    dependencies: ["BUILD AND PUSH"]
    task:
      jobs:
        - name: "Tests template"
          commands:
            - checkout
  - name: DEPLOY TO STAGING
    run:
      when: "branch =~ '^release/v'"
    dependencies: [ "TESTS" ]
    task:
      jobs:
        - name: "Deploy to staging cluster"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - export TAG=$(cat .version)
            - echo $TAG
            - export PROJECT_DIR=$(pwd)
            - echo ${PROJECT_DIR}
            - git clone https://github.com/KEHCamera/keh-development-deployment.git /home/<USER>/keh-development-deployment
            - cd /home/<USER>/keh-development-deployment/us-central1/magento-staging/helmreleases/values
            - yq --inplace --exit-status eval ".deployment.tag=strenv(TAG)" hr-magento-values.yaml
            - git add hr-magento-values.yaml
            - cd /home/<USER>/keh-development-deployment/us-central1/magento-staging/helmreleases/chart
            - rsync -r --delete ${PROJECT_DIR}/chart/templates/ ./templates
            - rm -rf ./templates/old_templates
            - cp -f ${PROJECT_DIR}/chart/Chart.yaml ./
#            - cp -f ${PROJECT_DIR}/chart/values.development.yaml  ./values.yaml
            - yq --inplace --exit-status eval ".appVersion=strenv(TAG)" Chart.yaml
            - git add .
            - git commit -m"Upgrading magento-staging to ${TAG}"
            - git push
            - "/home/<USER>/keh-magento/.semaphore/scripts/slack-notify.sh -s ok -z \"*MAGENTO STAGING* upgrade triggered\" -p \"\" -T \"${SEMAPHORE_WORKFLOW_TRIGGERED_BY} has just started the STAGING promotion\" -Z \"for TAG=*${TAG}*\n from <${SEMAPHORE_ORGANIZATION_URL}/workflows/${SEMAPHORE_WORKFLOW_ID}?pipeline_id=${SEMAPHORE_PIPELINE_ID}|CI/CD pipeline>\""
      secrets:
        - name: github-semaphore
        - name: slack-webhook-deployment
promotions:
  - name: DEVELOPMENT (from main or release branch only)
    pipeline_file: ./deploy-development.yml
  - name: RUN TESTS ON STAGING
    pipeline_file: ./run-tests-on-staging.yml
    auto_promote:
      when: branch =~ '^release/v' AND result = 'passed'
