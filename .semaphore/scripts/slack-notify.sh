#!/bin/sh
set -e

# Slack API Endpoint
if [ -z "${SLACK_WEBHOOK}" ]; then
    echo "ERROR: The Slack WEBHOOK is not set as an env variable! Exiting."
    exit 1
elif [ -n "${SLACK_WEBHOOK}" ]; then
    echo "INFO: The Slack API TOKEN was set as an env variable"
    WEBHOOK=${SLACK_WEBHOOK}
fi

helper() {
    echo "Usage: "
    echo "-C, Color                     This value is used to color the border along the left side of the message attachment."
    echo "-h, Help                      Show the command options for Slack."
    echo "-p, Pretext                   This is optional text that appears above the message attachment block."
    echo "-s, Status                    An optional value that can either be one of ok, info, warn or error."
    echo "-z, Text                      This is the main text in a message."
    echo "-Z, inner Text                This is the main text in a message attachment, and can contain standard message markup."
    echo "-E, ExtraText                 This is an additional text block in a message attachment, and can contain message markup. 'Info' color is applied."
    echo "-T, Title                     The title is displayed as larger, bold text near the top of a message attachment."
    exit 1
}

exit_abnormal() { # Exit with error.
    helper
    exit 1
}

# Check if any arguments were passed
if [ $# -eq 0 ]; then
    helper
    exit 1
else
while getopts "C:h:p:s:z:Z:T:E:L" opt; do
    case ${opt} in
    C) COLOR="${OPTARG}" ;;
    h) helper ;;
    p) PRETEXT="${OPTARG}" ;;
    s)
        if test "${OPTARG}" = "ok"; then PRIORITY="OK"; fi
        if test "${OPTARG}" = "info"; then PRIORITY='INFO'; fi
        if test "${OPTARG}" = "warn"; then PRIORITY='WARN'; fi
        if test "${OPTARG}" = "error"; then PRIORITY='ERROR'; fi
        ;;
    z) TEXT="${OPTARG}" ;;
    Z) INNER_TEXT="${OPTARG}" ;;
    T) TITLE="${OPTARG}" ;;
    E) EXTRATEXT="${OPTARG}" ;;
    *) exit_abnormal ;;
    esac
    done
fi

# set timestamp
EPOCH=$(date +%s)

# test for key parts of the message
if [ -z "${TEXT+x}" ]; then echo "WARNING: You do not have any TEXT (-z) specified in the message."; TEXT="${TEXT:-'This message is missing TEXT'}"; else echo "INFO: TEXT is set to '${TEXT}'"; fi
if [ -z "${INNER_TEXT+x}" ]; then echo "WARNING: You do not have any INNER_TEXT (-Z) specified in the message."; INNER_TEXT="${INNER_TEXT:-'This message is missing INNER_TEXT'}"; else echo "INFO: INNER_TEXT is set to '${INNER_TEXT}'"; fi
if [ -z "${TITLE+x}" ]; then echo "WARNING: You do not have a TITLE (-T) specified for the message."; TITLE=${TITLE:-'This message is missing a TITLE'}; else echo "INFO: TITLE is set to '${TITLE}'"; fi
if [ -z "${PRETEXT+x}" ]; then echo "WARNING: You do not have a PRETEXT (-p) specified for the message."; PRETEXT=${PRETEXT:-'This message is missing a PRETEXT'}; else echo "INFO: PRETEXT is set to '${PRETEXT}'"; fi

# color and style
if test "${PRIORITY}" = "OK"; then echo "INFO: STATUS (-s) was set to OK..."; ICON=${ICON:-'good'} && COLOR=${COLOR:-'#36a64f'}; fi
if test "${PRIORITY}" = "INFO"; then echo "INFO: STATUS (-s) was set to INFO..."; ICON=${ICON:-'info'} && COLOR=${COLOR:-'#439FE0'}; fi
if test "${PRIORITY}" = "WARN"; then echo "INFO: STATUS (-s) was set to WARN..."; ICON=${ICON:-'warn'} && COLOR=${COLOR:-'#ed7d21'}; fi
if test "${PRIORITY}" = "ERROR"; then echo "INFO: STATUS (-s) was set to ERROR..."; ICON=${ICON:-'error'} && COLOR=${COLOR:-'#E21B6C'}; fi

# send_request

# The complete Slack API payload, including attachments#

EXTRA_ATTACHMENT=$(cat <<-END
,
        {
            "mrkdwn_in": "[**pretext**]",
            "color": "#439FE0",
            "pretext":"",
            "text": "${EXTRATEXT}",
            "mrkdwn_in": ["text","pretext","fields"],
            "ts": "${EPOCH}"
        }
END
)

if [ -z "${EXTRATEXT+x}" ]; then
    echo "WARNING: You do not have a EXTRATEXT (-E) specified for the message.";
    EXTRA_ATTACHMENT=""
else
    echo "INFO: EXTRATEXT is set to '${EXTRATEXT}'";
fi

PAYLOAD=$(cat <<-END
    payload={
        "pretext": "${PRETEXT}",
        "color": "${COLOR}",
        "text": "${TEXT}",
        "link_names": "true",
        "mrkdwn": "true",
        "attachments": [
            {
                "mrkdwn_in": "[**pretext**]",
                "color": "${COLOR}",
                "pretext": "${PRETEXT}",
                "title": "${TITLE}",
                "text": "${INNER_TEXT}",
                "mrkdwn_in": ["text","pretext","fields"],
                "ts": "${EPOCH}"
            }
            ${EXTRA_ATTACHMENT}
        ]
    }
END
)

# Send the payload to the Slack API
echo "OK: All tests passed, sending message to Slack API..."
POST=$(curl -s -S -X POST --data-urlencode "${PAYLOAD}" "${WEBHOOK}");

# Check if the message posted to the Slack API. A successful POST should return "ok". Anything other than "ok" indicates an issue
if test "${POST}" != ok; then
    echo "ERROR: The POST to the Slack API failed (${POST})";
    echo "PAYLOAD: ${PAYLOAD}";
    return 1;
else
    echo "OK: Message successfully sent via the Slack API";
fi


exit 0
