#!/bin/bash
set -x
# Initial
git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/KEHCamera".insteadOf "https://github.com/KEHCamera"
git config --global --add url."https://${GITHUB_TOKEN}:<EMAIL>/KEHCamera".insteadOf "**************:KEHCamera"
git config --global user.email "<EMAIL>"
git config --global user.name "keh-devops"
pip install --upgrade bumpversion

bumpVersionPatch() {
    export COMMIT_MESSAGE=$(git log -1 --pretty=%B | tr -d '\n')
    # avoid loop
    if [[ "${COMMIT_MESSAGE}" != *"Bump version: "* ]]
    then
      bumpversion_cfg_file=".bumpversion.cfg"
      /home/<USER>/.local/bin/bumpversion --config-file ${bumpversion_cfg_file} patch
      git push
      git push --tags
    fi
}

startRelease() {
    bumpversion_cfg_file=".bumpversion.cfg"
    /home/<USER>/.local/bin/bumpversion --config-file ${bumpversion_cfg_file} --message 'Bump version: {current_version} → {new_version} [skip ci]' minor
    export RELEASE_BRANCH_NAME="release/v$(cat .version)"
    git branch ${RELEASE_BRANCH_NAME}
    git push --tags
    git config pull.rebase true
    git pull
    git push
    git checkout ${RELEASE_BRANCH_NAME}
    # "Dummy" commit
    echo "\n" >> README.md
    git add README.md && git commit -m "Bump version: Creating ${RELEASE_BRANCH_NAME}"
    git push -u origin ${RELEASE_BRANCH_NAME}
}

runForBumpVersionOrMainBranch() {
    export COMMIT_MESSAGE=$(git log -1 --pretty=%B | tr -d '\n')
    # avoid loop
    if [[ "${COMMIT_MESSAGE}" != *"Bump version: "* ]] && [[ "${SEMAPHORE_GIT_BRANCH}" == *"release/v"* ]]
    then
      echo "Don't run for commits different than bump version in release branches. Skipping..."
      return 130
    fi
}
