version: v1.0
name: DEPLOY TO PRODUCTION
agent:
  machine:
    type: e1-standard-8
    #    type: s1-keh-development-cluster
    os_image: ubuntu2004
#  containers:
#    - name: main
#      image: 'semaphoreci/ubuntu:20.04'
#      env_vars:
#        - name: FOO_1
#          value: BAR_1
blocks:
  - name: PUSH IMAGE TO KEH-PRODUCTION
    run:
      when: "branch =~ '^release/v'"
    dependencies: []
    task:
      jobs:
        - name: "pull release image and push it to keh-production"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - runForBumpVersionOrMainBranch
            - export TAG=$(cat .version)
            - echo $TAG
            - gcloud auth activate-service-account <EMAIL> --key-file=/home/<USER>/gcloud_secret_keh_development.json --project=keh-development
            - gcloud -q auth configure-docker us-central1-docker.pkg.dev
            - docker pull us-central1-docker.pkg.dev/keh-development/keh-magento/keh-magento:${TAG}
            - docker tag us-central1-docker.pkg.dev/keh-development/keh-magento/keh-magento:${TAG} us-central1-docker.pkg.dev/keh-production/keh-magento/keh-magento:${TAG}
            - gcloud auth activate-service-account <EMAIL> --key-file=/home/<USER>/gcloud_secret_keh_production.json --project=keh-production
            - gcloud -q auth configure-docker us-central1-docker.pkg.dev
            - docker push us-central1-docker.pkg.dev/keh-production/keh-magento/keh-magento:${TAG}
      secrets:
        - name: keh-development
        - name: keh-production
  - name: DEPLOY
    run:
      when: "branch =~ '^release/v'"
    dependencies: [ "PUSH IMAGE TO KEH-PRODUCTION" ]
    task:
      jobs:
        - name: "Deploy to production cluster"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - runForBumpVersionOrMainBranch
            - export TAG=$(cat .version)
            - echo $TAG
            - export PROJECT_DIR=$(pwd)
            - echo ${PROJECT_DIR}
            - git clone https://github.com/KEHCamera/keh-production-deployment.git /home/<USER>/keh-production-deployment
            - cd /home/<USER>/keh-production-deployment/us-central1/magento-production/helmreleases/values
            - yq --inplace --exit-status eval ".deployment.tag=strenv(TAG)" hr-magento-values.yaml
            - git add hr-magento-values.yaml
            - cd /home/<USER>/keh-production-deployment/us-central1/magento-production/helmreleases/chart
            - rsync -r --delete ${PROJECT_DIR}/chart/templates/ ./templates
            - rm -rf ./templates/old_templates
            - cp -f ${PROJECT_DIR}/chart/Chart.yaml ./
#            - cp -f ${PROJECT_DIR}/chart/values.production.yaml  ./values.yaml
            - yq --inplace --exit-status eval ".appVersion=strenv(TAG)" Chart.yaml
            - git add .
            - git commit -m"Upgrading magento-production to ${TAG}"
            - git push
            - "/home/<USER>/keh-magento/.semaphore/scripts/slack-notify.sh -s warn -z \"*MAGENTO PRODUCTION* upgrade triggered\" -p \"\" -T \"${SEMAPHORE_PIPELINE_PROMOTED_BY} has just started the PRODUCTION promotion\" -Z \"for TAG=*${TAG}*\n from <${SEMAPHORE_ORGANIZATION_URL}/workflows/${SEMAPHORE_WORKFLOW_ID}?pipeline_id=${SEMAPHORE_PIPELINE_ID}|CI/CD pipeline>\""
      secrets:
        - name: github-semaphore
        - name: slack-webhook-deployment
