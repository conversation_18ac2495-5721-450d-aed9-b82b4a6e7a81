version: v1.0
name: <PERSON><PERSON> TESTS ON STAGING
agent:
  machine:
#    type: e1-standard-8
    type: s1-keh-development-cluster
#    os_image: ubuntu2004
  containers:
    - name: main
      image: 'semaphoreci/ubuntu:20.04'
#      env_vars:
#        - name: FOO_1
#          value: BAR_1
blocks:
  - name: WAIT FOR DEPLOYMENT TO FINISH
    run:
      when: "branch =~ '^release/v'"
    dependencies: []
    task:
      jobs:
        - name: "wait for deployment to finish"
          commands:
            - checkout
            - export TAG=$(cat .version)
            - .semaphore/scripts/wait-for-staging-deployment-ends.sh
  - name: START TESTS EXECUTION
    run:
      when: "branch =~ '^release/v'"
    dependencies: ["WAIT FOR DEPLOYMENT TO FINISH"]
    task:
      jobs:
        - name: "execute tests on staging"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - export TAG=$(cat .version)
            - export TEST_REPO="https://github.com/KEHCamera/keh-test-automation.git"
            - export TEST_BRANCH=main
            - pwd
            - ".semaphore/scripts/slack-notify.sh -s ok -z \"*MAGENTO STAGING* automated tests triggered\" -p \"\" -T \"AUTOMATED STAGING TESTS have just started\" -Z \"for TAG=*${TAG}*\nfrom REPO=<${TEST_REPO}>\nBRANCH=*${TEST_BRANCH}*\n from <${SEMAPHORE_ORGANIZATION_URL}/workflows/${SEMAPHORE_WORKFLOW_ID}?pipeline_id=${SEMAPHORE_PIPELINE_ID}|CI/CD pipeline>\""
            - git clone ${TEST_REPO} /home/<USER>/keh-test-automation
            - cd /home/<USER>/keh-test-automation
            - git checkout ${TEST_BRANCH}
            - curl -sL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            - curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg
            - echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
            - apt-get update
            - apt-get install -y nodejs google-cloud-cli
            - node -v
            - npm install
            - npx playwright install
            - sudo apt-get install -y libgbm1
            - npx playwright install-deps
            - echo "Running on Staging"
            - npx playwright test --grep=@STAGE
            - tar czf test-results.tar.gz test-results
            - tar czf report-db.tar.gz report-db
            - artifact push workflow test-results.tar.gz
            - artifact push workflow report-db.tar.gz
            - artifact push workflow test-results
            - artifact push workflow report-db
            - gcloud auth activate-service-account <EMAIL> --key-file=/home/<USER>/gcloud_secret_keh_development.json --project=keh-development
            - ( gcloud storage rm --recursive gs://keh-test-automation-results/magento || true )
            - cp -r report-db magento
            - gcloud storage cp --recursive magento gs://keh-test-automation-results
            - cd -
            - ".semaphore/scripts/slack-notify.sh -s ok -z \"*MAGENTO STAGING* automated tests COMPLETED\" -p \"\" -T \"AUTOMATED STAGING TESTS have just finished\" -Z \"for TAG=*${TAG}*\nresults: <https://storage.cloud.google.com/keh-test-automation-results/magento/index.html>\""
      secrets:
        - name: github-semaphore
        - name: slack-webhook-deployment
        - name: keh-development
promotions:
  - name: SET SKIP POST DEPLOYMENT JOB FOR PRODUCTION
    pipeline_file: ./set-skip-post-deployment.yml
  - name: PRODUCTION (from release branch only)
    pipeline_file: ./deploy-production.yml
  - name: SET EXECUTE POST DEPLOYMENT JOB FOR PRODUCTION
    pipeline_file: ./set-execute-post-deployment.yml
