version: v1.0
name: SET EXECUTE POST DEPLOYMENT JOB FOR PRODUCTION
agent:
  machine:
    #    type: e1-standard-8
    type: s1-keh-development-cluster
  #    os_image: ubuntu2004
  containers:
    - name: main
      image: 'us-central1-docker.pkg.dev/keh-development/keh-devops-core/semaphore-agent-on-keh-development-k8s-cluster:latest'
#      env_vars:
#        - name: FOO_1
#          value: BAR_1
blocks:
  - name: SET EXECUTE POST-DEPLOY
    run:
      when: "branch =~ '^release/v'"
    dependencies: []
    task:
      jobs:
        - name: "Set execute post-deploy flag"
          commands:
            - checkout
            - source .semaphore/scripts/configure-production-post-deployment-job.sh
            - setPostDeployFlag EXECUTE
            - ".semaphore/scripts/slack-notify.sh -s warn -z \"*MAGENTO PRODUCTION* post-deploy job reconfigured\" -p \"\" -T \"post deploy job has been reconfigured by ${SEMAPHORE_PIPELINE_PROMOTED_BY}\" -Z \"current setting is *EXECUTE*\n from <${SEMAPHORE_ORGANIZATION_URL}/workflows/${SEMAPHORE_WORKFLOW_ID}?pipeline_id=${SEMAPHORE_PIPELINE_ID}|CI/CD pipeline>\""
      secrets:
        - name: keh-production
        - name: slack-webhook-deployment
