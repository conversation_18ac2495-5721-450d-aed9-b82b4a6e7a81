version: v1.0
name: DEPLOY TO DEVELOPMENT
agent:
  machine:
    type: e1-standard-8
    os_image: ubuntu2004
blocks:
  - name: DEPLOY
    run:
      when: "branch = 'develop' OR branch =~ '^release/v'"
    dependencies: []
    task:
      jobs:
        - name: "Deploy to development cluster"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - export TAG=${SEMAPHORE_GIT_SHA}
            - if [[ "${SEMAPHORE_GIT_BRANCH}" == *"release/v"* ]]; then export TAG=$(cat .version); fi
            - git clone https://github.com/KEHCamera/keh-development-deployment.git /home/<USER>/keh-development-deployment
            - cd /home/<USER>/keh-development-deployment/us-central1/magento-development/helmreleases/values
            - yq --inplace --exit-status eval ".deployment.tag=strenv(TAG)" hr-magento-values.yaml
            - git add hr-magento-values.yaml
            - git commit -m"Upgrading magento-development to ${TAG}"
            - git push
            - "/home/<USER>/keh-magento/.semaphore/scripts/slack-notify.sh -s ok -z \"*MAGENTO DEVELOPMENT* upgrade triggered\" -p \"\" -T \"${SEMAPHORE_PIPELINE_PROMOTED_BY} has just started the DEVELOPMENT promotion\" -Z \"for TAG=*${TAG}*\n from <${SEMAPHORE_ORGANIZATION_URL}/workflows/${SEMAPHORE_WORKFLOW_ID}?pipeline_id=${SEMAPHORE_PIPELINE_ID}|CI/CD pipeline>\""
      secrets:
        - name: github-semaphore
        - name: slack-webhook-deployment
promotions:
  - name: RELEASE
    pipeline_file: ./release.yml
