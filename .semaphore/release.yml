version: v1.0
name: RELEASE
agent:
  machine:
    type: e1-standard-8
    #    type: s1-keh-development-cluster
    os_image: ubuntu2004
#  containers:
#    - name: main
#      image: 'semaphoreci/ubuntu:20.04'
#      env_vars:
#        - name: FOO_1
#          value: BAR_1
blocks:
  - name: CREATE RELEASE
    run:
      when: "branch = 'develop'"
    dependencies: [ ]
    task:
      jobs:
        - name: "Create release"
          commands:
            - checkout
            - source .semaphore/scripts/bumpversion.sh
            - startRelease
      secrets:
        - name: github-semaphore
