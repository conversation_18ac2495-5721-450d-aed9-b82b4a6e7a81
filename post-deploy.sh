#!/bin/bash
set -x

export SCRIPT_START=`date +%s`
# skip execution of post deploy job if file exists in given path
if ! [ -f /var/www/keh-magento/var/.skip-post-deploy ]
then
    mkdir -p /var/www/keh-magento/pub/media/captcha
    mkdir -p /var/www/keh-magento/pub/media/catalog/product
    mkdir -p /var/www/keh-magento/pub/media/csv_rewrite
    mkdir -p /var/www/keh-magento/pub/media/feeds
    mkdir -p /var/www/keh-magento/pub/media/klevu_images
    mkdir -p /var/www/keh-magento/pub/media/sitemap
    mkdir -p /var/www/keh-magento/pub/media/slider
    mkdir -p /var/www/keh-magento/pub/media/tmp
    mkdir -p /var/www/keh-magento/pub/media/wysiwyg
    mkdir -p /var/www/keh-magento/pub/apps
    mkdir -p /var/www/keh-magento/var/cache
    mkdir -p /var/www/keh-magento/var/export
    mkdir -p /var/www/keh-magento/var/import
    mkdir -p /var/www/keh-magento/var/import_history
    mkdir -p /var/www/keh-magento/var/importexport
    mkdir -p /var/www/keh-magento/var/inchoo_testimonials
    mkdir -p /var/www/keh-magento/var/page_cache

    rm -rf var/cache/*
    rm -rf var/view_preprocessed/*
    rm -rf pub/apps/*

    date
    php bin/magento maintenance:enable
    date
    php bin/magento deploy:mode:set production --skip-compilation
    date
    # executes cleanup for pub/static, creates queues in rabbitmq
    php bin/magento setup:upgrade # --keep-generated
    date
    # creates contect for pub/static
    php bin/magento setup:static-content:deploy --exclude-theme=Magento/luma --jobs 10
    date
    php bin/magento webpos:deploy
    date
#    php bin/magento indexer:reindex
#    date
    php bin/magento cache:flush
    date
    php bin/magento maintenance:disable
    date
    chown -R www-data:www-data /var/www/keh-magento/pub/apps
    date
fi

export SCRIPT_STOP=`date +%s`
# notification
export SLACK_STATUS=ok
export ENV_UPPER=$(echo ${ENV} | tr '[:lower:]' '[:upper:]')
export MESSAGE="*MAGENTO ${ENV_UPPER}* upgrade COMPLETED"
export SCRIPT_RUN_TIME=$((SCRIPT_STOP-SCRIPT_START))
export TITLE="post deploy job has just finished - it took ${SCRIPT_RUN_TIME} seconds"

if [[ "${ENV}" == "production" ]]
then
    SLACK_STATUS=warn
fi

.semaphore/scripts/slack-notify.sh -s ${SLACK_STATUS} -z "${MESSAGE}" -p "" -T "${TITLE}" -Z "for TAG=*${TAG}*"

exit 0

#chown -R www-data:www-data /var/www/keh-magento/var /var/www/keh-magento/pub
# 7
#mkdir -p /var/www/sites/keh.com/files/html/var/page_cache
#mkdir -p /var/www/sites/keh.com/files/html/var/cache
#mkdir -p /var/www/sites/keh.com/files/html/var/log
#mkdir -p /var/www/sites/keh.com/files/html/pub/media/catalog/product
#mkdir -p /var/www/sites/keh.com/files/html/pub/media/captcha
#mountSrc=/var/www/sites/keh.com/html/pub/media
#mountDest=/var/www/sites/keh.com/files/html
## Source (key) is the persistent path (NFS/PVC)
##   Outside of Magento
## Destination (value) is the path that should link to the persistent path
##   Inside the Magento root
#declare -A mounts=(
#  [/catalog]=/pub/media/catalog
#  [/import]=/var/import
#  [/import_history]=/var/import_history
#  [/importexport]=/var/importexport
#  [/export]=/var/export
#  [/wysiwyg]=/pub/media/wysiwyg
#  [/feeds]=/pub/media/feeds
#  [/klevu_images]=/pub/media/klevu_images
#  [/var/inchoo_testimonials]=/var/inchoo_testimonials
#  [/sitemap]=/pub/media/sitemap
#  [/captcha]=/pub/media/captcha
#  [/slider]=/pub/media/slider
#  [/csv_rewrite]=/pub/media/csv_rewrite
#  [/tmp]=/pub/media/tmp
#)
## ln -s $mountSrc/maintenance/.maintenance.flag $mountDest/var/.maintenance.flag
## ln -s $mountSrc/maintenance/.maintenance.ip $mountDest/var/.maintenance.ip
## ln -s $mountSrc/maintenance/.maintenance.fpc.state $mountDest/var/.maintenance.fpc.state
