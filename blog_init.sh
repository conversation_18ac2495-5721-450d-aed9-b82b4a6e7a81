#!/usr/bin/env bash

# Default image comes with mariadb client, that can't be used
sudo apt update
sudo apt install mysql-client-5.7 -y

wget -P /var/www/html http://*************/keh-m2/blog_database.sql.gz
echo "CREATE DATABASE blog CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" | mysql -uroot -pinchoo -hdb
echo "GRANT ALL PRIVILEGES ON blog.* TO 'inchoo'@'%';" | mysql -uroot -pinchoo -hdb
gunzip < /var/www/html/blog_database.sql.gz | mysql -uinchoo -pinchoo -hdb blog
rm -f /var/www/html/blog_database.sql.gz
