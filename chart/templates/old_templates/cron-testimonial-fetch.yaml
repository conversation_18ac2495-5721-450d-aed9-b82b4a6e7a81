apiVersion: "batch/v1"
kind: "CronJob"
metadata:
  name: {{ .Values.inchoo.testimonial.name }}
  namespace: {{ .Values.global.namespace }}
  labels:
    app: {{ .Values.inchoo.testimonial.name }}
    environment: {{ .Values.global.environment }}
spec:
  suspend: {{ .Values.inchoo.testimonial.spec.suspend }}
  schedule: {{ .Values.inchoo.testimonial.spec.schedule }}
  concurrencyPolicy: {{ .Values.inchoo.testimonial.spec.concurrencyPolicy }}
  successfulJobsHistoryLimit: {{ .Values.inchoo.testimonial.spec.successfulJobsHistoryLimit }}
  startingDeadlineSeconds: {{ .Values.inchoo.testimonial.spec.startingDeadlineSeconds }}
  jobTemplate:
    spec:
      template:
        metadata:
          name: {{ .Values.inchoo.testimonial.name }}
        spec:
          restartPolicy: OnFailure
          volumes:
            - name: keh-m2-media
              persistentVolumeClaim:
                claimName: {{ .Values.magento.persistence.volumeName }}-pvc
            - name: varlog
              emptyDir: {}
          containers:
            - name: keh-m2-testimonial-fetch
              image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag}}
              imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
              command: [ "/bin/bash", "-c" ]
              args:
                - |
                  trap "touch /tmp/pod/file.unlock" EXIT
                  /mount.sh && \
                  php -d memory_limit=-1 bin/magento inchoo:testimonial:fetch
              resources:
                limits:
                  cpu: {{ .Values.inchoo.klevu_sell_feed.spec.resources.limits.cpu | default "500m" | quote  }}
                  memory: {{ .Values.inchoo.klevu_sell_feed.spec.resources.limits.memory | default "512Mi" | quote  }}
                requests:
                  cpu: {{ .Values.inchoo.klevu_sell_feed.spec.resources.requests.cpu | default "300m" | quote  }}
                  memory: {{ .Values.inchoo.klevu_sell_feed.spec.resources.requests.memory | default "256Mi" | quote  }}
              volumeMounts:
                - name: keh-m2-media
                  mountPath: /var/www/sites/keh.com/html/pub/media
                  subPath: ./kepler-1649c/magento/export/media/
                - name: varlog
                  mountPath: /var/log/magento
#            - name: keh-m2-testimonial-fetch-logger
#              image: 'us-central1-docker.pkg.dev/keh-development/keh-magento/keh-web-monitor:latest'
#              command: [ "/var/lib/monitor/monitor.bash" ]
#              args:
#                - /var/log/magento/
#                - /tmp/pod/file.unlock
#              resources:
#                limits:
#                  cpu: {{ .Values.inchoo.testimonial.logger.spec.resources.limits.cpu | default "200m" | quote  }}
#                  memory: {{ .Values.inchoo.testimonial.logger.spec.resources.limits.memory | default "128Mi" | quote  }}
#                requests:
#                  cpu: {{ .Values.inchoo.testimonial.logger.spec.resources.requests.cpu | default "100m" | quote  }}
#                  memory: {{ .Values.inchoo.testimonial.logger.spec.resources.requests.memory | default "15Mi" | quote }}
#              volumeMounts:
#                - name: keh-m2-media
#                  mountPath: /var/www/sites/keh.com/html/pub/media
#                - name: varlog
#                  mountPath: /var/www/sites/keh.com/files/html/var/log
