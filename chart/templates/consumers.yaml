apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: {{ .Values.global.namePrefix }}-web-consumer
  namespace: {{ .Values.global.namespace }}
  labels:
    app: {{ .Values.global.namePrefix }}-web-consumer
    environment: {{ .Values.global.environment }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.global.namePrefix }}-web-consumer
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.global.namePrefix }}-web-consumer
        environment: {{ .Values.global.environment }}
    spec:
      containers:
        - name: "keh-web-consumer"
          image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag }}
          imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
          command: [ "bin/magento", "queue:consumers:start", "--batch-size=1", "--max-messages=512", "async.operations.all" ]
          resources:
            limits:
              cpu: "100m"
              memory: "300Mi"
            requests:
              cpu: "50m"
              memory: "256Mi"
          envFrom:
            - configMapRef:
                name: env-config
          env:
            - name: REDIS_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: memorystore-instance-credentials
                  key: endpoint
            - name: SQL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: endpoint
            - name: SQL_USER
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: username
            - name: SQL_USER_PASS
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: password
            - name: RABBITMQ_PASS
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-password
            - name: RABBITMQ_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-endpoint
      nodeSelector:
  {{ toYaml .Values.nodeSelector | indent 8 }}
---
apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: {{ .Values.global.namePrefix }}-code-generator
  namespace: {{ .Values.global.namespace }}
  labels:
    app: {{ .Values.global.namePrefix }}-code-generator
    environment: {{ .Values.global.environment }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.global.namePrefix }}-code-generator
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.global.namePrefix }}-code-generator
        environment: {{ .Values.global.environment }}
    spec:
      containers:
        - name: "keh-web-code-generator"
          image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag }}
          imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
          command: [ "bin/magento", "queue:consumers:start", "--batch-size=1", "--max-messages=512", "codegeneratorProcessor" ]
          resources:
            limits:
              cpu: "100m"
              memory: "300Mi"
            requests:
              cpu: "50m"
              memory: "200Mi"
          envFrom:
            - configMapRef:
                name: env-config
          env:
            - name: REDIS_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: memorystore-instance-credentials
                  key: endpoint
            - name: SQL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: endpoint
            - name: SQL_USER
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: username
            - name: SQL_USER_PASS
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: password
            - name: RABBITMQ_PASS
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-password
            - name: RABBITMQ_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-endpoint
      nodeSelector:
  {{ toYaml .Values.nodeSelector | indent 8 }}
---
apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: {{ .Values.global.namePrefix }}-product-action-att-upd
  namespace: {{ .Values.global.namespace }}
  labels:
    app: {{ .Values.global.namePrefix }}-product-action-att-upd
    environment: {{ .Values.global.environment }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.global.namePrefix }}-product-action-att-upd
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.global.namePrefix }}-product-action-att-upd
        environment: {{ .Values.global.environment }}
    spec:
      containers:
        - name: "keh-web-product-action-attribute-update"
          image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag }}
          imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
          command: [ "bin/magento", "queue:consumers:start", "--batch-size=1", "--max-messages=512", "product_action_attribute.update" ]
          resources:
            limits:
              cpu: "100m"
              memory: "300Mi"
            requests:
              cpu: "50m"
              memory: "200Mi"
          envFrom:
            - configMapRef:
                name: env-config
          env:
            - name: REDIS_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: memorystore-instance-credentials
                  key: endpoint
            - name: SQL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: endpoint
            - name: SQL_USER
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: username
            - name: SQL_USER_PASS
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: password
            - name: RABBITMQ_PASS
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-password
            - name: RABBITMQ_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-endpoint
      nodeSelector:
  {{ toYaml .Values.nodeSelector | indent 8 }}
---
apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: {{ .Values.global.namePrefix }}-keh-meta-event
  namespace: {{ .Values.global.namespace }}
  labels:
    app: {{ .Values.global.namePrefix }}-keh-meta-event
    environment: {{ .Values.global.environment }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.global.namePrefix }}-keh-meta-event
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.global.namePrefix }}-keh-meta-event
        environment: {{ .Values.global.environment }}
    spec:
      containers:
        - name: "keh-meta-event"
          image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag }}
          imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
          command: [ "bin/magento", "queue:consumers:start", "--batch-size=100", "keh.meta.event" ]
          resources:
            limits:
              cpu: "300m"
              memory: "300Mi"
            requests:
              cpu: "200m"
              memory: "200Mi"
          envFrom:
            - configMapRef:
                name: env-config
          env:
            - name: REDIS_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: memorystore-instance-credentials
                  key: endpoint
            - name: SQL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: endpoint
            - name: SQL_USER
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: username
            - name: SQL_USER_PASS
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: password
            - name: RABBITMQ_PASS
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-password
            - name: RABBITMQ_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-endpoint
      nodeSelector:
  {{ toYaml .Values.nodeSelector | indent 8 }}
---

apiVersion: "apps/v1"
kind: "Deployment"
metadata:
  name: {{ .Values.global.namePrefix }}-bloomreach-engagement
  namespace: {{ .Values.global.namespace }}
  labels:
    app: {{ .Values.global.namePrefix }}-bloomreach-engagement
    environment: {{ .Values.global.environment }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.global.namePrefix }}-bloomreach-engagement
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: {{ .Values.global.namePrefix }}-bloomreach-engagement
        environment: {{ .Values.global.environment }}
    spec:
      containers:
        - name: "bloomreach-engagement"
          image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag }}
          imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
          command: [ "bin/magento", "queue:consumers:start", "--batch-size=100", "--max-messages=512", "bloomreach_engagement_events_rabbitmq_queue" ]
          resources:
            limits:
              cpu: "300m"
              memory: "1500Mi"
            requests:
              cpu: "200m"
              memory: "1000Mi"
          envFrom:
            - configMapRef:
                name: env-config
          env:
            - name: REDIS_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: memorystore-instance-credentials
                  key: endpoint
            - name: SQL_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: endpoint
            - name: SQL_USER
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: username
            - name: SQL_USER_PASS
              valueFrom:
                secretKeyRef:
                  name: cloudsql-instance-credentials
                  key: password
            - name: RABBITMQ_PASS
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-password
            - name: RABBITMQ_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-secret
                  key: rabbitmq-endpoint
      nodeSelector:
  {{ toYaml .Values.nodeSelector | indent 8 }}

---
#apiVersion: "apps/v1"
#kind: "Deployment"
#metadata:
#  name: {{ .Values.global.namePrefix }}-match-customer-seg-prc
#  namespace: {{ .Values.global.namespace }}
#  labels:
#    app: {{ .Values.global.namePrefix }}-match-customer-seg-prc
#    environment: {{ .Values.global.environment }}
#spec:
#  replicas: 1
#  selector:
#    matchLabels:
#      app: {{ .Values.global.namePrefix }}-match-customer-seg-prc
#  strategy:
#    type: Recreate
#  template:
#    metadata:
#      labels:
#        app: {{ .Values.global.namePrefix }}-match-customer-seg-prc
#        environment: {{ .Values.global.environment }}
#    spec:
#      containers:
#        - name: "keh-web-match-customer-segment-processor"
#          image: {{ .Values.deployment.image }}:{{ .Values.deployment.tag }}
#          imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
#          command: [ "bin/magento", "queue:consumers:start", "--batch-size=1", "--max-messages=512", "matchCustomerSegmentProcessor" ]
#          resources:
#            limits:
#              cpu: 100m
#              memory: 200Mi
#            requests:
#              cpu: 50m
#              memory: 100Mi
#          env:
#            - name: REDIS_ENDPOINT
#              valueFrom:
#                secretKeyRef:
#                  name: memorystore-instance-credentials
#                  key: endpoint
#            - name: SQL_ENDPOINT
#              valueFrom:
#                secretKeyRef:
#                  name: cloudsql-instance-credentials
#                  key: endpoint
#            - name: SQL_USER
#              valueFrom:
#                secretKeyRef:
#                  name: cloudsql-instance-credentials
#                  key: username
#            - name: SQL_USER_PASS
#              valueFrom:
#                secretKeyRef:
#                  name: cloudsql-instance-credentials
#                  key: password
#            - name: RABBITMQ_PASS
#              valueFrom:
#                secretKeyRef:
#                  name: rabbitmq-secret
#                  key: rabbitmq-password
