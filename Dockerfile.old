ARG BASE_IMAGE="gcr.io/keh-internal-development/keh-web/base_image:0367fc47696d81d5db550bf1d6e9631c5c332438"
FROM ${BASE_IMAGE}

ENV INSTALL_DIR=/var/www/sites/keh.com/files/html
WORKDIR $INSTALL_DIR

ARG ENV
ARG SELFCERT_C="US"
ARG SELFCERT_ST="GA"
ARG SELFCERT_O="KEH, Inc."
ARG SELFCERT_CN="localhost"
ARG SELFCERT_SAN="DNS:*.com"
ARG SHORT_SHA
# [szymon] I don't know why this is needed
RUN openssl req -x509 \
	-nodes \
	-days 90 \
	-subj "/C=${SELFCERT_C}/ST=${SELFCERT_ST}/O=${SELFCERT_O}/CN=${SELFCERT_CN}" \
	-addext "subjectAltName=${SELFCERT_SAN}" \
	-newkey rsa:2048 \
	-keyout /etc/ssl/private/nginx-selfsigned.key \
	-out /etc/ssl/certs/nginx-selfsigned.crt
# some socket hacks and global conf files movement
RUN mkdir /run/php && \
    touch /run/php/keh.sock && \
    chown www-data:www-data /run/php/keh.sock /var/www . && \
    mv /usr/local/etc/php-fpm.d/www.conf /usr/local/etc/php-fpm.d/www.conf.original && \
    mv /usr/local/etc/php-fpm.d/zz-docker.conf /usr/local/etc/php-fpm.d/zz-docker.conf.original
COPY ./etc/php-fpm/. /usr/local/etc/php-fpm.d/
COPY ./etc/php/. /usr/local/etc/php/conf.d/
COPY ./etc/nginx/site.conf /etc/nginx/sites-enabled/default
# copying EVERYTHING
COPY --chown=www-data:www-data --chmod=777 . .
RUN cp etc/config.php app/etc/config.php
RUN cp etc/env.php app/etc/env.php

RUN echo "${SHORT_SHA}" > $INSTALL_DIR/gcpbuild.txt
RUN mkdir -p /var/www/sites/keh.com/logs/php-fpm /var/www/sites/keh.com/logs/nginx/
# this can be moved to base_image after
RUN composer install --ignore-platform-reqs --prefer-dist --ansi --no-dev --no-interaction
# preparing config for bin/magento setup:di:compile
RUN cp etc/env.php app/etc/build.env.php
RUN cp etc/build.env.php app/etc/env.php
# call bin/magento setup:di:compile
RUN php -dmemory_limit=5G bin/magento setup:di:compile
# cleanup config after bin/magento setup:di:compile
RUN cp etc/env.php app/etc/env.php
# copy startup scripts
RUN cp build_magento.sh start_services.sh mount.sh /
# set cmd
CMD /start_services.sh
# expose
EXPOSE 80
EXPOSE 443
EXPOSE 9000
