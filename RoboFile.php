<?php
/**
 * This is project's console commands configuration for Robo task runner.
 *
 * @see http://robo.li/
 */
class RoboFile extends \Robo\Tasks
{
    protected $envSettigns = [
        'stage' => [
            'user' => 'robo-stage',
            'domain' => 'robo.stage'
        ],
        'live' => [
            'user' => 'robo-live',
            'domain' => 'robo.live'
        ]
    ];

    protected $selectedEnv = 'stage';

    /**
     * @var string Path to PHP binary
     */
    protected $phpBin;

    /**
     * @var string
     */
    protected $releaseFile;

    /**
     * @var string Name of magento command
     */
    protected $magentoBin = 'bin/magento';

    /**
     * @var string[]
     */
    protected $releaseFolders = [
        'app/code',
        'app/design',
        'bin',
        'generated',
        'config',
        'lib',
        'pub/errors',
        'pub/opt',
        'pub/static',
        'pub/us_en',
        'pub/sitemaps',
        'pub/feeds',
        'var/di',
        'var/generation',
        'var/view_preprocessed',
        'setup',
        'update',
        'vendor',
        'data-migration'
    ];

    /**
     * @var string[]
     */
    protected $releaseFiles = [
        '.htaccess',
        'index.php',
        'pub/cron.php',
        'pub/get.php',
        'pub/index.php',
        'pub/static.php',
        'pub/health_check.php',
        'pub/.htaccess',
        'composer.lock',
        'composer.json',
        'app/.htaccess',
        'app/autoload.php',
        'app/bootstrap.php',
        'app/functions.php',
        'app/etc/NonComposerComponentRegistration.php',
        'app/etc/config.php',
        'app/etc/db_schema.xml',
        'app/etc/di.xml',
        'app/etc/registration_globlist.php',
        'app/etc/vendor_path.php',
        'production-db-update.magerun',
        'staging-db-update.magerun',
        'n98-magerun2.phar'
    ];

    /**
     * RoboFile constructor.
     */
    public function __construct()
    {
        $this->phpBin = PHP_BINARY;
        $this->stopOnFail(true);
    }

    /**
     * Switches to custom environment
     *
     * @param $env
     * @throws Exception
     */
    protected function setEnv($env)
    {
        if (isset($this->envSettigns[$env]) == false) {
            throw new \Exception('Unconfigured environment!');
        }

        if ($env === 'live' && $this->confirm('Use production environemnt?') !== true) {
            $this->say("Exiting...");
            exit();
        }

        $this->say("Using {$env} environemnt.");
        $this->selectedEnv = $env;
    }

    /**
     * Returns SSH user for deployment
     * @return mixed
     */
    protected function getEnvUser()
    {
        return $this->envSettigns[$this->selectedEnv]['user'];
    }

    /**
     * Returns SSH hostname for deployment
     *
     * @return string
     */
    protected function getEnvHostname()
    {
        return $this->envSettigns[$this->selectedEnv]['domain'];
    }

    /**
     * Pushes file to remote server
     *
     * @param string $file Build artifact to push
     * @param array $opts Environment to which deployment should be done
     * @throws Exception
     */
    public function releasePush($file, $opts = ['env' => 'stage'])
    {
        $this->setEnv($opts['env']);

        $cmd = 'echo ' . $file;
        $this->_exec($cmd);
    }

    /**
     * Performs code compilation and collects the build artifact
     */
    public function buildRelease()
    {
        // Prepare Magento
        $this->say('Cleaning up files');
        $this->_deleteDir([
            'vendor',
            'var/cache',
            'var/page_cache',
            'var/view_preprocessed',
            'generated',
            'generated',
            'pub/static/adminhtml',
            'pub/static/frontend',
        ]);
        $this->_exec('rm -f pub/static/deployed_version.txt');

        $this->say('Initializing composer');
        $this->_exec('composer install --no-ansi --optimize-autoloader --no-dev --ignore-platform-reqs');

        $this->say('Compiling DI');
        $this->binMagento('setup:di:compile', '--no-ansi');

        $this->say('Compiling static assets');
        $this->binMagento('setup:static-content:deploy', '--no-ansi en_US -f -j 8');
    }

    /**
     * Collects release artifact
     */
    public function collectArtifact()
    {
        $this->say('Collecting build artifact');
        $releaseName = $this->getReleaseName();

        $arhive = $this->taskPack($releaseName);

        // Add folders to release
        foreach ($this->releaseFolders as $folder) {
            $arhive->add($folder);
        }


        // Add files to release
        foreach ($this->releaseFiles as $file) {
            $arhive->add($file);
        }
        $arhive->run();

        $this->say('Compressing artifact');
        $this->_exec('gzip ' . $releaseName);
    }

    /**
     * Wrapper for bin/magento execution
     *
     * @param string $command Command to execute
     * @param bool|string $args Additional parameters
     */
    private function binMagento($command, $args = false)
    {
        $cmd = $this->phpBin . ' ' . $this->magentoBin . ' ';
        $cmd .= $command;

        if ($args !== false) {
            $cmd .= ' ' . $args;
        }

        $this->_exec($cmd);
    }

    /**
     * Generates release archive name. If such already exists, deletes it
     *
     * @return string
     * @throws Exception
     */
    protected function getReleaseName()
    {
        $this->releaseFile = 'artifact.tar';

        if (file_exists($this->releaseFile) == true) {
            $this->_exec('rm ' . $this->releaseFile);
        }

        return $this->releaseFile;
    }
}
