steps:
# Access id_github file from secret manager
- name: gcr.io/cloud-builders/gcloud
  entrypoint: 'bash'
  args: [ '-c', 'gcloud secrets versions access latest --secret=github-key > /root/.ssh/id_github' ]
  volumes:
  - name: 'ssh'
    path: /root/.ssh
# Set up git with key and domain
- name: 'gcr.io/cloud-builders/git'
  entrypoint: 'bash'
  args:
  - '-c'
  - |
    chmod 600 /root/.ssh/id_github
    cat <<EOF >/root/.ssh/config
    Hostname github.com
    IdentityFile /root/.ssh/id_github
    EOF
    mv known_hosts.github /root/.ssh/known_hosts
  volumes:
  - name: 'ssh'
    path: /root/.ssh
# Clone the www.keh.com-gke repo
- name: 'gcr.io/cloud-builders/gcloud'
  id: Clone www.keh.com-gke repository
  entrypoint: /bin/sh
  args:
  - '-c'
  - |
    <NAME_EMAIL>:KEHCamera/www.keh.com-gke.git && \
    cd www.keh.com-gke && \
    git checkout build-image && \
    git config user.email $(gcloud auth list --filter=status:ACTIVE --format='value(account)')
  volumes:
  - name: 'ssh'
    path: /root/.ssh
# Apply change to repo for trigger
- name: 'gcr.io/cloud-builders/gcloud'
  id: apply change for activitating trigger
  entrypoint: /bin/sh
  args:
  - '-c'
  - |
     cd www.keh.com-gke && \
     echo ${SHORT_SHA} > gcpbuild.txt
# Push to build-image repo
- name: 'gcr.io/cloud-builders/gcloud'
  id: Push to build-image repo
  entrypoint: /bin/sh
  args:
  - '-c'
  - |
    set -x && \
    cd www.keh.com-gke && \
    git add gcpbuild.txt && \
    git commit -m "Deploying image gcr.io/keh-web-shop/keh-web-image:${SHORT_SHA}
    Built from commit ${COMMIT_SHA} of repository www.keh.com-gke
    Author: $(git log --format='%an <%ae>' -n 1 HEAD)" && \
    git push origin build-image
  volumes:
  - name: 'ssh'
    path: /root/.ssh
timeout: "1800s"
options:
 machineType: 'N1_HIGHCPU_8'
tags: ['web-builds']