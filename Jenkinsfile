#!/usr/bin/env groovy
import groovy.json.JsonOutput


pipeline {
    agent {
        node {
            label 'shadow-master' // Only one with ssh access to required servers
        }
    }

    options {
        buildDiscarder(
            logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '', numToKeepStr: '10')
        )
    }

    stages {
        stage('Static code test') {
            steps {
                sh '''
                    mkdir -p app/code/Inchoo;
                    touch app/code/Inchoo/temp.php
                    mkdir -p results;
                    docker run --rm --volume `pwd`:/project udovicic/magentost:3.1 \
                        -s --extensions=php,phtml --report=junit --standard=Magento2 \
                        app/code/Inchoo > results/static.xml
                '''
                sh '''
                    mkdir -p app/design/frontend/Inchoo;
                    mkdir -p results;
                    docker run --rm --volume `pwd`:/project udovicic/magentost:3.1 \
                        -s --extensions=php,phtml --report=junit --standard=Magento2 \
                        app/design/frontend/Inchoo > results/static.xml
                '''
            }

            post {
                always {
                    junit 'results/*.xml'
                }

                success {
                    slackBuildNotify("Build succeeded", "good")
                }
            }
        }
        
        stage('Deployment to Google Cloud staging') {
            when {
                not {
                     anyOf {
                        changeRequest()
                        branch 'master'
                    }
                }
            }

            steps {
                script {
                    def proceed = true

                    try {
                        timeout(time: 1, unit: 'MINUTES') {
                            env.DEPLOY_TARGET = input message: 'Deploy to Google Cloud staging server?',
                                parameters: [choice(name: 'SERVER', choices: ['mercury', 'ross', 'kepler', 'sirius', 'orion', 'titan'],description: 'Choose which server')]
                        }
                    } catch (err) {
                        proceed = false
                    }

                    if(proceed) {
                        echo "Deployment to server: ${env.DEPLOY_TARGET}"
                        sh "/bin/bash bin/deploy.sh ${env.DEPLOY_TARGET}"
                    }
                }
            }
        }

        stage('Deployment to production server') {
            when {
                branch 'master'
            }

            steps {
                script {
                    sh '/bin/bash bin/deploy.sh production'
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }

        failure {
            slackBuildNotify("Build failed", "danger")
        }
    }
}

void slackBuildNotify( title, color) {
    def gitChanges = []
    currentBuild.changeSets.each { set ->
        set.each { entry ->
            truncated_msg = entry.msg.take(100)
            gitChanges.add("_${truncated_msg}_ by ${entry.author.fullName}")
        }
    }
    def attachments = JsonOutput.toJson([
        [
            "fallback": "${title}: ${env.JOB_NAME} ${env.BUILD_DISPLAY_NAME}",
            "color": "${color}",
            "title": "${title}",
            "text": gitChanges.join("\n"),
            "mrkdwn_in": [
                "text"
            ],
            "fields": [
                [
                    "title": "Branch",
                    "value": "${env.BRANCH_NAME}",
                    "short": true
                ],
                [
                    "title": "Build",
                    "value": "${env.JOB_NAME} ${env.BUILD_DISPLAY_NAME}",
                    "short": true
                ]
            ],
            "actions": [
                [
                    "type": "button",
                    "text": "Details/Deployment",
                    "url": "${env.RUN_DISPLAY_URL}"
                ]
            ]
        ]
    ])
    slackSend (botUser: false,
        channel: 'devops-keh-m2',
        color: "${color}",
        message: "${title}",
        attachments: "${attachments}",
        teamDomain: 'inchoo',
        tokenCredentialId: 'inchoo-slack-token'
    )
}
