<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="../../config.xsd">
    <steps mode="settings">
        <step title="Settings Step">
            <integrity>Migration\Step\Settings\Integrity</integrity>
            <data>Migration\Step\Settings\Data</data>
        </step>
        <step title="Stores Step">
            <integrity>Migration\Step\Stores\Integrity</integrity>
            <data>Migration\Step\Stores\Data</data>
            <volume>Migration\Step\Stores\Volume</volume>
        </step>
    </steps>
    <steps mode="data">
<!--        <step title="EAV Step">-->
<!--            <integrity>Migration\Step\Eav\Integrity</integrity>-->
<!--            <data>Migration\Step\Eav\Data</data>-->
<!--            <volume>Migration\Step\Eav\Volume</volume>-->
<!--        </step>-->
        <step title="Custom Customer Attributes Step">
            <integrity>Migration\Step\CustomCustomerAttributes\Integrity</integrity>
            <data>Migration\Step\CustomCustomerAttributes\Data</data>
            <volume>Migration\Step\CustomCustomerAttributes\Volume</volume>
        </step>
        <step title="Customer Attributes Step">
            <integrity>Migration\Step\Customer\Integrity</integrity>
            <data>Migration\Step\Customer\Data</data>
            <volume>Migration\Step\Customer\Volume</volume>
        </step>
        <step title="Map Step">
            <integrity>Migration\Step\Map\Integrity</integrity>
            <data>Migration\Step\Map\Data</data>
            <volume>Migration\Step\Map\Volume</volume>
        </step>
<!--        <step title="Url Rewrite Step">-->
<!--            <integrity>Migration\Step\UrlRewrite\Version11410to2000</integrity>-->
<!--            <data>Migration\Step\UrlRewrite\Version11410to2000</data>-->
<!--            <volume>Migration\Step\UrlRewrite\Version11410to2000</volume>-->
<!--        </step>-->
        <step title="SalesOrder Step">
            <integrity>Migration\Step\SalesOrder\Integrity</integrity>
            <data>Migration\Step\SalesOrder\Data</data>
            <volume>Migration\Step\SalesOrder\Volume</volume>
        </step>
        <step title="OrderGrids Step">
            <integrity>Migration\Step\OrderGridsEE\Integrity</integrity>
            <data>Migration\Step\OrderGridsEE\Data</data>
            <volume>Migration\Step\OrderGridsEE\Volume</volume>
        </step>
        <step title="SalesIncrement Step">
            <integrity>Migration\Step\SalesIncrement\Integrity</integrity>
            <data>Migration\Step\SalesIncrement\Data</data>
            <volume>Migration\Step\SalesIncrement\Volume</volume>
        </step>
    </steps>
    <steps mode="delta">
        <step title="Custom Customer Attributes Step">
            <delta>Migration\Step\CustomCustomerAttributes\Delta</delta>
            <volume>Migration\Step\CustomCustomerAttributes\Volume</volume>
        </step>
        <step title="Customer Attributes Step">
            <delta>Migration\Step\Customer\Delta</delta>
            <volume>Migration\Step\Customer\Volume</volume>
        </step>
        <step title="Map Step">
            <delta>Migration\Step\Map\Delta</delta>
            <volume>Migration\Step\Map\Volume</volume>
        </step>
        <step title="SalesOrder Step">
            <delta>Migration\Step\SalesOrder\Delta</delta>
            <volume>Migration\Step\SalesOrder\Volume</volume>
        </step>
        <step title="OrderGrids Step">
            <delta>Migration\Step\OrderGridsEE\Delta</delta>
            <volume>Migration\Step\OrderGridsEE\Volume</volume>
        </step>
        <step title="SalesIncrement Step">
            <delta>Migration\Step\SalesIncrement\Delta</delta>
            <volume>Migration\Step\SalesIncrement\Volume</volume>
        </step>
    </steps>
    <source>
        <database host="db" name="kehm1" user="inchoo" password="inchoo"/>
    </source>
    <destination>
        <database host="db" name="kehm2-sanitized" user="inchoo" password="inchoo"/>
    </destination>
    <options>
        <map_file>../../../migration/inchoo/map-sanitized.xml</map_file>
        <eav_map_file>etc/commerce-to-commerce/map-eav.xml.dist</eav_map_file>
        <eav_document_groups_file>etc/commerce-to-commerce/eav-document-groups.xml.dist</eav_document_groups_file>
        <eav_attribute_groups_file>etc/commerce-to-commerce/eav-attribute-groups.xml.dist</eav_attribute_groups_file>
        <!--log_map_file>etc/commerce-to-commerce/map-log.xml.dist</log_map_file>
        <log_document_groups_file>etc/commerce-to-commerce/log-document-groups.xml.dist</log_document_groups_file-->
        <settings_map_file>etc/commerce-to-commerce/settings.xml.dist</settings_map_file>
        <customer_map_file>../../../migration/inchoo/map-customer.xml</customer_map_file>
        <customer_document_groups_file>etc/commerce-to-commerce/customer-document-groups.xml.dist</customer_document_groups_file>
        <customer_attribute_groups_file>etc/commerce-to-commerce/customer-attribute-groups.xml.dist</customer_attribute_groups_file>
        <sales_order_map_file>../../../migration/inchoo/map-sales.xml</sales_order_map_file>
        <order_grids_document_groups_file>etc/commerce-to-commerce/order-grids-document-groups.xml.dist</order_grids_document_groups_file>
        <delta_document_groups_file>../../../migration/inchoo/deltalog.xml</delta_document_groups_file>
        <customer_attr_map_file>etc/commerce-to-commerce/customer-attr-map.xml.dist</customer_attr_map_file>
        <customer_attr_document_groups_file>etc/commerce-to-commerce/customer-attr-document-groups.xml.dist</customer_attr_document_groups_file>
        <class_map>etc/commerce-to-commerce/class-map.xml.dist</class_map>
        <!--visual_merchandiser_map>etc/commerce-to-commerce/visual_merchandiser_map.xml.dist</visual_merchandiser_map>
        <visual_merchandiser_document_groups>etc/commerce-to-commerce/visual_merchandiser_document_groups.xml.dist</visual_merchandiser_document_groups>
        <visual_merchandiser_attribute_groups>etc/commerce-to-commerce/visual_merchandiser_attribute_groups.xml.dist</visual_merchandiser_attribute_groups>
        <map_document_groups>etc/commerce-to-commerce/map-document-groups.xml.dist</map_document_groups>
        <tier_price_map_file>etc/commerce-to-commerce/1.14.4.1/map-tier-price.xml.dist</tier_price_map_file-->
        <stores_map_file>etc/commerce-to-commerce/map-stores.xml.dist</stores_map_file>
        <!--
        In case bulk_size=0 it will be auto-detected for every document.
        -->
        <bulk_size>0</bulk_size>
        <!--
        Set direct_document_copy = 1 for better performance.
        NOTE: 'source' and 'destination' databases MUST be placed on the same MySQL instance
        and 'destination' user MUST be granted with 'SELECT' permissions on 'source' database
         -->
        <direct_document_copy>0</direct_document_copy>
        <source_prefix />
        <dest_prefix />
        <auto_resolve_urlrewrite_duplicates>0</auto_resolve_urlrewrite_duplicates>
        <log_file>migration.log</log_file>
        <progress_bar_format>%percent%% [%bar%] Remaining Time: %remaining%</progress_bar_format>
        <upgrade_customer_password_hash>1</upgrade_customer_password_hash>
        <edition_migrate>commerce-to-commerce</edition_migrate>
        <edition_number>1.14.4.1</edition_number>
        <init_statements_source>SET NAMES utf8;</init_statements_source>
        <init_statements_destination>SET NAMES utf8;</init_statements_destination>
        <crypt_key>832d68518cbe7e680ce7181689bc5b19</crypt_key>
    </options>
</config>
