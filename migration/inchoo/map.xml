<?xml version="1.0" encoding="UTF-8"?>
<map xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="../../map.xsd">
    <source>
        <document_rules>
            <!-- INCHOO: additional ignored tables -->
            <ignore>
                <document>catalog_product_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_product</document>
            </ignore>
            <ignore>
                <document>cataloginventory_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_index</document>
            </ignore>
            <ignore>
                <document>catalog_category_product</document>
            </ignore>
            <ignore>
                <document>adminnotification_inbox</document>
            </ignore>
            <ignore>
                <document>catalogrule_*</document>
            </ignore>
            <ignore>
                <document>catalogsearch_*</document>
            </ignore>
            <ignore>
                <document>checkout_*</document>
            </ignore>
            <!--ignore>
                <document>cms_*</document>
            </ignore-->
            <ignore>
                <document>core_*</document>
            </ignore>
            <ignore>
                <document>cron_schedule</document>
            </ignore>
            <ignore>
                <document>dataflow_*</document>
            </ignore>
            <ignore>
                <document>design_change</document>
            </ignore>
            <ignore>
                <document>downloadable_*</document>
            </ignore>
            <ignore>
                <document>enterprise_customersegment_*</document>
            </ignore>
            <ignore>
                <document>rating</document>
            </ignore>
            <ignore>
                <document>rating_*</document>
            </ignore>
            <ignore>
                <document>review</document>
            </ignore>
            <ignore>
                <document>review_*</document>
            </ignore>
            <ignore>
                <document>enterprise_logging_*</document>
            </ignore>
            <ignore>
                <document>enterprise_reminder_*</document>
            </ignore>
            <ignore>
                <document>sales_bestsellers_*</document>
            </ignore>
            <ignore>
                <document>report_*</document>
            </ignore>
            <ignore>
                <document>product_alert_stock</document>
            </ignore>
            <ignore>
                <document>product_alert_price</document>
            </ignore>
            <ignore>
                <document>newsletter_*</document>
            </ignore>
            <ignore>
                <document>enterprise_rma</document>
            </ignore>
            <ignore>
                <document>enterprise_rma_*</document>
            </ignore>
            <ignore>
                <document>am_meta_config</document>
            </ignore>
            <ignore>
                <document>am_meta_config_backup</document>
            </ignore>
            <ignore>
                <document>am_seo_sitemap</document>
            </ignore>
            <ignore>
                <document>am_shopby_filter</document>
            </ignore>
            <ignore>
                <document>am_shopby_range</document>
            </ignore>
            <ignore>
                <document>am_shopby_value</document>
            </ignore>
            <ignore>
                <document>am_shopby_value_link</document>
            </ignore>
            <ignore>
                <document>bronto_*</document>
            </ignore>
            <ignore>
                <document>anowave_*</document>
            </ignore>
            <ignore>
                <document>xplenty_*</document>
            </ignore>
            <ignore>
                <document>klevu_*</document>
            </ignore>
            <ignore>
                <document>aw_*</document>
            </ignore>
            <ignore>
                <document>smtppro_email_log</document>
            </ignore>
            <ignore>
                <document>bitpay_*</document>
            </ignore>
            <ignore>
                <document>inchoo_cybersource_signifyd</document>
            </ignore>
            <ignore>
                <document>inchoo_event</document>
            </ignore>
            <ignore>
                <document>inchoo_event_ticket</document>
            </ignore>
            <ignore>
                <document>inchoo_keh_stores</document>
            </ignore>
            <ignore>
                <document>inchoo_productribbon_strikethrough</document>
            </ignore>
            <ignore>
                <document>emarsys_*</document>
            </ignore>
            <ignore>
                <document>cryozonic_stripesubscriptions_customers</document>
            </ignore>
            <ignore>
                <document>rw_*</document>
            </ignore>
            <ignore>
                <document>tax_nexus</document>
            </ignore>
            <ignore>
                <document>tieredcoupon</document>
            </ignore>
            <ignore>
                <document>tieredcoupon_coupon</document>
            </ignore>
            <ignore>
                <document>wordpress_*</document>
            </ignore>
            <ignore>
                <document>inchoo_signifyd_device_fingerprint</document>
            </ignore>
            <ignore>
                <document>klevu_order_sync</document>
            </ignore>
            <ignore>
                <document>klevu_product_sync</document>
            </ignore>
            <!-- INCHOO END:  -->
            <ignore>
                <document>googleshopping_attributes</document>
            </ignore>
            <ignore>
                <document>googleshopping_items</document>
            </ignore>
            <ignore>
                <document>googleshopping_types</document>
            </ignore>
            <ignore>
                <document>enterprise_sales_creditmemo_grid_archive</document>
            </ignore>
            <ignore>
                <document>enterprise_sales_invoice_grid_archive</document>
            </ignore>
            <ignore>
                <document>enterprise_sales_order_grid_archive</document>
            </ignore>
            <ignore>
                <document>enterprise_sales_shipment_grid_archive</document>
            </ignore>
            <ignore>
                <document>sales_flat_creditmemo_grid</document>
            </ignore>
            <ignore>
                <document>sales_flat_invoice_grid</document>
            </ignore>
            <ignore>
                <document>sales_flat_order_grid</document>
            </ignore>
            <ignore>
                <document>sales_flat_shipment_grid</document>
            </ignore>
            <ignore>
                <document>customer_entity</document>
            </ignore>
            <ignore>
                <document>customer_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_entity_varchar</document>
            </ignore>
            <ignore>
                <document>customer_address_entity</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_varchar</document>
            </ignore>
            <ignore>
                <document>m2_cl_*</document>
            </ignore>
            <ignore>
                <document>url_rewrite_m2*</document>
            </ignore>
            <ignore>
                <document>admin_assert</document>
            </ignore>
            <ignore>
                <document>api2_acl_attribute</document>
            </ignore>
            <ignore>
                <document>api2_acl_role</document>
            </ignore>
            <ignore>
                <document>api2_acl_rule</document>
            </ignore>
            <ignore>
                <document>api2_acl_user</document>
            </ignore>
            <ignore>
                <document>api_assert</document>
            </ignore>
            <ignore>
                <document>api_role</document>
            </ignore>
            <ignore>
                <document>api_rule</document>
            </ignore>
            <ignore>
                <document>api_session</document>
            </ignore>
            <ignore>
                <document>api_user</document>
            </ignore>
            <ignore>
                <document>log_customer</document>
            </ignore>
            <ignore>
                <document>log_quote</document>
            </ignore>
            <ignore>
                <document>log_summary_type</document>
            </ignore>
            <ignore>
                <document>log_summary</document>
            </ignore>
            <ignore>
                <document>log_url</document>
            </ignore>
            <ignore>
                <document>log_url_info</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_categs_index_idx</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_categs_index_tmp</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_products_index_idx</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_products_index_tmp</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_product_enabled_index</document>
            </ignore>
            <ignore>
                <document>catalog_product_flat_cl</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_idx</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_tmp</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_cat_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_bundle_price_index</document>
            </ignore>
            <ignore>
                <document>catalog_product_bundle_stock_index</document>
            </ignore>
            <ignore>
                <document>catalogindex_aggregation</document>
            </ignore>
            <ignore>
                <document>catalogindex_aggregation_tag</document>
            </ignore>
            <ignore>
                <document>catalogindex_aggregation_to_tag</document>
            </ignore>
            <ignore>
                <document>catalogindex_minimal_price</document>
            </ignore>
            <ignore>
                <document>catalogindex_price</document>
            </ignore>
            <ignore>
                <document>core_layout_update</document>
            </ignore>
            <ignore>
                <document>core_layout_link</document>
            </ignore>
            <ignore>
                <document>widget_instance_page_layout</document>
            </ignore>
            <ignore>
                <document>enterprise_queue_queue</document>
            </ignore>
            <ignore>
                <document>enterprise_queue_task</document>
            </ignore>
            <ignore>
                <document>find_feed_import_codes</document>
            </ignore>
            <ignore>
                <document>googlebase_attributes</document>
            </ignore>
            <ignore>
                <document>googlebase_items</document>
            </ignore>
            <ignore>
                <document>googlebase_types</document>
            </ignore>
            <ignore>
                <document>googlecheckout_api_debug</document>
            </ignore>
            <ignore>
                <document>ogone_api_debug</document>
            </ignore>
            <ignore>
                <document>oscommerce_import</document>
            </ignore>
            <ignore>
                <document>oscommerce_import_type</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders_products</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders_status_history</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders_total</document>
            </ignore>
            <ignore>
                <document>oscommerce_ref</document>
            </ignore>
            <ignore>
                <document>paygate_authorizenet_debug</document>
            </ignore>
            <ignore>
                <document>paypal_api_debug</document>
            </ignore>
            <ignore>
                <document>paypaluk_api_debug</document>
            </ignore>
            <ignore>
                <document>amazonpayments_api_debug</document>
            </ignore>
            <ignore>
                <document>chronopay_api_debug</document>
            </ignore>
            <ignore>
                <document>cybermut_api_debug</document>
            </ignore>
            <ignore>
                <document>cybersource_api_debug</document>
            </ignore>
            <ignore>
                <document>eway_api_debug</document>
            </ignore>
            <ignore>
                <document>flo2cash_api_debug</document>
            </ignore>
            <ignore>
                <document>ideal_api_debug</document>
            </ignore>
            <ignore>
                <document>paybox_api_debug</document>
            </ignore>
            <ignore>
                <document>paybox_question_number</document>
            </ignore>
            <ignore>
                <document>protx_api_debug</document>
            </ignore>
            <ignore>
                <document>social_facebook_actions</document>
            </ignore>
            <ignore>
                <document>strikeiron_tax_rate</document>
            </ignore>
            <ignore>
                <document>xmlconnect_application</document>
            </ignore>
            <ignore>
                <document>xmlconnect_config_data</document>
            </ignore>
            <ignore>
                <document>xmlconnect_history</document>
            </ignore>
            <ignore>
                <document>xmlconnect_images</document>
            </ignore>
            <ignore>
                <document>xmlconnect_notification_template</document>
            </ignore>
            <ignore>
                <document>xmlconnect_queue</document>
            </ignore>
            <ignore>
                <document>googlecheckout_notification</document>
            </ignore>
            <ignore>
                <document>index_event</document>
            </ignore>
            <ignore>
                <document>index_process</document>
            </ignore>
            <ignore>
                <document>index_process_event</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_cl</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_cl</document>
            </ignore>
            <ignore>
                <document>core_cache_tag</document>
            </ignore>
            <ignore>
                <document>core_cache_option</document>
            </ignore>
            <ignore>
                <document>core_cache</document>
            </ignore>
            <ignore>
                <document>core_flag</document>
            </ignore>
            <ignore>
                <document>core_email_queue</document>
            </ignore>
            <ignore>
                <document>core_email_queue_recipients</document>
            </ignore>
            <ignore>
                <document>merchandiser_category_values</document>
            </ignore>
            <ignore>
                <document>merchandiser_vmbuild</document>
            </ignore>
            <ignore>
                <document>dataflow_batch</document>
            </ignore>
            <ignore>
                <document>dataflow_batch_export</document>
            </ignore>
            <ignore>
                <document>dataflow_batch_import</document>
            </ignore>
            <ignore>
                <document>dataflow_import_data</document>
            </ignore>
            <ignore>
                <document>dataflow_profile</document>
            </ignore>
            <ignore>
                <document>dataflow_profile_history</document>
            </ignore>
            <ignore>
                <document>dataflow_session</document>
            </ignore>
            <ignore>
                <document>core_url_rewrite</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_url_key</document>
            </ignore>
            <ignore>
                <document>catalogindex_eav</document>
            </ignore>
            <ignore>
                <document>catalog_eav_attribute</document>
            </ignore>
            <ignore>
                <document>customer_eav_attribute</document>
            </ignore>
            <ignore>
                <document>eav_attribute</document>
            </ignore>
            <ignore>
                <document>eav_attribute_group</document>
            </ignore>
            <ignore>
                <document>eav_attribute_set</document>
            </ignore>
            <ignore>
                <document>eav_entity_attribute</document>
            </ignore>
            <ignore>
                <document>eav_entity_type</document>
            </ignore>
            <ignore>
                <document>catalog_category_entity_url_key</document>
            </ignore>
            <ignore>
                <document>poll</document>
            </ignore>
            <ignore>
                <document>poll_answer</document>
            </ignore>
            <ignore>
                <document>poll_store</document>
            </ignore>
            <ignore>
                <document>poll_vote</document>
            </ignore>
            <ignore>
                <document>sales_recurring_profile</document>
            </ignore>
            <ignore>
                <document>sales_recurring_profile_order</document>
            </ignore>
            <ignore>
                <document>tag</document>
            </ignore>
            <ignore>
                <document>tag_properties</document>
            </ignore>
            <ignore>
                <document>tag_relation</document>
            </ignore>
            <ignore>
                <document>tag_summary</document>
            </ignore>
            <ignore>
                <document>weee_discount</document>
            </ignore>
            <ignore>
                <document>catalogsearch_result</document>
            </ignore>
            <ignore>
                <document>log_visitor</document>
            </ignore>
            <ignore>
                <document>log_visitor_info</document>
            </ignore>
            <ignore>
                <document>log_visitor_online</document>
            </ignore>
            <ignore>
                <document>core_config_data</document>
            </ignore>
            <ignore>
                <document>s_*b_*_*</document>
            </ignore>
            <ignore>
                <document>catalog_product_flat_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_store_*</document>
            </ignore>
            <ignore>
                <document>googleoptimizer_code</document>
            </ignore>
            <ignore>
                <document>core_resource</document>
            </ignore>
            <ignore>
                <document>cron_schedule</document>
            </ignore>
            <ignore>
                <document>admin_user</document>
            </ignore>
            <ignore>
                <document>enterprise_admin_passwords</document>
            </ignore>
            <ignore>
                <document>admin_role</document>
            </ignore>
            <ignore>
                <document>admin_rule</document>
            </ignore>
            <ignore>
                <document>sales_flat_order</document>
            </ignore>
            <ignore>
                <document>admin_role</document>
            </ignore>
            <ignore>
                <document>admin_rule</document>
            </ignore>
            <ignore>
                <document>enterprise_url_rewrite</document>
            </ignore>
            <ignore>
                <document>enterprise_url_rewrite_category_cl</document>
            </ignore>
            <ignore>
                <document>enterprise_url_rewrite_product_cl</document>
            </ignore>
            <ignore>
                <document>enterprise_url_rewrite_redirect</document>
            </ignore>
            <ignore>
                <document>enterprise_url_rewrite_redirect_cl</document>
            </ignore>
            <ignore>
                <document>enterprise_url_rewrite_redirect_rewrite</document>
            </ignore>
            <ignore>
                <document>enterprise_catalog_category_rewrite</document>
            </ignore>
            <ignore>
                <document>enterprise_catalog_product_rewrite</document>
            </ignore>
            <ignore>
                <document>enterprise_catalogpermissions_index</document>
            </ignore>
            <ignore>
                <document>enterprise_catalogpermissions_index_product</document>
            </ignore>
            <ignore>
                <document>enterprise_rma_item_eav_attribute</document>
            </ignore>
            <ignore>
                <document>enterprise_rma_item_eav_attribute_website</document>
            </ignore>
            <ignore>
                <document>enterprise_customer_sales_flat_order</document>
            </ignore>
            <ignore>
                <document>enterprise_customer_sales_flat_order_address</document>
            </ignore>
            <ignore>
                <document>enterprise_customer_sales_flat_quote</document>
            </ignore>
            <ignore>
                <document>enterprise_customer_sales_flat_quote_address</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index_crosssell</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index_crosssell_product</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index_related</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index_related_product</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index_upsell</document>
            </ignore>
            <ignore>
                <document>enterprise_targetrule_index_upsell_product</document>
            </ignore>
            <ignore>
                <document>enterprise_staging</document>
            </ignore>
            <ignore>
                <document>enterprise_staging_action</document>
            </ignore>
            <ignore>
                <document>enterprise_staging_item</document>
            </ignore>
            <ignore>
                <document>enterprise_staging_log</document>
            </ignore>
            <ignore>
                <document>core_session</document>
            </ignore>
            <ignore>
                <document>enterprise_staging_product_unlinked</document>
            </ignore>
            <ignore>
                <document>enterprise_support_backup</document>
            </ignore>
            <ignore>
                <document>enterprise_support_backup_item</document>
            </ignore>
            <ignore>
                <document>enterprise_support_sysreport</document>
            </ignore>
            <ignore>
                <document>enterprise_mview_event</document>
            </ignore>
            <ignore>
                <document>enterprise_mview_metadata</document>
            </ignore>
            <ignore>
                <document>enterprise_mview_metadata_event</document>
            </ignore>
            <ignore>
                <document>enterprise_mview_metadata_group</document>
            </ignore>
            <ignore>
                <document>enterprise_mview_subscriber</document>
            </ignore>
            <ignore>
                <document>enterprise_index_multiplier</document>
            </ignore>
            <ignore>
                <document>catalogrule_affected_product</document>
            </ignore>
            <ignore>
                <document>catalog_product_super_attribute_pricing</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_group_price</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_tier_price</document>
            </ignore>
            <ignore>
                <document>permission_block</document>
            </ignore>
            <ignore>
                <document>permission_variable</document>
            </ignore>
            <ignore>
                <document>enterprise_cms_page_revision</document>
            </ignore>
            <ignore>
                <document>enterprise_cms_page_version</document>
            </ignore>
            <ignore>
                <document>customer_flowpassword</document>
            </ignore>
            <ignore>
                <document>core_store</document>
            </ignore>
            <ignore>
                <document>core_store_group</document>
            </ignore>
            <ignore>
                <document>core_website</document>
            </ignore>
            <rename>
                <document>am_shopby_page</document>
                <to>amasty_amshopby_page</to>
            </rename>
            <rename>
                <document>catalogsearch_query</document>
                <to>search_query</to>
            </rename>
            <rename>
                <document>core_variable</document>
                <to>variable</to>
            </rename>
            <rename>
                <document>core_variable_value</document>
                <to>variable_value</to>
            </rename>
            <rename>
                <document>core_email_template</document>
                <to>email_template</to>
            </rename>
            <rename>
                <document>core_translate</document>
                <to>translation</to>
            </rename>
            <rename>
                <document>core_session</document>
                <to>session</to>
            </rename>
            <rename>
                <document>coupon_aggregated</document>
                <to>salesrule_coupon_aggregated</to>
            </rename>
            <rename>
                <document>coupon_aggregated_order</document>
                <to>salesrule_coupon_aggregated_order</to>
            </rename>
            <rename>
                <document>coupon_aggregated_updated</document>
                <to>salesrule_coupon_aggregated_updated</to>
            </rename>
            <rename>
                <document>sales_flat_creditmemo</document>
                <to>sales_creditmemo</to>
            </rename>
            <rename>
                <document>sales_flat_creditmemo_comment</document>
                <to>sales_creditmemo_comment</to>
            </rename>
            <rename>
                <document>sales_flat_creditmemo_item</document>
                <to>sales_creditmemo_item</to>
            </rename>
            <rename>
                <document>sales_flat_invoice</document>
                <to>sales_invoice</to>
            </rename>
            <rename>
                <document>sales_flat_invoice_comment</document>
                <to>sales_invoice_comment</to>
            </rename>
            <rename>
                <document>sales_flat_invoice_item</document>
                <to>sales_invoice_item</to>
            </rename>
            <rename>
                <document>sales_flat_order_address</document>
                <to>sales_order_address</to>
            </rename>
            <rename>
                <document>sales_flat_order_item</document>
                <to>sales_order_item</to>
            </rename>
            <rename>
                <document>sales_flat_order_payment</document>
                <to>sales_order_payment</to>
            </rename>
            <rename>
                <document>sales_flat_order_status_history</document>
                <to>sales_order_status_history</to>
            </rename>
            <rename>
                <document>sales_flat_quote</document>
                <to>quote</to>
            </rename>
            <rename>
                <document>sales_flat_quote_address</document>
                <to>quote_address</to>
            </rename>
            <rename>
                <document>sales_flat_quote_address_item</document>
                <to>quote_address_item</to>
            </rename>
            <rename>
                <document>sales_flat_quote_item</document>
                <to>quote_item</to>
            </rename>
            <rename>
                <document>sales_flat_quote_item_option</document>
                <to>quote_item_option</to>
            </rename>
            <rename>
                <document>sales_flat_quote_payment</document>
                <to>quote_payment</to>
            </rename>
            <rename>
                <document>sales_flat_quote_shipping_rate</document>
                <to>quote_shipping_rate</to>
            </rename>
            <rename>
                <document>sales_flat_shipment</document>
                <to>sales_shipment</to>
            </rename>
            <rename>
                <document>sales_flat_shipment_comment</document>
                <to>sales_shipment_comment</to>
            </rename>
            <rename>
                <document>sales_flat_shipment_item</document>
                <to>sales_shipment_item</to>
            </rename>
            <rename>
                <document>sales_flat_shipment_track</document>
                <to>sales_shipment_track</to>
            </rename>
            <rename>
                <document>sales_billing_agreement</document>
                <to>paypal_billing_agreement</to>
            </rename>
            <rename>
                <document>sales_billing_agreement_order</document>
                <to>paypal_billing_agreement_order</to>
            </rename>
            <rename>
                <document>enterprise_rma</document>
                <to>magento_rma</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_entity</document>
                <to>magento_rma_item_entity</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_entity_datetime</document>
                <to>magento_rma_item_entity_datetime</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_entity_decimal</document>
                <to>magento_rma_item_entity_decimal</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_entity_int</document>
                <to>magento_rma_item_entity_int</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_entity_text</document>
                <to>magento_rma_item_entity_text</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_entity_varchar</document>
                <to>magento_rma_item_entity_varchar</to>
            </rename>
            <rename>
                <document>enterprise_rma_item_form_attribute</document>
                <to>magento_rma_item_form_attribute</to>
            </rename>
            <rename>
                <document>enterprise_rma_shipping_label</document>
                <to>magento_rma_shipping_label</to>
            </rename>
            <rename>
                <document>enterprise_rma_status_history</document>
                <to>magento_rma_status_history</to>
            </rename>
            <rename>
                <document>enterprise_banner</document>
                <to>magento_banner</to>
            </rename>
            <rename>
                <document>enterprise_banner_catalogrule</document>
                <to>magento_banner_catalogrule</to>
            </rename>
            <rename>
                <document>enterprise_banner_content</document>
                <to>magento_banner_content</to>
            </rename>
            <rename>
                <document>enterprise_banner_customersegment</document>
                <to>magento_banner_customersegment</to>
            </rename>
            <rename>
                <document>enterprise_banner_salesrule</document>
                <to>magento_banner_salesrule</to>
            </rename>
            <rename>
                <document>enterprise_catalogevent_event</document>
                <to>magento_catalogevent_event</to>
            </rename>
            <rename>
                <document>enterprise_catalogevent_event_image</document>
                <to>magento_catalogevent_event_image</to>
            </rename>
            <rename>
                <document>enterprise_catalogpermissions</document>
                <to>magento_catalogpermissions</to>
            </rename>
            <rename>
                <document>enterprise_cms_hierarchy_lock</document>
                <to>magento_versionscms_hierarchy_lock</to>
            </rename>
            <rename>
                <document>enterprise_cms_hierarchy_metadata</document>
                <to>magento_versionscms_hierarchy_metadata</to>
            </rename>
            <rename>
                <document>enterprise_cms_hierarchy_node</document>
                <to>magento_versionscms_hierarchy_node</to>
            </rename>
            <rename>
                <document>enterprise_cms_increment</document>
                <to>magento_versionscms_increment</to>
            </rename>
            <rename>
                <document>enterprise_customerbalance</document>
                <to>magento_customerbalance</to>
            </rename>
            <rename>
                <document>enterprise_customerbalance_history</document>
                <to>magento_customerbalance_history</to>
            </rename>
            <rename>
                <document>enterprise_customersegment_customer</document>
                <to>magento_customersegment_customer</to>
            </rename>
            <rename>
                <document>enterprise_customersegment_event</document>
                <to>magento_customersegment_event</to>
            </rename>
            <rename>
                <document>enterprise_customersegment_segment</document>
                <to>magento_customersegment_segment</to>
            </rename>
            <rename>
                <document>enterprise_customersegment_website</document>
                <to>magento_customersegment_website</to>
            </rename>
            <rename>
                <document>enterprise_giftcard_amount</document>
                <to>magento_giftcard_amount</to>
            </rename>
            <rename>
                <document>enterprise_giftcardaccount</document>
                <to>magento_giftcardaccount</to>
            </rename>
            <rename>
                <document>enterprise_giftcardaccount_history</document>
                <to>magento_giftcardaccount_history</to>
            </rename>
            <rename>
                <document>enterprise_giftcardaccount_pool</document>
                <to>magento_giftcardaccount_pool</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_data</document>
                <to>magento_giftregistry_data</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_entity</document>
                <to>magento_giftregistry_entity</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_item</document>
                <to>magento_giftregistry_item</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_item_option</document>
                <to>magento_giftregistry_item_option</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_label</document>
                <to>magento_giftregistry_label</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_person</document>
                <to>magento_giftregistry_person</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_type</document>
                <to>magento_giftregistry_type</to>
            </rename>
            <rename>
                <document>enterprise_giftregistry_type_info</document>
                <to>magento_giftregistry_type_info</to>
            </rename>
            <rename>
                <document>enterprise_giftwrapping</document>
                <to>magento_giftwrapping</to>
            </rename>
            <rename>
                <document>enterprise_giftwrapping_store_attributes</document>
                <to>magento_giftwrapping_store_attributes</to>
            </rename>
            <rename>
                <document>enterprise_giftwrapping_website</document>
                <to>magento_giftwrapping_website</to>
            </rename>
            <rename>
                <document>enterprise_invitation</document>
                <to>magento_invitation</to>
            </rename>
            <rename>
                <document>enterprise_invitation_status_history</document>
                <to>magento_invitation_status_history</to>
            </rename>
            <rename>
                <document>enterprise_invitation_track</document>
                <to>magento_invitation_track</to>
            </rename>
            <rename>
                <document>enterprise_logging_event</document>
                <to>magento_logging_event</to>
            </rename>
            <rename>
                <document>enterprise_logging_event_changes</document>
                <to>magento_logging_event_changes</to>
            </rename>
            <rename>
                <document>enterprise_reminder_rule</document>
                <to>magento_reminder_rule</to>
            </rename>
            <rename>
                <document>enterprise_reminder_rule_coupon</document>
                <to>magento_reminder_rule_coupon</to>
            </rename>
            <rename>
                <document>enterprise_reminder_rule_log</document>
                <to>magento_reminder_rule_log</to>
            </rename>
            <rename>
                <document>enterprise_reminder_rule_website</document>
                <to>magento_reminder_rule_website</to>
            </rename>
            <rename>
                <document>enterprise_reminder_template</document>
                <to>magento_reminder_template</to>
            </rename>
            <rename>
                <document>enterprise_reward</document>
                <to>magento_reward</to>
            </rename>
            <rename>
                <document>enterprise_reward_history</document>
                <to>magento_reward_history</to>
            </rename>
            <rename>
                <document>enterprise_reward_rate</document>
                <to>magento_reward_rate</to>
            </rename>
            <rename>
                <document>enterprise_reward_salesrule</document>
                <to>magento_reward_salesrule</to>
            </rename>
            <rename>
                <document>enterprise_scheduled_operations</document>
                <to>magento_scheduled_operations</to>
            </rename>
            <rename>
                <document>enterprise_targetrule</document>
                <to>magento_targetrule</to>
            </rename>
            <rename>
                <document>enterprise_targetrule_customersegment</document>
                <to>magento_targetrule_customersegment</to>
            </rename>
            <rename>
                <document>enterprise_targetrule_product</document>
                <to>magento_targetrule_product</to>
            </rename>
            <rename>
                <document>core_directory_storage</document>
                <to>media_storage_directory_storage</to>
            </rename>
            <rename>
                <document>core_file_storage</document>
                <to>media_storage_file_storage</to>
            </rename>
            <rename>
                <document>enterprise_rma_grid</document>
                <to>magento_rma_grid</to>
            </rename>
            <rename>
                <document>enterprise_support_backup</document>
                <to>support_backup</to>
            </rename>
            <rename>
                <document>enterprise_support_backup_item</document>
                <to>support_backup_item</to>
            </rename>
        </document_rules>
        <field_rules>

            <!-- INCHOO START -->
            <move>
                <field>am_shopby_page.meta_kw</field>
                <to>amasty_amshopby_page.meta_keywords</to>
            </move>
            <move>
                <field>am_shopby_page.cond</field>
                <to>amasty_amshopby_page.conditions</to>
            </move>
            <move>
                <field>am_shopby_page.cats</field>
                <to>amasty_amshopby_page.categories</to>
            </move>
            <move>
                <field>am_shopby_page.cms_block_id</field>
                <to>amasty_amshopby_page.top_block_id</to>
            </move>
            <move>
                <field>am_shopby_page.bottom_cms_block_id</field>
                <to>amasty_amshopby_page.bottom_block_id</to>
            </move>
            <move>
                <field>am_shopby_page.meta_descr</field>
                <to>amasty_amshopby_page.meta_description</to>
            </move>

            <!-- INCHOO END -->

            <move>
                <field>cms_page.root_template</field>
                <to>cms_page.page_layout</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.hidden_tax_amount</field>
                <to>sales_creditmemo.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.base_hidden_tax_amount</field>
                <to>sales_creditmemo.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.shipping_hidden_tax_amount</field>
                <to>sales_creditmemo.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.base_shipping_hidden_tax_amnt</field>
                <to>sales_creditmemo.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_creditmemo_item.hidden_tax_amount</field>
                <to>sales_creditmemo_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo_item.base_hidden_tax_amount</field>
                <to>sales_creditmemo_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.hidden_tax_amount</field>
                <to>sales_invoice.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.base_hidden_tax_amount</field>
                <to>sales_invoice.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.shipping_hidden_tax_amount</field>
                <to>sales_invoice.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.base_shipping_hidden_tax_amnt</field>
                <to>sales_invoice.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_invoice_item.hidden_tax_amount</field>
                <to>sales_invoice_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice_item.base_hidden_tax_amount</field>
                <to>sales_invoice_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_amount</field>
                <to>sales_order_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order_item.base_hidden_tax_amount</field>
                <to>sales_order_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_invoiced</field>
                <to>sales_order_item.discount_tax_compensation_invoiced</to>
            </move>
            <move>
                <field>sales_flat_order_item.base_hidden_tax_invoiced</field>
                <to>sales_order_item.base_discount_tax_compensation_invoiced</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_refunded</field>
                <to>sales_order_item.discount_tax_compensation_refunded</to>
            </move>
            <move>
                <field>sales_flat_order_item.base_hidden_tax_refunded</field>
                <to>sales_order_item.base_discount_tax_compensation_refunded</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_canceled</field>
                <to>sales_order_item.discount_tax_compensation_canceled</to>
            </move>
            <move>
                <field>sales_flat_order_payment.cc_last4</field>
                <to>sales_order_payment.cc_last_4</to>
            </move>
            <move>
                <field>sales_flat_quote_payment.cc_last4</field>
                <to>quote_payment.cc_last_4</to>
            </move>
            <move>
                <field>sales_flat_quote_address.hidden_tax_amount</field>
                <to>quote_address.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address.base_hidden_tax_amount</field>
                <to>quote_address.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address.shipping_hidden_tax_amount</field>
                <to>quote_address.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address.base_shipping_hidden_tax_amnt</field>
                <to>quote_address.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_quote_address_item.hidden_tax_amount</field>
                <to>quote_address_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address_item.base_hidden_tax_amount</field>
                <to>quote_address_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_item.hidden_tax_amount</field>
                <to>quote_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_item.base_hidden_tax_amount</field>
                <to>quote_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>catalog_category_entity_datetime.entity_id</field>
                <to>catalog_category_entity_datetime.row_id</to>
            </move>
            <move>
                <field>catalog_category_entity_decimal.entity_id</field>
                <to>catalog_category_entity_decimal.row_id</to>
            </move>
            <move>
                <field>catalog_category_entity_int.entity_id</field>
                <to>catalog_category_entity_int.row_id</to>
            </move>
            <move>
                <field>catalog_category_entity_text.entity_id</field>
                <to>catalog_category_entity_text.row_id</to>
            </move>
            <move>
                <field>catalog_category_entity_varchar.entity_id</field>
                <to>catalog_category_entity_varchar.row_id</to>
            </move>
            <!--move>
                <field>catalog_product_entity_datetime.entity_id</field>
                <to>catalog_product_entity_datetime.row_id</to>
            </move>
            <move>
                <field>catalog_product_entity_decimal.entity_id</field>
                <to>catalog_product_entity_decimal.row_id</to>
            </move>
            <move>
                <field>catalog_product_entity_gallery.entity_id</field>
                <to>catalog_product_entity_gallery.row_id</to>
            </move>
            <move>
                <field>catalog_product_entity_int.entity_id</field>
                <to>catalog_product_entity_int.row_id</to>
            </move>
            <move>
                <field>catalog_product_entity_text.entity_id</field>
                <to>catalog_product_entity_text.row_id</to>
            </move>
            <move>
                <field>catalog_product_entity_varchar.entity_id</field>
                <to>catalog_product_entity_varchar.row_id</to>
            </move-->
            <!--move>
                <field>catalogrule_customer_group.rule_id</field>
                <to>catalogrule_customer_group.row_id</to>
            </move-->
            <!--move>
                <field>catalogrule_website.rule_id</field>
                <to>catalogrule_website.row_id</to>
            </move-->
            <move>
                <field>salesrule_customer_group.rule_id</field>
                <to>salesrule_customer_group.row_id</to>
            </move>
            <move>
                <field>salesrule_product_attribute.rule_id</field>
                <to>salesrule_product_attribute.row_id</to>
            </move>
            <move>
                <field>salesrule_website.rule_id</field>
                <to>salesrule_website.row_id</to>
            </move>
            <move>
                <field>cms_block_store.block_id</field>
                <to>cms_block_store.row_id</to>
            </move>
            <move>
                <field>cms_page_store.page_id</field>
                <to>cms_page_store.row_id</to>
            </move>
            <move>
                <field>catalog_product_entity_tier_price.entity_id</field>
                <to>catalog_product_entity_tier_price.row_id</to>
            </move>
            <move>
                <field>enterprise_giftcard_amount.entity_id</field>
                <to>magento_giftcard_amount.row_id</to>
            </move>
            <!-- INCHOO -->
            <transform>
                <field>am_shopby_page.cond</field>
                <handler class="\Inchoo\Migration\Handler\Amasty\Shopby\Page\Column\Conditions" />
            </transform>
            <transform>
                <field>am_shopby_page.stores</field>
                <handler class="\Inchoo\Migration\Handler\Amasty\Shopby\Page\Column\Stores" />
            </transform>
            <!-- INCHOO END -->
            <transform>
                <field>catalog_category_entity_varchar.value</field>
                <handler class="\Migration\Handler\ConvertEavValue">
                    <param name="map" value="[one_column:1column;two_columns_left:2columns-left;two_columns_right:2columns-right;three_columns:3columns]" />
                    <param name="attributeCode" value="page_layout" />
                </handler>
            </transform>
            <transform>
                <field>catalog_category_entity_varchar.value</field>
                <handler class="\Migration\Handler\SetValueAttributeCondition">
                    <param name="attributeCode" value="custom_design" />
                    <param name="value" value="null" />
                </handler>
            </transform>
            <transform>
                <field>catalog_category_entity_text.value</field>
                <handler class="\Migration\Handler\SetValueAttributeCondition">
                    <param name="attributeCode" value="custom_layout_update" />
                    <param name="value" value="null" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_entity_varchar.value</field>
                <handler class="\Migration\Handler\ConvertEavValue">
                    <param name="map" value="[one_column:1column;two_columns_left:2columns-left;two_columns_right:2columns-right;three_columns:3columns]" />
                    <param name="attributeCode" value="page_layout" />
                </handler>
            </transform>
            <transform>
                <field>cms_page.root_template</field>
                <handler class="\Migration\Handler\Convert">
                    <param name="map" value="[one_column:1column;two_columns_left:2columns-left;two_columns_right:2columns-right;three_columns:3columns]" />
                    <param name="defaultValue" value="1column" />
                </handler>
            </transform>
            <transform>
                <field>cms_block.content</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>cms_page.content</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>newsletter_template.template_text</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>core_email_template.template_text</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>widget_instance.instance_type</field>
                <handler class="\Migration\Handler\ClassMap" />
            </transform>
            <transform>
                <field>catalogrule.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>catalogrule.actions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>salesrule.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>salesrule.actions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>enterprise_targetrule.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>enterprise_targetrule.actions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>enterprise_reminder_rule.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>enterprise_reminder_rule.condition_sql</field>
                <handler class="Migration\Handler\Rule\ConditionSql" />
            </transform>
            <transform>
                <field>enterprise_customersegment_segment.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData" />
            </transform>
            <transform>
                <field>enterprise_customersegment_segment.condition_sql</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="" />
                </handler>
            </transform>
            <transform>
                <field>sales_flat_order_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_creditmemo_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_invoice_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_logging_event.ip</field>
                <handler class="Migration\Handler\ConvertIp" />
            </transform>
            <transform>
                <field>enterprise_logging_event.x_forwarded_ip</field>
                <handler class="Migration\Handler\ConvertIp" />
            </transform>
            <transform>
                <field>rating_option_vote.remote_ip_long</field>
                <handler class="Migration\Handler\ConvertIp" />
            </transform>
            <transform>
                <field>sendfriend_log.ip</field>
                <handler class="Migration\Handler\ConvertIp" />
            </transform>
            <transform>
                <field>catalog_product_entity_media_gallery.value_id</field>
                <handler class="\Migration\Handler\Gallery\InsertValueToEntity" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_type.meta_xml</field>
                <handler class="\Migration\Handler\ConvertDateFormat" />
            </transform>
            <transform>
                <field>catalogrule.rule_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_catalogrule" />
                </handler>
            </transform>
            <transform>
                <field>salesrule.rule_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_salesrule" />
                </handler>
            </transform>
            <transform>
                <field>catalog_category_entity.entity_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_catalog_category" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_entity.entity_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_product" />
                </handler>
            </transform>
            <transform>
                <field>cms_block.block_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_cms_block" />
                </handler>
            </transform>
            <transform>
                <field>cms_page.page_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_cms_page" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_bundle_option.option_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_product_bundle_option" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_bundle_selection.selection_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_product_bundle_selection" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_bundle_option.option_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_product_bundle_option" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_bundle_selection.selection_id</field>
                <handler class="\Migration\Handler\Sequence">
                    <param name="table" value="sequence_product_bundle_selection" />
                </handler>
            </transform>
            <transform>
                <field>catalogsearch_query.synonym_for</field>
                <handler class="\Migration\Handler\Synonym" />
            </transform>
            <transform>
                <field>sales_flat_quote_payment.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_payment.cc_number_enc</field>
                <handler class="\Migration\Handler\Encrypt" />
            </transform>
            <transform>
                <field>enterprise_reward_history.additional_data</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_order_item.product_options</field>
                <handler class="\Migration\Handler\SerializeToJson\SalesOrderItem" />
            </transform>
            <transform>
                <field>sales_flat_order_payment.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_order_payment.cc_number_enc</field>
                <handler class="\Migration\Handler\Encrypt" />
            </transform>
            <transform>
                <field>sales_flat_shipment.packages</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_payment_transaction.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>sales_flat_quote_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>enterprise_giftregistry_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption" />
            </transform>
            <transform>
                <field>wishlist_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>wishlist_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption" />
            </transform>
            <transform>
                <field>sales_flat_quote.gift_cards</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_address.applied_taxes</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_address.gift_cards</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>widget_instance.widget_parameters</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_reminder_rule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_targetrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_targetrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_targetrule.action_select_bind</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>salesrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>salesrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>catalogrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>catalogrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_customersegment_segment.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>admin_user.extra</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>email_template.is_legacy</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="1" />
                </handler>
            </transform>
            <transform>
                <field>newsletter_template.is_legacy</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="1" />
                </handler>
            </transform>
            <transform>
                <field>enterprise_logging_event.info</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_logging_event_changes.original_data</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_logging_event_changes.result_data</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_rma_item_entity.product_options</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_rma_shipping_label.packages</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_person.custom_values</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_entity.custom_values</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_entity.shipping_address</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_scheduled_operations.file_info</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_scheduled_operations.entity_attributes</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_reward_history.additional_data</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_order_item.product_options</field>
                <handler class="\Migration\Handler\SerializeToJson\SalesOrderItem" />
            </transform>
            <transform>
                <field>sales_flat_shipment.packages</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_payment_transaction.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>sales_flat_quote_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>enterprise_giftregistry_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption" />
            </transform>
            <transform>
                <field>wishlist_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>wishlist_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption" />
            </transform>
            <transform>
                <field>sales_flat_quote.gift_cards</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_address.applied_taxes</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>sales_flat_quote_address.gift_cards</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>widget_instance.widget_parameters</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_reminder_rule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_targetrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_targetrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_targetrule.action_select_bind</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>salesrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>salesrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>catalogrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>catalogrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_customersegment_segment.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>admin_user.extra</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_logging_event.info</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_logging_event_changes.original_data</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_logging_event_changes.result_data</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_rma_item_entity.product_options</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_rma_shipping_label.packages</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_person.custom_values</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_entity.custom_values</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_giftregistry_entity.shipping_address</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_scheduled_operations.file_info</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <transform>
                <field>enterprise_scheduled_operations.entity_attributes</field>
                <handler class="\Migration\Handler\SerializeToJson" />
            </transform>
            <!-- INCHOO: -->
            <ignore>
                <field>tax_class.tj_salestax_code</field>
            </ignore>
            <ignore>
                <field>am_shopby_page.stores</field>
            </ignore>
            <ignore>
                <field>am_shopby_page.num</field>
            </ignore>
            <ignore>
                <field>am_shopby_page.use_cat</field>
            </ignore>
            <ignore>
                <field>am_shopby_page.custom_layout_update_xml</field>
            </ignore>
            <ignore>
                <field>am_shopby_page.seo_noindex</field>
            </ignore>
            <ignore>
                <field>am_shopby_page.seo_nofollow</field>
            </ignore>
            <!-- INC
            OO END; -->
            <ignore>
                <field>admin_rule.role_type</field>
            </ignore>
            <ignore>
                <field>admin_rule.assert_id</field>
            </ignore>
            <ignore>
                <field>sales_order_tax.hidden</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.reward_points_balance_refunded</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.reward_salesrule_points</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_item.is_nominal</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.paybox_request_number</field>
            </ignore>
            <ignore>
                <field>weee_tax.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_varchar.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_gallery.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery.entity_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_varchar.entity_type_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_creditmemo.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.ideal_transaction_checked</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.ideal_issuer_title</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.paybox_question_number</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.ideal_issuer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.flo2cash_account_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.ideal_issuer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.ideal_issuer_list</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.paypal_payer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.paypal_payer_status</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.paypal_correlation_id</field>
            </ignore>
            <ignore>
                <field>cms_page.layout_update_xml</field>
            </ignore>
            <ignore>
                <field>cms_page.custom_layout_update_xml</field>
            </ignore>
            <ignore>
                <field>cms_page.amseo-uuid</field>
            </ignore>
            <ignore>
                <field>widget_instance.package_theme</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity.attribute_set_id</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_rma_item_entity_varchar.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_giftcard_amount.entity_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_targetrule.use_customer_segment</field>
            </ignore>
            <ignore>
                <field>enterprise_targetrule_product.store_id</field>
            </ignore>
            <ignore>
                <field>newsletter_template.template_text_preprocessed</field>
            </ignore>
            <ignore>
                <field>enterprise_giftregistry_person.middlename</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote.base_customer_balance_virtual_amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote.visitor_id</field>
            </ignore>
            <ignore>
                <field>catalogsearch_query.synonym_for</field>
            </ignore>
            <ignore>
                <field>catalogrule.sub_is_enable</field>
            </ignore>
            <ignore>
                <field>catalogrule.sub_simple_action</field>
            </ignore>
            <ignore>
                <field>catalogrule.sub_discount_amount</field>
            </ignore>
            <ignore>
                <field>catalogrule_product.sub_simple_action</field>
            </ignore>
            <ignore>
                <field>catalogrule_product.sub_discount_amount</field>
            </ignore>
            <ignore>
                <field>cms_page.published_revision_id</field>
            </ignore>
            <ignore>
                <field>cms_page.under_version_control</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.customer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.invoice_status_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.invoice_type</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.is_virtual</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.real_order_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.total_due</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.total_paid</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice_item.shipment_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.base_custbalance_amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.currency_base_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.currency_code</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.currency_rate</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.custbalance_amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.is_hold</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.is_multi_payment</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.real_order_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.tax_percent</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.tracking_numbers</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_address.address_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_address.gift_message_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_address.tax_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.cc_raw_request</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.cc_raw_response</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.customer_payment_id</field>
            </ignore>
            <ignore>
                <field>sales_bestsellers_aggregated_daily.product_type_id</field>
            </ignore>
            <ignore>
                <field>sales_bestsellers_aggregated_monthly.product_type_id</field>
            </ignore>
            <ignore>
                <field>sales_bestsellers_aggregated_yearly.product_type_id</field>
            </ignore>
            <ignore>
                <field>enterprise_banner.is_ga_enabled</field>
            </ignore>
            <ignore>
                <field>enterprise_banner.ga_creative</field>
            </ignore>
            <ignore>
                <datatype>sales_flat_quote_payment.cc_exp_month</datatype>
            </ignore>
            <ignore>
                <datatype>weee_tax.state</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_banner.is_ga_enabled</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_logging_event.ip</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_logging_event.x_forwarded_ip</datatype>
            </ignore>
            <ignore>
                <datatype>rating_option_vote.remote_ip_long</datatype>
            </ignore>
            <ignore>
                <datatype>sendfriend_log.ip</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_catalogevent_event.date_start</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_catalogevent_event.date_end</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_token.callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.rejected_callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_reminder_rule.from_date</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_reminder_rule.to_date</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_invitation.group_id</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_logging_event.info</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_group_website.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_catalogpermissions.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_reward_rate.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_product_attribute.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>sales_flat_order.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>paypal_settlement_report.report_date</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_coupon.expiration_date</datatype>
            </ignore>
            <!-- INCHOO START -->
            <ignore>
                <datatype>am_shopby_page.page_id</datatype>
            </ignore>
            <ignore>
                <datatype>am_shopby_page.cond</datatype>
            </ignore>
            <ignore>
                <datatype>am_shopby_page.meta_kw</datatype>
            </ignore>
            <ignore>
                <datatype>am_shopby_page.cats</datatype>
            </ignore>
            <ignore>
                <datatype>am_shopby_page.cms_block_id</datatype>
            </ignore>
            <ignore>
                <datatype>am_shopby_page.bottom_cms_block_id</datatype>
            </ignore>
            <ignore>
                <datatype>am_shopby_page.seo_sitemap</datatype>
            </ignore>
            <!-- INCHOO END -->
        </field_rules>
    </source>
    <destination>
        <document_rules>
            <!-- INCHOO-->
            <ignore>
                <document>klevu_order_sync</document>
            </ignore>
            <ignore>
                <document>klevu_product_sync</document>
            </ignore>
            <ignore>
                <document>inchoo_event</document>
            </ignore>
            <ignore>
                <document>inchoo_event_ticket</document>
            </ignore>
            <ignore>
                <document>inchoo_productribbon_strikethrough</document>
            </ignore>
            <ignore>
                <document>adminnotification_inbox</document>
            </ignore>
            <!--ignore>
                <document>inchoo_*</document>
            </ignore-->
            <ignore>
                <document>catalog_product_*</document>
            </ignore>
            <ignore>
                <document>catalogrule_*</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_cms_page</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_filter_setting</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_group_attr</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_group_attr_option</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_group_attr_value</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_option_setting</document>
            </ignore>
            <ignore>
                <document>amasty_amshopby_page_store</document>
            </ignore>
            <!--ignore>
                <document>cms_*</document>
            </ignore-->
            <ignore>
                <document>astound_*</document>
            </ignore>
            <ignore>
                <document>bitpay_transactions</document>
            </ignore>
            <ignore>
                <document>emarsys_events_data</document>
            </ignore>
            <ignore>
                <document>powerreviews_reviewdisplay</document>
            </ignore>
            <ignore>
                <document>rw_*</document>
            </ignore>
            <ignore>
                <document>stripe_*</document>
            </ignore>
            <ignore>
                <document>tax_nexus</document>
            </ignore>
            <ignore>
                <document>fedexcrossborder_status_history</document>
            </ignore>
            <ignore>
                <document>inchoo_cnc_store</document>
            </ignore>
            <ignore>
                <document>inchoo_header_banners</document>
            </ignore>
            <ignore>
                <document>mageplaza_smtp_log</document>
            </ignore>
            <!-- INCHOO END; -->
            <ignore>
                <document>admin_analytics_usage_version_log</document>
            </ignore>
            <ignore>
                <document>media_gallery_asset</document>
            </ignore>
            <ignore>
                <document>media_gallery_asset_keyword</document>
            </ignore>
            <ignore>
                <document>media_gallery_keyword</document>
            </ignore>
            <ignore>
                <document>queue_poison_pill</document>
            </ignore>
            <ignore>
                <document>adobe_stock_asset</document>
            </ignore>
            <ignore>
                <document>adobe_stock_category</document>
            </ignore>
            <ignore>
                <document>adobe_stock_creator</document>
            </ignore>
            <ignore>
                <document>adobe_user_profile</document>
            </ignore>
            <ignore>
                <document>scconnector_google_feed_cl</document>
            </ignore>
            <ignore>
                <document>scconnector_google_remove_cl</document>
            </ignore>
            <ignore>
                <document>temando_product_attribute_mapping</document>
            </ignore>
            <ignore>
                <document>vertex_custom_option_flex_field</document>
            </ignore>
            <ignore>
                <document>yotpo_rich_snippets</document>
            </ignore>
            <ignore>
                <document>yotpo_sync</document>
            </ignore>
            <ignore>
                <document>magento_acknowledged_bulk</document>
            </ignore>
            <ignore>
                <document>magento_bulk</document>
            </ignore>
            <ignore>
                <document>magento_operation</document>
            </ignore>
            <ignore>
                <document>signifyd_case</document>
            </ignore>
            <ignore>
                <document>ui_bookmark</document>
            </ignore>
            <ignore>
                <document>migration_backup_*</document>
            </ignore>
            <ignore>
                <document>googleoptimizer_code</document>
            </ignore>
            <ignore>
                <document>indexer_state</document>
            </ignore>
            <ignore>
                <document>integration</document>
            </ignore>
            <ignore>
                <document>mview_state</document>
            </ignore>
            <ignore>
                <document>theme</document>
            </ignore>
            <ignore>
                <document>theme_file</document>
            </ignore>
            <ignore>
                <document>vde_theme_change</document>
            </ignore>
            <ignore>
                <document>admin_system_messages</document>
            </ignore>
            <ignore>
                <document>catalog_url_rewrite_product_category</document>
            </ignore>
            <ignore>
                <document>customer_visitor</document>
            </ignore>
            <ignore>
                <document>url_rewrite</document>
            </ignore>
            <ignore>
                <document>layout_update</document>
            </ignore>
            <ignore>
                <document>layout_link</document>
            </ignore>
            <ignore>
                <document>catalog_product_flat_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_*</document>
            </ignore>
            <ignore>
                <document>catalogindex_eav</document>
            </ignore>
            <ignore>
                <document>catalog_eav_attribute</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_index_default</document>
            </ignore>
            <ignore>
                <document>customer_eav_attribute</document>
            </ignore>
            <ignore>
                <document>customer_eav_attribute_website</document>
            </ignore>
            <ignore>
                <document>catalog_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_product_frontend_action</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_replica</document>
            </ignore>
            <ignore>
                <document>catalogrule_group_website_replica</document>
            </ignore>
            <ignore>
                <document>catalogrule_product_price_replica</document>
            </ignore>
            <ignore>
                <document>catalogrule_product_replica</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_product_replica</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_replica</document>
            </ignore>
            <ignore>
                <document>eav_attribute</document>
            </ignore>
            <ignore>
                <document>eav_attribute_group</document>
            </ignore>
            <ignore>
                <document>eav_attribute_set</document>
            </ignore>
            <ignore>
                <document>eav_entity_attribute</document>
            </ignore>
            <ignore>
                <document>eav_entity_type</document>
            </ignore>
            <ignore>
                <document>authorization_role</document>
            </ignore>
            <ignore>
                <document>authorization_rule</document>
            </ignore>
            <ignore>
                <document>setup_module</document>
            </ignore>
            <ignore>
                <document>sales_order</document>
            </ignore>
            <ignore>
                <document>cache</document>
            </ignore>
            <ignore>
                <document>cache_tag</document>
            </ignore>
            <ignore>
                <document>customer_log</document>
            </ignore>
            <ignore>
                <document>flag</document>
            </ignore>
            <ignore>
                <document>log_customer</document>
            </ignore>
            <ignore>
                <document>log_quote</document>
            </ignore>
            <ignore>
                <document>log_summary_type</document>
            </ignore>
            <ignore>
                <document>log_summary</document>
            </ignore>
            <ignore>
                <document>log_url</document>
            </ignore>
            <ignore>
                <document>log_url_info</document>
            </ignore>
            <ignore>
                <document>quote_id_mask</document>
            </ignore>
            <ignore>
                <document>session</document>
            </ignore>
            <ignore>
                <document>sales_sequence_profile</document>
            </ignore>
            <ignore>
                <document>sales_sequence_meta</document>
            </ignore>
            <ignore>
                <document>sequence_invoice_*</document>
            </ignore>
            <ignore>
                <document>sequence_creditmemo_*</document>
            </ignore>
            <ignore>
                <document>sequence_order_*</document>
            </ignore>
            <ignore>
                <document>sequence_shipment*</document>
            </ignore>
            <ignore>
                <document>sequence_rma_item_*</document>
            </ignore>
            <ignore>
                <document>magento_rma_item_eav_attribute</document>
            </ignore>
            <ignore>
                <document>magento_rma_item_eav_attribute_website</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_product</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_product_tmp</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_tmp</document>
            </ignore>
            <ignore>
                <document>magento_customercustomattributes_sales_flat_order</document>
            </ignore>
            <ignore>
                <document>magento_customercustomattributes_sales_flat_order_address</document>
            </ignore>
            <ignore>
                <document>magento_customercustomattributes_sales_flat_quote</document>
            </ignore>
            <ignore>
                <document>magento_customercustomattributes_sales_flat_quote_address</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index_crosssell</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index_crosssell_product</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index_related</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index_related_product</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index_upsell</document>
            </ignore>
            <ignore>
                <document>magento_targetrule_index_upsell_product</document>
            </ignore>
            <ignore>
                <document>quote_id_mask</document>
            </ignore>
            <ignore>
                <document>eav_attribute_option_swatch</document>
            </ignore>
            <ignore>
                <document>import_history</document>
            </ignore>
            <ignore>
                <document>magento_sales_creditmemo_grid_archive</document>
            </ignore>
            <ignore>
                <document>magento_sales_invoice_grid_archive</document>
            </ignore>
            <ignore>
                <document>magento_sales_order_grid_archive</document>
            </ignore>
            <ignore>
                <document>magento_sales_shipment_grid_archive</document>
            </ignore>
            <ignore>
                <document>sales_creditmemo_grid</document>
            </ignore>
            <ignore>
                <document>sales_invoice_grid</document>
            </ignore>
            <ignore>
                <document>sales_order_grid</document>
            </ignore>
            <ignore>
                <document>sales_shipment_grid</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_scope*</document>
            </ignore>
            <ignore>
                <document>catalogsearch_recommendations</document>
            </ignore>
            <ignore>
                <document>customer_entity</document>
            </ignore>
            <ignore>
                <document>customer_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_entity_varchar</document>
            </ignore>
            <ignore>
                <document>customer_address_entity</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_varchar</document>
            </ignore>
            <ignore>
                <document>store</document>
            </ignore>
            <ignore>
                <document>store_group</document>
            </ignore>
            <ignore>
                <document>store_website</document>
            </ignore>
            <ignore>
                <document>support_report</document>
            </ignore>
            <ignore>
                <document>log_visitor_info</document>
            </ignore>
            <ignore>
                <document>log_visitor_online</document>
            </ignore>
            <ignore>
                <document>reporting_module_status</document>
            </ignore>
            <ignore>
                <document>reporting_orders</document>
            </ignore>
            <ignore>
                <document>reporting_system_updates</document>
            </ignore>
            <ignore>
                <document>reporting_users</document>
            </ignore>
            <ignore>
                <document>reporting_counts</document>
            </ignore>
            <ignore>
                <document>queue</document>
            </ignore>
            <ignore>
                <document>queue_message</document>
            </ignore>
            <ignore>
                <document>queue_message_status</document>
            </ignore>
            <ignore>
                <document>msp_tfa_country_codes</document>
            </ignore>
            <ignore>
                <document>msp_tfa_trusted</document>
            </ignore>
            <ignore>
                <document>msp_tfa_user_config</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_media_gallery_value_to_entity</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_media_gallery_value_video</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_tier_price</document>
            </ignore>
            <ignore>
                <document>customer_grid_flat</document>
            </ignore>
            <ignore>
                <document>visual_merchandiser_rule</document>
            </ignore>
            <ignore>
                <document>admin_passwords</document>
            </ignore>
            <ignore>
                <document>oauth_token_request_log</document>
            </ignore>
            <ignore>
                <document>design_config_grid_flat</document>
            </ignore>
            <ignore>
                <document>release_notification_viewer_log</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_attribute_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_category_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_price_cl</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_cl</document>
            </ignore>
            <ignore>
                <document>catalogrule_product_cl</document>
            </ignore>
            <ignore>
                <document>catalogrule_rule_cl</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_cl</document>
            </ignore>
            <ignore>
                <document>customer_dummy_cl</document>
            </ignore>
            <ignore>
                <document>design_config_dummy_cl</document>
            </ignore>
            <ignore>
                <document>admin_user_session</document>
            </ignore>
            <ignore>
                <document>magento_salesrule_filter</document>
            </ignore>
            <ignore>
                <document>multiinventory_warehouse</document>
            </ignore>
            <ignore>
                <document>password_reset_request_event</document>
            </ignore>
            <ignore>
                <document>queue_lock</document>
            </ignore>
            <ignore>
                <document>quote_preview</document>
            </ignore>
            <ignore>
                <document>search_synonyms</document>
            </ignore>
            <ignore>
                <document>email_automation</document>
            </ignore>
            <ignore>
                <document>email_campaign</document>
            </ignore>
            <ignore>
                <document>email_catalog</document>
            </ignore>
            <ignore>
                <document>email_contact</document>
            </ignore>
            <ignore>
                <document>email_importer</document>
            </ignore>
            <ignore>
                <document>email_order</document>
            </ignore>
            <ignore>
                <document>email_review</document>
            </ignore>
            <ignore>
                <document>email_rules</document>
            </ignore>
            <ignore>
                <document>email_wishlist</document>
            </ignore>
            <ignore>
                <document>email_abandoned_cart</document>
            </ignore>
            <ignore>
                <document>email_contact_consent</document>
            </ignore>
            <ignore>
                <document>email_failed_auth</document>
            </ignore>
            <ignore>
                <document>temando_checkout_address</document>
            </ignore>
            <ignore>
                <document>temando_order</document>
            </ignore>
            <ignore>
                <document>temando_shipment</document>
            </ignore>
            <ignore>
                <document>temando_collection_point_search</document>
            </ignore>
            <ignore>
                <document>temando_order_collection_point</document>
            </ignore>
            <ignore>
                <document>temando_quote_collection_point</document>
            </ignore>
            <ignore>
                <document>temando_order_pickup_location</document>
            </ignore>
            <ignore>
                <document>temando_pickup_location_search</document>
            </ignore>
            <ignore>
                <document>temando_quote_pickup_location</document>
            </ignore>
            <ignore>
                <document>temando_rma_shipment</document>
            </ignore>
            <ignore>
                <document>amazon_customer</document>
            </ignore>
            <ignore>
                <document>amazon_pending_authorization</document>
            </ignore>
            <ignore>
                <document>amazon_pending_capture</document>
            </ignore>
            <ignore>
                <document>amazon_pending_refund</document>
            </ignore>
            <ignore>
                <document>amazon_quote</document>
            </ignore>
            <ignore>
                <document>amazon_sales_order</document>
            </ignore>
            <ignore>
                <document>klarna_core_order</document>
            </ignore>
            <ignore>
                <document>klarna_payments_quote</document>
            </ignore>
            <ignore>
                <document>vertex_customer_code</document>
            </ignore>
            <ignore>
                <document>vertex_invoice_sent</document>
            </ignore>
            <ignore>
                <document>vertex_taxrequest</document>
            </ignore>
            <ignore>
                <document>vertex_order_invoice_status</document>
            </ignore>
            <ignore>
                <document>vertex_sales_creditmemo_item_invoice_text_code</document>
            </ignore>
            <ignore>
                <document>vertex_sales_creditmemo_item_tax_code</document>
            </ignore>
            <ignore>
                <document>vertex_sales_creditmemo_item_vertex_tax_code</document>
            </ignore>
            <ignore>
                <document>vertex_sales_order_item_invoice_text_code</document>
            </ignore>
            <ignore>
                <document>vertex_sales_order_item_tax_code</document>
            </ignore>
            <ignore>
                <document>vertex_sales_order_item_vertex_tax_code</document>
            </ignore>
            <ignore>
                <document>sequence_catalog_category</document>
            </ignore>
            <ignore>
                <document>sequence_catalogrule</document>
            </ignore>
            <ignore>
                <document>sequence_cms_block</document>
            </ignore>
            <ignore>
                <document>sequence_cms_page</document>
            </ignore>
            <ignore>
                <document>sequence_product</document>
            </ignore>
            <ignore>
                <document>sequence_salesrule</document>
            </ignore>
            <ignore>
                <document>sequence_product_bundle_selection</document>
            </ignore>
            <ignore>
                <document>sequence_product_bundle_option</document>
            </ignore>
            <ignore>
                <document>staging_update</document>
            </ignore>
            <ignore>
                <document>vault_payment_token</document>
            </ignore>
            <ignore>
                <document>vault_payment_token_order_payment_link</document>
            </ignore>
            <ignore>
                <document>company</document>
            </ignore>
            <ignore>
                <document>company_advanced_customer_entity</document>
            </ignore>
            <ignore>
                <document>company_credit</document>
            </ignore>
            <ignore>
                <document>company_credit_history</document>
            </ignore>
            <ignore>
                <document>company_order_entity</document>
            </ignore>
            <ignore>
                <document>company_payment</document>
            </ignore>
            <ignore>
                <document>company_permissions</document>
            </ignore>
            <ignore>
                <document>company_roles</document>
            </ignore>
            <ignore>
                <document>company_structure</document>
            </ignore>
            <ignore>
                <document>company_team</document>
            </ignore>
            <ignore>
                <document>company_user_roles</document>
            </ignore>
            <ignore>
                <document>negotiable_quote</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_comment</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_comment_attachment</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_company_config</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_grid</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_history</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_item</document>
            </ignore>
            <ignore>
                <document>negotiable_quote_purged_content</document>
            </ignore>
            <ignore>
                <document>requisition_list</document>
            </ignore>
            <ignore>
                <document>requisition_list_item</document>
            </ignore>
            <ignore>
                <document>shared_catalog</document>
            </ignore>
            <ignore>
                <document>shared_catalog_product_item</document>
            </ignore>
            <ignore>
                <document>sharedcatalog_category_permissions</document>
            </ignore>
            <ignore>
                <document>patch_list</document>
            </ignore>
            <ignore>
                <document>inventory_low_stock_notification_configuration</document>
            </ignore>
            <ignore>
                <document>inventory_reservation</document>
            </ignore>
            <ignore>
                <document>inventory_shipment_source</document>
            </ignore>
            <ignore>
                <document>inventory_source</document>
            </ignore>
            <ignore>
                <document>inventory_source_carrier_link</document>
            </ignore>
            <ignore>
                <document>inventory_source_item</document>
            </ignore>
            <ignore>
                <document>inventory_source_stock_link</document>
            </ignore>
            <ignore>
                <document>inventory_stock</document>
            </ignore>
            <ignore>
                <document>inventory_stock_*</document>
            </ignore>
            <ignore>
                <document>inventory_geoname</document>
            </ignore>
        </document_rules>
        <field_rules>
            <transform>
                <field>admin_user.interface_locale</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="en_US" />
                </handler>
            </transform>
            <transform>
                <field>cataloginventory_stock.website_id</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0" />
                </handler>
            </transform>
            <transform>
                <field>cataloginventory_stock_item.website_id</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0" />
                </handler>
            </transform>
            <transform>
                <field>rating.is_active</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0" />
                </handler>
            </transform>
            <transform>
                <field>authorization_role.user_type</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="2" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.base_amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.real_amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.real_base_amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.taxable_item_type</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="product" />
                </handler>
            </transform>
            <transform>
                <field>oauth_token.user_type</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="1" />
                </handler>
            </transform>
            <transform>
                <field>widget_instance.theme_id</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="3" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_status_state.visible_on_front</field>
                <handler class="\Migration\Handler\SalesOrderStatusState\SetVisibleOnFront" />
            </transform>
            <transform>
                <field>magento_banner_content.banner_content</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>catalog_product_entity_media_gallery_value.row_id</field>
                <handler class="\Migration\Handler\Gallery\SetEntityId" />
            </transform>
            <transform>
                <field>catalog_category_entity.row_id</field>
                <handler class="\Migration\Handler\FieldCopy">
                    <param name="fieldCopy" value="entity_id" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_entity.row_id</field>
                <handler class="\Migration\Handler\FieldCopy">
                    <param name="fieldCopy" value="entity_id" />
                </handler>
            </transform>
            <transform>
                <field>catalogrule.row_id</field>
                <handler class="\Migration\Handler\FieldCopy">
                    <param name="fieldCopy" value="rule_id" />
                </handler>
            </transform>
            <transform>
                <field>cms_block.row_id</field>
                <handler class="\Migration\Handler\FieldCopy">
                    <param name="fieldCopy" value="block_id" />
                </handler>
            </transform>
            <transform>
                <field>cms_page.row_id</field>
                <handler class="\Migration\Handler\FieldCopy">
                    <param name="fieldCopy" value="page_id" />
                </handler>
            </transform>
            <transform>
                <field>salesrule.row_id</field>
                <handler class="\Migration\Handler\FieldCopy">
                    <param name="fieldCopy" value="rule_id" />
                </handler>
            </transform>
            <transform>
                <field>magento_catalogevent_event.status</field>
                <handler class="\Migration\Handler\GetEventStatus" />
            </transform>
            <transform>
                <field>catalog_product_bundle_option_value.parent_product_id</field>
                <handler class="\Migration\Handler\BundleParentProduct">
                    <param name="parentField" value="option_id" />
                    <param name="documentWithProductId" value="catalog_product_bundle_option" />
                    <param name="fieldWithProductId" value="parent_id" />
                </handler>
            </transform>
            <transform>
                <field>catalog_product_bundle_selection_price.parent_product_id</field>
                <handler class="\Migration\Handler\BundleParentProduct">
                    <param name="parentField" value="selection_id" />
                    <param name="documentWithProductId" value="catalog_product_bundle_selection" />
                    <param name="fieldWithProductId" value="parent_product_id" />
                </handler>
            </transform>
            <transform>
                <field>product_alert_price.store_id</field>
                <handler class="\Migration\Handler\ProductAlertStoreId" />
            </transform>
            <transform>
                <field>product_alert_stock.store_id</field>
                <handler class="\Migration\Handler\ProductAlertStoreId" />
            </transform>
            <transform>
                <field>quote_address_item.store_id</field>
                <handler class="\Migration\Handler\QuoteMultiAddressStoreId" />
            </transform>
            <!-- INCHOO  -->
            <transform>
                <field>amasty_amshopby_page.seo_robots</field>
                <handler class="\Inchoo\Migration\Handler\Amasty\Shopby\Page\Column\SeoRobots" />
            </transform>
            <!-- INCHOO END -->
            <ignore>
                <field>quote_address_item.store_id</field>
            </ignore>
            <!-- INCHOO -->
            <ignore>
                <field>amasty_amshopby_page.position</field>
            </ignore>
            <ignore>
                <field>amasty_amshopby_page.image</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.tj_salestax_sync_date</field>
            </ignore>
            <ignore>
                <field>amasty_amshopby_page.seo_robots</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_order_number</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_tracking</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_status_code</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_status_date</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_landedcosttransactionid</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_subtotal</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_duty</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_tax</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_shipping</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_domestic</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_insurance</field>
            </ignore>
            <ignore>
                <field>quote.crossborder_totals_total</field>
            </ignore>
            <!-- INCHOO END -->
            <ignore>
                <field>product_alert_price.store_id</field>
            </ignore>
            <ignore>
                <field>product_alert_stock.store_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery_value.entity_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery.media_type</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery.disabled</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery_value.record_id</field>
            </ignore>
            <ignore>
                <field>admin_user.interface_locale</field>
            </ignore>
            <ignore>
                <field>rating.is_active</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.send_email</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.customer_note</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.customer_note_notify</field>
            </ignore>
            <ignore>
                <field>sales_invoice.send_email</field>
            </ignore>
            <ignore>
                <field>sales_invoice.customer_note</field>
            </ignore>
            <ignore>
                <field>sales_invoice.customer_note_notify</field>
            </ignore>
            <ignore>
                <field>sales_shipment.send_email</field>
            </ignore>
            <ignore>
                <field>sales_shipment.customer_note</field>
            </ignore>
            <ignore>
                <field>sales_shipment.customer_note_notify</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo_item.tax_ratio</field>
            </ignore>
            <ignore>
                <field>sales_invoice_item.tax_ratio</field>
            </ignore>
            <ignore>
                <field>authorization_role.user_type</field>
            </ignore>
            <ignore>
                <field>cataloginventory_stock.website_id</field>
            </ignore>
            <ignore>
                <field>cataloginventory_stock_item.website_id</field>
            </ignore>
            <ignore>
                <field>oauth_nonce.consumer_id</field>
            </ignore>
            <ignore>
                <field>oauth_token.user_type</field>
            </ignore>
            <ignore>
                <field>sales_order_status_state.visible_on_front</field>
            </ignore>
            <ignore>
                <field>widget_instance.theme_id</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.base_amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.real_amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.real_base_amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.associated_item_id</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.taxable_item_type</field>
            </ignore>
            <ignore>
                <field>magento_banner.is_ga_enabled</field>
            </ignore>
            <ignore>
                <field>magento_banner.ga_creative</field>
            </ignore>
            <ignore>
                <field>checkout_agreement.mode</field>
            </ignore>
            <ignore>
                <field>cataloginventory_stock_item.deferred_stock_update</field>
            </ignore>
            <ignore>
                <field>cataloginventory_stock_item.use_config_deferred_stock_update</field>
            </ignore>
            <ignore>
                <field>quote.gw_base_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote.gw_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote.gw_items_base_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote.gw_items_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote.gw_card_base_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote.gw_card_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_address.gw_base_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_address.gw_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_address.gw_items_base_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_address.gw_items_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_address.gw_card_base_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_address.gw_card_price_incl_tax</field>
            </ignore>
            <ignore>
                <field>quote_item.is_excluded_product</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity.created_in</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity.updated_in</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity.created_in</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity.updated_in</field>
            </ignore>
            <ignore>
                <field>catalogrule.created_in</field>
            </ignore>
            <ignore>
                <field>catalogrule.updated_in</field>
            </ignore>
            <ignore>
                <field>cms_block.created_in</field>
            </ignore>
            <ignore>
                <field>cms_block.updated_in</field>
            </ignore>
            <ignore>
                <field>cms_page.created_in</field>
            </ignore>
            <ignore>
                <field>cms_page.updated_in</field>
            </ignore>
            <ignore>
                <field>salesrule.created_in</field>
            </ignore>
            <ignore>
                <field>salesrule.updated_in</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity.row_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_product.entity_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity.row_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery_value.row_id</field>
            </ignore>
            <ignore>
                <field>catalogrule.row_id</field>
            </ignore>
            <ignore>
                <field>admin_user.refresh_token</field>
            </ignore>
            <ignore>
                <field>salesrule_coupon.generated_by_dotmailer</field>
            </ignore>
            <ignore>
                <field>quote_address.validated_country_code</field>
            </ignore>
            <ignore>
                <field>quote_address.validated_vat_number</field>
            </ignore>
            <ignore>
                <field>cms_block.row_id</field>
            </ignore>
            <ignore>
                <field>cms_page.row_id</field>
            </ignore>
            <ignore>
                <field>salesrule.row_id</field>
            </ignore>
            <ignore>
                <field>cms_page.meta_title</field>
            </ignore>
            <ignore>
                <field>magento_catalogevent_event.status</field>
            </ignore>
            <ignore>
                <field>catalog_product_bundle_selection_price.parent_product_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_bundle_option_value.parent_product_id</field>
            </ignore>
            <ignore>
                <field>cms_page.layout_update_selected</field>
            </ignore>
            <ignore>
                <field>email_template.is_legacy</field>
            </ignore>
            <ignore>
                <field>newsletter_template.is_legacy</field>
            </ignore>
            <ignore>
                <field>core_config_data.updated_at</field>
            </ignore>
            <ignore>
                <datatype>quote_payment.cc_exp_month</datatype>
            </ignore>
            <ignore>
                <datatype>weee_tax.state</datatype>
            </ignore>
            <ignore>
                <datatype>magento_logging_event.ip</datatype>
            </ignore>
            <ignore>
                <datatype>magento_logging_event.x_forwarded_ip</datatype>
            </ignore>
            <ignore>
                <datatype>rating_option_vote.remote_ip_long</datatype>
            </ignore>
            <ignore>
                <datatype>sendfriend_log.ip</datatype>
            </ignore>
            <ignore>
                <datatype>magento_catalogevent_event.date_start</datatype>
            </ignore>
            <ignore>
                <datatype>magento_catalogevent_event.date_end</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.rejected_callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_token.callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>magento_reminder_rule.from_date</datatype>
            </ignore>
            <ignore>
                <datatype>magento_reminder_rule.to_date</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_invitation.group_id</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_logging_event.info</datatype>
            </ignore>
            <ignore>
                <datatype>catalog_product_bundle_price_index.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalog_product_index_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalog_product_index_tier_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_group_website.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>enterprise_catalogpermissions.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>magento_catalogpermissions.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>magento_invitation.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>magento_logging_event.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>magento_reward_rate.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_product_attribute.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>sales_order.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>magento_invitation.group_id</datatype>
            </ignore>
            <ignore>
                <datatype>magento_logging_event.info</datatype>
            </ignore>
            <ignore>
                <datatype>paypal_settlement_report.report_date</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_coupon.expiration_date</datatype>
            </ignore>
            <!-- INCHOO START -->
            <ignore>
                <datatype>amasty_amshopby_page.page_id</datatype>
            </ignore>
            <ignore>
                <datatype>amasty_amshopby_page.meta_keywords</datatype>
            </ignore>
            <ignore>
                <datatype>amasty_amshopby_page.conditions</datatype>
            </ignore>
            <ignore>
                <datatype>amasty_amshopby_page.categories</datatype>
            </ignore>
            <ignore>
                <datatype>amasty_amshopby_page.top_block_id</datatype>
            </ignore>
            <ignore>
                <datatype>amasty_amshopby_page.bottom_block_id</datatype>
            </ignore>
            <ignore>
                <datatype>tax_class.tj_salestax_code</datatype>
            </ignore>
            <ignore>
                <datatype>amasty_amshopby_page.seo_sitemap</datatype>
            </ignore>
            <!-- INCHOO END -->
        </field_rules>
    </destination>
</map>
