<?xml version="1.0" encoding="UTF-8"?>
<map xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="../map.xsd">
    <source>
        <field_rules>
            <ignore>
                <field>customer_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_entity.attribute_set_id</field>
            </ignore>
            <ignore>
                <field>customer_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_entity_varchar.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.attribute_set_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity_varchar.entity_type_id</field>
            </ignore>
        </field_rules>
    </source>
    <destination>
        <field_rules>
            <ignore>
                <field>customer_entity.created_in</field>
            </ignore>
            <ignore>
                <field>customer_entity.prefix</field>
            </ignore>
            <ignore>
                <field>customer_entity.firstname</field>
            </ignore>
            <ignore>
                <field>customer_entity.middlename</field>
            </ignore>
            <ignore>
                <field>customer_entity.lastname</field>
            </ignore>
            <ignore>
                <field>customer_entity.suffix</field>
            </ignore>
            <ignore>
                <field>customer_entity.dob</field>
            </ignore>
            <ignore>
                <field>customer_entity.password_hash</field>
            </ignore>
            <ignore>
                <field>customer_entity.rp_token</field>
            </ignore>
            <ignore>
                <field>customer_entity.rp_token_created_at</field>
            </ignore>
            <ignore>
                <field>customer_entity.default_billing</field>
            </ignore>
            <ignore>
                <field>customer_entity.default_shipping</field>
            </ignore>
            <ignore>
                <field>customer_entity.taxvat</field>
            </ignore>
            <ignore>
                <field>customer_entity.confirmation</field>
            </ignore>
            <ignore>
                <field>customer_entity.gender</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.city</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.company</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.country_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.fax</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.firstname</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.lastname</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.middlename</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.postcode</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.prefix</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.region</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.region_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.street</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.suffix</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.telephone</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.vat_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.vat_is_valid</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.vat_request_date</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.vat_request_id</field>
            </ignore>
            <ignore>
                <field>customer_address_entity.vat_request_success</field>
            </ignore>
            <ignore>
                <field>customer_entity.failures_num</field>
            </ignore>
            <ignore>
                <field>customer_entity.first_failure</field>
            </ignore>
            <ignore>
                <field>customer_entity.lock_expires</field>
            </ignore>
            <!-- INCHOO START -->
            <ignore>
                <field>customer_entity.stripe_customer_id</field>
            </ignore>
            <!-- INCHOO END -->
        </field_rules>
    </destination>
</map>
