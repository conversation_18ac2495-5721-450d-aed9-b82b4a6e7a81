<?xml version="1.0" encoding="UTF-8"?>
<map xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="../../map.xsd">
    <source>
        <document_rules>
            <ignore>
                <document>catalog_product_entity_media_gallery_value</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_media_gallery</document>
            </ignore>
            <ignore>
                <document>gaussdev_brand_brand</document>
            </ignore>
            <ignore>
                <document>gaussdev_fitguide_guide</document>
            </ignore>
            <ignore>
                <document>gaussdev_fitguide_guide_store</document>
            </ignore>
            <ignore>
                <document>gaussdev_import_tmp</document>
            </ignore>
            <ignore>
                <document>gaussdev_import_tmp_fast</document>
            </ignore>
            <ignore>
                <document>gaussdev_import_tmp_stock</document>
            </ignore>
            <ignore>
                <document>gaussdev_store_location</document>
            </ignore>
            <ignore>
                <document>gaussdev_store_location_content</document>
            </ignore>
            <ignore>
                <document>gaussdev_store_location_stock</document>
            </ignore>
            <ignore>
                <document>ho_import_entity</document>
            </ignore>
            <ignore>
                <document>mailchimp_ecommerce_sync_data</document>
            </ignore>
            <ignore>
                <document>mailchimp_errors</document>
            </ignore>
            <ignore>
                <document>mailchimp_sync_batches</document>
            </ignore>
            <ignore>
                <document>mailchimp_webhook_request</document>
            </ignore>
            <ignore>
                <document>paymentemail</document>
            </ignore>
            <ignore>
                <document>shipping_matrixrate</document>
            </ignore>
            <ignore>
                <document>slideshow,smtppro_email_log</document>
            </ignore>
            <ignore>
                <document>storelocator</document>
            </ignore>
            <ignore>
                <document>wordpress_association</document>
            </ignore>
            <ignore>
                <document>wordpress_association_type</document>
            </ignore>
            <ignore>
                <document>wp_commentmeta</document>
            </ignore>
            <ignore>
                <document>wp_comments</document>
            </ignore>
            <ignore>
                <document>wp_links</document>
            </ignore>
            <ignore>
                <document>wp_options</document>
            </ignore>
            <ignore>
                <document>wp_postmeta</document>
            </ignore>
            <ignore>
                <document>wp_posts</document>
            </ignore>
            <ignore>
                <document>wp_term_relationships</document>
            </ignore>
            <ignore>
                <document>wp_term_taxonomy</document>
            </ignore>
            <ignore>
                <document>wp_termmeta</document>
            </ignore>
            <ignore>
                <document>wp_terms</document>
            </ignore>
            <ignore>
                <document>wp_usermeta</document>
            </ignore>
            <ignore>
                <document>wp_users</document>
            </ignore>
            <ignore>
                <document>slideshow</document>
            </ignore>
            <ignore>
                <document>smtppro_email_log</document>
            </ignore>
            <ignore>
                <document>googleshopping_attributes</document>
            </ignore>
            <ignore>
                <document>googleshopping_items</document>
            </ignore>
            <ignore>
                <document>googleshopping_types</document>
            </ignore>
            <ignore>
                <document>sales_flat_creditmemo_grid</document>
            </ignore>
            <ignore>
                <document>sales_flat_invoice_grid</document>
            </ignore>
            <ignore>
                <document>sales_flat_order_grid</document>
            </ignore>
            <ignore>
                <document>sales_flat_shipment_grid</document>
            </ignore>
            <ignore>
                <document>customer_entity</document>
            </ignore>
            <ignore>
                <document>customer_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_entity_varchar</document>
            </ignore>
            <ignore>
                <document>customer_address_entity</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_varchar</document>
            </ignore>
            <ignore>
                <document>m2_cl_*</document>
            </ignore>
            <ignore>
                <document>url_rewrite_m2*</document>
            </ignore>
            <ignore>
                <document>admin_assert</document>
            </ignore>
            <ignore>
                <document>api2_acl_attribute</document>
            </ignore>
            <ignore>
                <document>api2_acl_role</document>
            </ignore>
            <ignore>
                <document>api2_acl_rule</document>
            </ignore>
            <ignore>
                <document>api2_acl_user</document>
            </ignore>
            <ignore>
                <document>api_assert</document>
            </ignore>
            <ignore>
                <document>api_role</document>
            </ignore>
            <ignore>
                <document>api_rule</document>
            </ignore>
            <ignore>
                <document>api_session</document>
            </ignore>
            <ignore>
                <document>api_user</document>
            </ignore>
            <ignore>
                <document>log_customer</document>
            </ignore>
            <ignore>
                <document>log_quote</document>
            </ignore>
            <ignore>
                <document>log_summary_type</document>
            </ignore>
            <ignore>
                <document>log_summary</document>
            </ignore>
            <ignore>
                <document>log_url</document>
            </ignore>
            <ignore>
                <document>log_url_info</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_categs_index_idx</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_categs_index_tmp</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_products_index_idx</document>
            </ignore>
            <ignore>
                <document>catalog_category_anc_products_index_tmp</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_product_enabled_index</document>
            </ignore>
            <ignore>
                <document>catalog_product_flat_cl</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_idx</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_tmp</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_cat_cl</document>
            </ignore>
            <ignore>
                <document>catalog_product_bundle_price_index</document>
            </ignore>
            <ignore>
                <document>catalog_product_bundle_stock_index</document>
            </ignore>
            <ignore>
                <document>catalogindex_aggregation</document>
            </ignore>
            <ignore>
                <document>catalogindex_aggregation_tag</document>
            </ignore>
            <ignore>
                <document>catalogindex_aggregation_to_tag</document>
            </ignore>
            <ignore>
                <document>catalogindex_minimal_price</document>
            </ignore>
            <ignore>
                <document>catalogindex_price</document>
            </ignore>
            <ignore>
                <document>core_layout_update</document>
            </ignore>
            <ignore>
                <document>core_layout_link</document>
            </ignore>
            <ignore>
                <document>widget_instance_page_layout</document>
            </ignore>
            <ignore>
                <document>googlecheckout_api_debug</document>
            </ignore>
            <ignore>
                <document>ogone_api_debug</document>
            </ignore>
            <ignore>
                <document>oscommerce_import</document>
            </ignore>
            <ignore>
                <document>oscommerce_import_type</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders_products</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders_status_history</document>
            </ignore>
            <ignore>
                <document>oscommerce_orders_total</document>
            </ignore>
            <ignore>
                <document>oscommerce_ref</document>
            </ignore>
            <ignore>
                <document>paygate_authorizenet_debug</document>
            </ignore>
            <ignore>
                <document>paypal_api_debug</document>
            </ignore>
            <ignore>
                <document>paypaluk_api_debug</document>
            </ignore>
            <ignore>
                <document>amazonpayments_api_debug</document>
            </ignore>
            <ignore>
                <document>chronopay_api_debug</document>
            </ignore>
            <ignore>
                <document>cybermut_api_debug</document>
            </ignore>
            <ignore>
                <document>cybersource_api_debug</document>
            </ignore>
            <ignore>
                <document>eway_api_debug</document>
            </ignore>
            <ignore>
                <document>flo2cash_api_debug</document>
            </ignore>
            <ignore>
                <document>ideal_api_debug</document>
            </ignore>
            <ignore>
                <document>paybox_api_debug</document>
            </ignore>
            <ignore>
                <document>protx_api_debug</document>
            </ignore>
            <ignore>
            	<document>xmlconnect_application</document>
            </ignore>
            <ignore>
            	<document>xmlconnect_config_data</document>
            </ignore>
            <ignore>
            	<document>xmlconnect_history</document>
            </ignore>
            <ignore>
            	<document>xmlconnect_images</document>
            </ignore>
            <ignore>
            	<document>xmlconnect_notification_template</document>
            </ignore>
            <ignore>
            	<document>xmlconnect_queue</document>
            </ignore>
            <ignore>
                <document>googlecheckout_notification</document>
            </ignore>
            <ignore>
                <document>index_event</document>
            </ignore>
            <ignore>
                <document>index_process</document>
            </ignore>
            <ignore>
                <document>index_process_event</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_cl</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_cl</document>
            </ignore>
            <ignore>
                <document>core_cache_tag</document>
            </ignore>
            <ignore>
                <document>core_cache_option</document>
            </ignore>
            <ignore>
                <document>core_cache</document>
            </ignore>
            <ignore>
                <document>core_flag</document>
            </ignore>
            <ignore>
                <document>core_email_queue</document>
            </ignore>
            <ignore>
                <document>core_email_queue_recipients</document>
            </ignore>
            <ignore>
                <document>merchandiser_category_values</document>
            </ignore>
            <ignore>
                <document>merchandiser_vmbuild</document>
            </ignore>
            <ignore>
                <document>dataflow_batch</document>
            </ignore>
            <ignore>
                <document>dataflow_batch_export</document>
            </ignore>
            <ignore>
                <document>dataflow_batch_import</document>
            </ignore>
            <ignore>
                <document>dataflow_import_data</document>
            </ignore>
            <ignore>
                <document>dataflow_profile</document>
            </ignore>
            <ignore>
                <document>dataflow_profile_history</document>
            </ignore>
            <ignore>
                <document>dataflow_session</document>
            </ignore>
            <ignore>
                <document>core_url_rewrite</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_url_key</document>
            </ignore>
            <ignore>
                <document>catalogindex_eav</document>
            </ignore>
            <ignore>
                <document>catalog_eav_attribute</document>
            </ignore>
            <ignore>
                <document>customer_eav_attribute</document>
            </ignore>
            <ignore>
                <document>eav_attribute</document>
            </ignore>
            <ignore>
                <document>eav_attribute_group</document>
            </ignore>
            <ignore>
                <document>eav_attribute_set</document>
            </ignore>
            <ignore>
                <document>eav_entity_attribute</document>
            </ignore>
            <ignore>
                <document>eav_entity_type</document>
            </ignore>
            <ignore>
                <document>catalog_category_entity_url_key</document>
            </ignore>
            <ignore>
                <document>poll</document>
            </ignore>
            <ignore>
                <document>poll_answer</document>
            </ignore>
            <ignore>
                <document>poll_store</document>
            </ignore>
            <ignore>
                <document>poll_vote</document>
            </ignore>
            <ignore>
                <document>sales_recurring_profile</document>
            </ignore>
            <ignore>
                <document>sales_recurring_profile_order</document>
            </ignore>
            <ignore>
                <document>tag</document>
            </ignore>
            <ignore>
                <document>tag_properties</document>
            </ignore>
            <ignore>
                <document>tag_relation</document>
            </ignore>
            <ignore>
                <document>tag_summary</document>
            </ignore>
            <ignore>
                <document>weee_discount</document>
            </ignore>
            <ignore>
                <document>catalogsearch_result</document>
            </ignore>
            <ignore>
                <document>log_visitor</document>
            </ignore>
            <ignore>
                <document>log_visitor_info</document>
            </ignore>
            <ignore>
                <document>log_visitor_online</document>
            </ignore>
            <ignore>
                <document>core_config_data</document>
            </ignore>
            <ignore>
                <document>s_*b_*_*</document>
            </ignore>
            <ignore>
                <document>catalog_product_flat_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_store_*</document>
            </ignore>
            <ignore>
                <document>googleoptimizer_code</document>
            </ignore>
            <ignore>
                <document>core_resource</document>
            </ignore>
            <ignore>
                <document>cron_schedule</document>
            </ignore>
            <ignore>
                <document>admin_user</document>
            </ignore>
            <ignore>
                <document>admin_role</document>
            </ignore>
            <ignore>
                <document>admin_rule</document>
            </ignore>
            <ignore>
                <document>admin_role</document>
            </ignore>
            <ignore>
                <document>admin_rule</document>
            </ignore>
            <ignore>
                <document>catalogrule_affected_product</document>
            </ignore>
            <ignore>
                <document>catalog_product_super_attribute_pricing</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_group_price</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_tier_price</document>
            </ignore>
            <ignore>
                <document>permission_block</document>
            </ignore>
            <ignore>
                <document>permission_variable</document>
            </ignore>
            <ignore>
                <document>googlebase_attributes</document>
            </ignore>
            <ignore>
                <document>googlebase_items</document>
            </ignore>
            <ignore>
                <document>googlebase_types</document>
            </ignore>
            <ignore>
                <document>core_store</document>
            </ignore>
            <ignore>
                <document>core_store_group</document>
            </ignore>
            <ignore>
                <document>core_website</document>
            </ignore>
            <rename>
                <document>catalogsearch_query</document>
                <to>search_query</to>
            </rename>
            <rename>
                <document>core_variable</document>
                <to>variable</to>
            </rename>
            <rename>
                <document>core_variable_value</document>
                <to>variable_value</to>
            </rename>
            <rename>
                <document>core_email_template</document>
                <to>email_template</to>
            </rename>
            <rename>
                <document>core_translate</document>
                <to>translation</to>
            </rename>
            <rename>
                <document>core_session</document>
                <to>session</to>
            </rename>
            <rename>
                <document>coupon_aggregated</document>
                <to>salesrule_coupon_aggregated</to>
            </rename>
            <rename>
                <document>coupon_aggregated_order</document>
                <to>salesrule_coupon_aggregated_order</to>
            </rename>
            <rename>
                <document>coupon_aggregated_updated</document>
                <to>salesrule_coupon_aggregated_updated</to>
            </rename>
            <rename>
                <document>sales_flat_order</document>
                <to>sales_order</to>
            </rename>
            <rename>
                <document>sales_flat_creditmemo</document>
                <to>sales_creditmemo</to>
            </rename>
            <rename>
                <document>sales_flat_creditmemo_comment</document>
                <to>sales_creditmemo_comment</to>
            </rename>
            <rename>
                <document>sales_flat_creditmemo_item</document>
                <to>sales_creditmemo_item</to>
            </rename>
            <rename>
                <document>sales_flat_invoice</document>
                <to>sales_invoice</to>
            </rename>
            <rename>
                <document>sales_flat_invoice_comment</document>
                <to>sales_invoice_comment</to>
            </rename>
            <rename>
                <document>sales_flat_invoice_item</document>
                <to>sales_invoice_item</to>
            </rename>
            <rename>
                <document>sales_flat_order_address</document>
                <to>sales_order_address</to>
            </rename>
            <rename>
                <document>sales_flat_order_item</document>
                <to>sales_order_item</to>
            </rename>
            <rename>
                <document>sales_flat_order_payment</document>
                <to>sales_order_payment</to>
            </rename>
            <rename>
                <document>sales_flat_order_status_history</document>
                <to>sales_order_status_history</to>
            </rename>
            <rename>
                <document>sales_flat_quote</document>
                <to>quote</to>
            </rename>
            <rename>
                <document>sales_flat_quote_address</document>
                <to>quote_address</to>
            </rename>
            <rename>
                <document>sales_flat_quote_address_item</document>
                <to>quote_address_item</to>
            </rename>
            <rename>
                <document>sales_flat_quote_item</document>
                <to>quote_item</to>
            </rename>
            <rename>
                <document>sales_flat_quote_item_option</document>
                <to>quote_item_option</to>
            </rename>
            <rename>
                <document>sales_flat_quote_payment</document>
                <to>quote_payment</to>
            </rename>
            <rename>
                <document>sales_flat_quote_shipping_rate</document>
                <to>quote_shipping_rate</to>
            </rename>
            <rename>
                <document>sales_flat_shipment</document>
                <to>sales_shipment</to>
            </rename>
            <rename>
                <document>sales_flat_shipment_comment</document>
                <to>sales_shipment_comment</to>
            </rename>
            <rename>
                <document>sales_flat_shipment_item</document>
                <to>sales_shipment_item</to>
            </rename>
            <rename>
                <document>sales_flat_shipment_track</document>
                <to>sales_shipment_track</to>
            </rename>
            <rename>
                <document>core_directory_storage</document>
                <to>media_storage_directory_storage</to>
            </rename>
            <rename>
                <document>core_file_storage</document>
                <to>media_storage_file_storage</to>
            </rename>
            <rename>
                <document>sales_billing_agreement</document>
                <to>paypal_billing_agreement</to>
            </rename>
            <rename>
                <document>sales_billing_agreement_order</document>
                <to>paypal_billing_agreement_order</to>
            </rename>
        </document_rules>
        <field_rules>
            <move>
                <field>cms_page.root_template</field>
                <to>cms_page.page_layout</to>
            </move>
            <move>
                <field>sales_flat_order.hidden_tax_amount</field>
                <to>sales_order.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order.base_hidden_tax_amount</field>
                <to>sales_order.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order.shipping_hidden_tax_amount</field>
                <to>sales_order.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order.base_shipping_hidden_tax_amnt</field>
                <to>sales_order.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_order.hidden_tax_invoiced</field>
                <to>sales_order.discount_tax_compensation_invoiced</to>
            </move>
            <move>
                <field>sales_flat_order.base_hidden_tax_invoiced</field>
                <to>sales_order.base_discount_tax_compensation_invoiced</to>
            </move>
            <move>
                <field>sales_flat_order.hidden_tax_refunded</field>
                <to>sales_order.discount_tax_compensation_refunded</to>
            </move>
            <move>
                <field>sales_flat_order.base_hidden_tax_refunded</field>
                <to>sales_order.base_discount_tax_compensation_refunded</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.hidden_tax_amount</field>
                <to>sales_creditmemo.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.base_hidden_tax_amount</field>
                <to>sales_creditmemo.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.shipping_hidden_tax_amount</field>
                <to>sales_creditmemo.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo.base_shipping_hidden_tax_amnt</field>
                <to>sales_creditmemo.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_creditmemo_item.hidden_tax_amount</field>
                <to>sales_creditmemo_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_creditmemo_item.base_hidden_tax_amount</field>
                <to>sales_creditmemo_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.hidden_tax_amount</field>
                <to>sales_invoice.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.base_hidden_tax_amount</field>
                <to>sales_invoice.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.shipping_hidden_tax_amount</field>
                <to>sales_invoice.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice.base_shipping_hidden_tax_amnt</field>
                <to>sales_invoice.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_invoice_item.hidden_tax_amount</field>
                <to>sales_invoice_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_invoice_item.base_hidden_tax_amount</field>
                <to>sales_invoice_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_amount</field>
                <to>sales_order_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order_item.base_hidden_tax_amount</field>
                <to>sales_order_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_invoiced</field>
                <to>sales_order_item.discount_tax_compensation_invoiced</to>
            </move>
            <move>
                <field>sales_flat_order_item.base_hidden_tax_invoiced</field>
                <to>sales_order_item.base_discount_tax_compensation_invoiced</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_refunded</field>
                <to>sales_order_item.discount_tax_compensation_refunded</to>
            </move>
            <move>
                <field>sales_flat_order_item.base_hidden_tax_refunded</field>
                <to>sales_order_item.base_discount_tax_compensation_refunded</to>
            </move>
            <move>
                <field>sales_flat_order_item.hidden_tax_canceled</field>
                <to>sales_order_item.discount_tax_compensation_canceled</to>
            </move>
            <move>
                <field>sales_flat_order_payment.cc_last4</field>
                <to>sales_order_payment.cc_last_4</to>
            </move>
            <move>
                <field>sales_flat_quote_payment.cc_last4</field>
                <to>quote_payment.cc_last_4</to>
            </move>
            <move>
                <field>sales_flat_quote_address.hidden_tax_amount</field>
                <to>quote_address.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address.base_hidden_tax_amount</field>
                <to>quote_address.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address.shipping_hidden_tax_amount</field>
                <to>quote_address.shipping_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address.base_shipping_hidden_tax_amnt</field>
                <to>quote_address.base_shipping_discount_tax_compensation_amnt</to>
            </move>
            <move>
                <field>sales_flat_quote_address_item.hidden_tax_amount</field>
                <to>quote_address_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_address_item.base_hidden_tax_amount</field>
                <to>quote_address_item.base_discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_item.hidden_tax_amount</field>
                <to>quote_item.discount_tax_compensation_amount</to>
            </move>
            <move>
                <field>sales_flat_quote_item.base_hidden_tax_amount</field>
                <to>quote_item.base_discount_tax_compensation_amount</to>
            </move>
            <transform>
                <field>catalog_category_entity_varchar.value</field>
                <handler class="\Migration\Handler\ConvertEavValue">
                    <param name="map" value="[one_column:1column;two_columns_left:2columns-left;two_columns_right:2columns-right;three_columns:3columns]"/>
                    <param name="attributeCode" value="page_layout"/>
                </handler>
            </transform>
            <transform>
                <field>catalog_category_entity_varchar.value</field>
                <handler class="\Migration\Handler\TruncateSuffix">
                    <param name="suffixPath" value="catalog/seo/category_url_suffix"/>
                    <param name="attributeCodes" value="[url_path]"/>
                    <param name="entityTypeCode" value="catalog_category"/>
                </handler>
            </transform>
            <transform>
                <field>catalog_category_entity_varchar.value</field>
                <handler class="\Migration\Handler\SetValueAttributeCondition">
                     <param name="attributeCode" value="custom_design"/>
                     <param name="value" value="null"/>
                </handler>
            </transform>
            <transform>
                <field>catalog_category_entity_text.value</field>
                <handler class="\Migration\Handler\SetValueAttributeCondition">
                     <param name="attributeCode" value="custom_layout_update"/>
                     <param name="value" value="null"/>
                </handler>
            </transform>
            <transform>
                <field>catalog_product_entity_varchar.value</field>
                <handler class="\Migration\Handler\ConvertEavValue">
                    <param name="map" value="[one_column:1column;two_columns_left:2columns-left;two_columns_right:2columns-right;three_columns:3columns]"/>
                    <param name="attributeCode" value="page_layout"/>
                </handler>
            </transform>
            <transform>
                <field>catalog_product_entity_varchar.value</field>
                <handler class="\Migration\Handler\TruncateSuffix">
                    <param name="suffixPath" value="catalog/seo/product_url_suffix"/>
                    <param name="attributeCodes" value="[url_key,url_path]"/>
                    <param name="entityTypeCode" value="catalog_product"/>
                </handler>
            </transform>
            <transform>
                <field>cms_page.root_template</field>
                <handler class="\Migration\Handler\Convert">
                    <param name="map" value="[one_column:1column;two_columns_left:2columns-left;two_columns_right:2columns-right;three_columns:3columns]" />
                    <param name="defaultValue" value="1column" />
                </handler>
            </transform>
            <transform>
                <field>cms_block.content</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>cms_page.content</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>newsletter_template.template_text</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>core_email_template.template_text</field>
                <handler class="\Migration\Handler\Placeholder" />
            </transform>
            <transform>
                <field>widget_instance.instance_type</field>
                <handler class="\Migration\Handler\ClassMap" />
            </transform>
            <transform>
                <field>catalogrule.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData"/>
            </transform>
            <transform>
                <field>catalogrule.actions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData"/>
            </transform>
            <transform>
                <field>salesrule.conditions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData"/>
            </transform>
            <transform>
                <field>salesrule.actions_serialized</field>
                <handler class="\Migration\Handler\Rule\SerializedData"/>
            </transform>
            <transform>
                <field>sales_flat_order_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_quote_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_creditmemo_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_invoice_item.weee_tax_applied</field>
                <handler class="Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>catalog_product_entity_media_gallery.value_id</field>
                <handler class="\Migration\Handler\Gallery\InsertValueToEntity"/>
            </transform>
            <transform>
                <field>rating_option_vote.remote_ip_long</field>
                <handler class="\Migration\Handler\ConvertIp"/>
            </transform>
            <transform>
                <field>sendfriend_log.ip</field>
                <handler class="\Migration\Handler\ConvertIp"/>
            </transform>
            <transform>
                <field>catalogsearch_query.synonym_for</field>
                <handler class="\Migration\Handler\Synonym"/>
            </transform>
            <transform>
                <field>sales_flat_quote_payment.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_order_item.product_options</field>
                <handler class="\Migration\Handler\SerializeToJson\SalesOrderItem"/>
            </transform>
            <transform>
                <field>sales_flat_order_payment.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_shipment.packages</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_payment_transaction.additional_information</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_quote_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>sales_flat_quote_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption"/>
            </transform>
            <transform>
                <field>wishlist_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ConvertWithConditions">
                    <param name="conditionalField" value="code" />
                    <param name="conditionalFieldValuesPattern" value="/(parameters)|(info_buyRequest)|(attributes)|(bundle_option_ids)|(bundle_selection_ids)|(bundle_selection_attributes)/" />
                </handler>
            </transform>
            <transform>
                <field>wishlist_item_option.value</field>
                <handler class="\Migration\Handler\SerializeToJson\ItemOption"/>
            </transform>
            <transform>
                <field>sales_flat_quote.gift_cards</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_quote_address.applied_taxes</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>sales_flat_quote_address.gift_cards</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>widget_instance.widget_parameters</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>salesrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>salesrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>catalogrule.conditions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>catalogrule.actions_serialized</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <transform>
                <field>admin_user.extra</field>
                <handler class="\Migration\Handler\SerializeToJson"/>
            </transform>
            <ignore>
                <field>newsletter_subscriber.subscriber_firstname</field>
            </ignore>
            <ignore>
                <field>newsletter_subscriber.subscriber_lastname</field>
            </ignore>
            <ignore>
                <field>newsletter_subscriber.mailchimp_sync_delta</field>
            </ignore>
            <ignore>
                <field>newsletter_subscriber.mailchimp_sync_error</field>
            </ignore>
            <ignore>
                <field>newsletter_subscriber.mailchimp_sync_modified</field>
            </ignore>
            <ignore>
                <field>newsletter_subscriber.exit_popup_coupon_code</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.mailchimp_campaign_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.mailchimp_abandonedcart_flag</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.mailchimp_landing_page</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote.mailchimp_abandonedcart_flag</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote.mailchimp_landing_page</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote.mailchimp_campaign_id</field>
            </ignore>
            <ignore>
                <field>salesrule.promo_sku</field>
            </ignore>
            <ignore>
                <field>salesrule.promo_cats</field>
            </ignore>
            <ignore>
                <field>salesrule.each_m</field>
            </ignore>
            <ignore>
                <field>salesrule.buy_x_get_n</field>
            </ignore>
            <ignore>
                <field>salesrule.price_selector</field>
            </ignore>
            <ignore>
                <field>salesrule.max_discount</field>
            </ignore>
            <ignore>
                <field>salesrule.amskip_rule</field>
            </ignore>
            <ignore>
                <field>admin_rule.role_type</field>
            </ignore>
            <ignore>
                <field>admin_rule.assert_id</field>
            </ignore>
            <ignore>
                <field>sales_order_tax.hidden</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.paypal_ipn_customer_notified</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_item.is_nominal</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.paybox_request_number</field>
            </ignore>
            <ignore>
                <field>weee_tax.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_category_entity_varchar.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_datetime.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_decimal.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_gallery.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery.entity_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_int.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_text.entity_type_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_varchar.entity_type_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_creditmemo.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.ideal_transaction_checked</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.ideal_issuer_title</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.paybox_question_number</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.ideal_issuer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.flo2cash_account_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.cybersource_token</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.ideal_issuer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.ideal_issuer_list</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.paypal_payer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.paypal_payer_status</field>
            </ignore>
            <ignore>
                <field>sales_flat_quote_payment.paypal_correlation_id</field>
            </ignore>
            <ignore>
                <field>cms_page.layout_update_xml</field>
            </ignore>
            <ignore>
                <field>cms_page.custom_layout_update_xml</field>
            </ignore>
            <ignore>
                <field>widget_instance.package_theme</field>
            </ignore>
            <ignore>
                <field>newsletter_template.template_text_preprocessed</field>
            </ignore>
            <ignore>
                <field>catalogrule.sub_is_enable</field>
            </ignore>
            <ignore>
                <field>catalogrule.sub_simple_action</field>
            </ignore>
            <ignore>
                <field>catalogrule.sub_discount_amount</field>
            </ignore>
            <ignore>
                <field>catalogrule_product.sub_simple_action</field>
            </ignore>
            <ignore>
                <field>catalogrule_product.sub_discount_amount</field>
            </ignore>
            <ignore>
                <field>catalogsearch_query.synonym_for</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.customer_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.invoice_status_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.invoice_type</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.is_virtual</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.real_order_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.total_due</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice.total_paid</field>
            </ignore>
            <ignore>
                <field>sales_flat_invoice_item.shipment_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.base_custbalance_amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.currency_base_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.currency_code</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.currency_rate</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.custbalance_amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.is_hold</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.is_multi_payment</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.real_order_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.tax_percent</field>
            </ignore>
            <ignore>
                <field>sales_flat_order.tracking_numbers</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_address.address_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_address.gift_message_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_address.tax_id</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.amount</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.cc_raw_request</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.cc_raw_response</field>
            </ignore>
            <ignore>
                <field>sales_flat_order_payment.customer_payment_id</field>
            </ignore>
            <ignore>
                <datatype>sales_flat_quote_payment.cc_exp_month</datatype>
            </ignore>
            <ignore>
                <datatype>weee_tax.state</datatype>
            </ignore>
            <ignore>
                <datatype>sendfriend_log.ip</datatype>
            </ignore>
            <ignore>
                <datatype>rating_option_vote.remote_ip_long</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.rejected_callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_token.callback_url</datatype>
            </ignore>
           <ignore>
               <datatype>catalogrule_customer_group.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_group_website.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_product.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_product_price.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>customer_group.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>paypal_settlement_report.report_date</datatype>
           </ignore>
           <ignore>
               <datatype>sales_flat_order.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>salesrule_customer_group.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>salesrule_product_attribute.customer_group_id</datatype>
           </ignore>
            <ignore>
                <datatype>catalogrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_group_website.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_product_attribute.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>sales_flat_order.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>paypal_settlement_report.report_date</datatype>
            </ignore>
        </field_rules>
    </source>
    <destination>
        <document_rules>
            <ignore>
            <document>amasty_amshopby_cms_page</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_filter_setting</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_group_attr</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_group_attr_option</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_group_attr_value</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_option_setting</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_page</document>
            </ignore>
            <ignore>
            <document>amasty_amshopby_page_store</document>
            </ignore>
            <ignore>
            <document>amazon_customer</document>
            </ignore>
            <ignore>
            <document>amazon_pending_authorization</document>
            </ignore>
            <ignore>
            <document>amazon_pending_capture</document>
            </ignore>
            <ignore>
            <document>amazon_pending_refund</document>
            </ignore>
            <ignore>
            <document>amazon_quote</document>
            </ignore>
            <ignore>
            <document>amazon_sales_order</document>
            </ignore>
            <ignore>
            <document>catalog_category_product_cl</document>
            </ignore>
            <ignore>
            <document>catalog_product_attribute_cl</document>
            </ignore>
            <ignore>
            <document>catalog_product_category_cl</document>
            </ignore>
            <ignore>
            <document>catalog_product_price_cl</document>
            </ignore>
            <ignore>
            <document>cataloginventory_stock_cl</document>
            </ignore>
            <ignore>
            <document>catalogrule_product_cl</document>
            </ignore>
            <ignore>
            <document>catalogrule_rule_cl</document>
            </ignore>
            <ignore>
            <document>catalogsearch_fulltext_cl</document>
            </ignore>
            <ignore>
            <document>customer_dummy_cl</document>
            </ignore>
            <ignore>
            <document>design_config_dummy_cl</document>
            </ignore>
            <ignore>
            <document>email_abandoned_cart</document>
            </ignore>
            <ignore>
            <document>email_automation</document>
            </ignore>
            <ignore>
            <document>email_campaign</document>
            </ignore>
            <ignore>
            <document>email_catalog</document>
            </ignore>
            <ignore>
            <document>email_contact</document>
            </ignore>
            <ignore>
            <document>email_contact_consent</document>
            </ignore>
            <ignore>
            <document>email_failed_auth</document>
            </ignore>
            <ignore>
            <document>email_importer</document>
            </ignore>
            <ignore>
            <document>email_order</document>
            </ignore>
            <ignore>
            <document>email_review</document>
            </ignore>
            <ignore>
            <document>email_rules</document>
            </ignore>
            <ignore>
            <document>email_wishlist</document>
            </ignore>
            <ignore>
            <document>import_excel</document>
            </ignore>
            <ignore>
            <document>import_excel_tmp</document>
            </ignore>
            <ignore>
            <document>klarna_core_order</document>
            </ignore>
            <ignore>
            <document>klarna_payments_quote</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_animations</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_backup</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_css</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_navigations</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_options</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_sliders</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_slides</document>
            </ignore>
            <ignore>
            <document>nwdthemes_revslider_static_slides</document>
            </ignore>
            <ignore>
            <document>release_notification_viewer_log</document>
            </ignore>
            <ignore>
            <document>signifyd_case</document>
            </ignore>
            <ignore>
            <document>temando_checkout_address</document>
            </ignore>
            <ignore>
            <document>temando_collection_point_search</document>
            </ignore>
            <ignore>
            <document>temando_order</document>
            </ignore>
            <ignore>
            <document>temando_order_collection_point</document>
            </ignore>
            <ignore>
            <document>temando_quote_collection_point</document>
            </ignore>
            <ignore>
            <document>temando_shipment</document>
            </ignore>
            <ignore>
            <document>vertex_customer_code</document>
            </ignore>
            <ignore>
            <document>vertex_invoice_sent</document>
            </ignore>
            <ignore>
            <document>vertex_order_invoice_status</document>
            </ignore>
            <ignore>
            <document>vertex_taxrequest</document>
            </ignore>
            <ignore>
                <document>catalog_product_index_price*</document>
            </ignore>
            <ignore>
                <document>customer_entity</document>
            </ignore>
            <ignore>
                <document>customer_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_entity_varchar</document>
            </ignore>
            <ignore>
                <document>customer_address_entity</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_datetime</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_decimal</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_int</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_text</document>
            </ignore>
            <ignore>
                <document>customer_address_entity_varchar</document>
            </ignore>
            <ignore>
                <document>ui_bookmark</document>
            </ignore>
            <ignore>
                <document>migration_backup_*</document>
            </ignore>
            <ignore>
                <document>googleoptimizer_code</document>
            </ignore>
            <ignore>
                <document>indexer_state</document>
            </ignore>
            <ignore>
                <document>integration</document>
            </ignore>
            <ignore>
                <document>mview_state</document>
            </ignore>
            <ignore>
                <document>theme</document>
            </ignore>
            <ignore>
                <document>theme_file</document>
            </ignore>
            <ignore>
                <document>vde_theme_change</document>
            </ignore>
            <ignore>
                <document>admin_system_messages</document>
            </ignore>
            <ignore>
                <document>catalog_url_rewrite_product_category</document>
            </ignore>
            <ignore>
                <document>customer_visitor</document>
            </ignore>
            <ignore>
                <document>url_rewrite</document>
            </ignore>
            <ignore>
                <document>layout_update</document>
            </ignore>
            <ignore>
                <document>layout_link</document>
            </ignore>
            <ignore>
                <document>catalog_product_flat_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_flat_*</document>
            </ignore>
            <ignore>
                <document>catalogindex_eav</document>
            </ignore>
            <ignore>
                <document>catalog_eav_attribute</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_index_default</document>
            </ignore>
            <ignore>
                <document>customer_eav_attribute</document>
            </ignore>
            <ignore>
                <document>customer_eav_attribute_website</document>
            </ignore>
            <ignore>
                <document>catalog_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_category_product_index_*</document>
            </ignore>
            <ignore>
                <document>catalog_product_frontend_action</document>
            </ignore>
            <ignore>
                <document>cataloginventory_stock_status_replica</document>
            </ignore>
            <ignore>
                <document>catalogrule_group_website_replica</document>
            </ignore>
            <ignore>
                <document>catalogrule_product_price_replica</document>
            </ignore>
            <ignore>
                <document>catalogrule_product_replica</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_product_replica</document>
            </ignore>
            <ignore>
                <document>magento_catalogpermissions_index_replica</document>
            </ignore>
            <ignore>
                <document>eav_attribute</document>
            </ignore>
            <ignore>
                <document>eav_attribute_group</document>
            </ignore>
            <ignore>
                <document>eav_attribute_set</document>
            </ignore>
            <ignore>
                <document>eav_entity_attribute</document>
            </ignore>
            <ignore>
                <document>eav_entity_type</document>
            </ignore>
            <ignore>
                <document>authorization_role</document>
            </ignore>
            <ignore>
                <document>authorization_rule</document>
            </ignore>
            <ignore>
                <document>setup_module</document>
            </ignore>
            <ignore>
                <document>cache</document>
            </ignore>
            <ignore>
                <document>cache_tag</document>
            </ignore>
            <ignore>
                <document>customer_log</document>
            </ignore>
            <ignore>
                <document>flag</document>
            </ignore>
            <ignore>
                <document>log_customer</document>
            </ignore>
            <ignore>
                <document>log_quote</document>
            </ignore>
            <ignore>
                <document>log_summary_type</document>
            </ignore>
            <ignore>
                <document>log_summary</document>
            </ignore>
            <ignore>
                <document>log_url</document>
            </ignore>
            <ignore>
                <document>log_url_info</document>
            </ignore>
            <ignore>
                <document>quote_id_mask</document>
            </ignore>
            <ignore>
                <document>session</document>
            </ignore>
            <ignore>
                <document>sales_sequence_profile</document>
            </ignore>
            <ignore>
                <document>sales_sequence_meta</document>
            </ignore>
            <ignore>
                <document>sequence_invoice_*</document>
            </ignore>
            <ignore>
                <document>sequence_creditmemo_*</document>
            </ignore>
            <ignore>
                <document>sequence_order_*</document>
            </ignore>
            <ignore>
                <document>sequence_shipment*</document>
            </ignore>
            <ignore>
                <document>sequence_rma_item_*</document>
            </ignore>
            <ignore>
                <document>vault_payment_token</document>
            </ignore>
            <ignore>
                <document>vault_payment_token_order_payment_link</document>
            </ignore>
            <ignore>
                <document>search_synonyms</document>
            </ignore>
            <ignore>
                <document>eav_attribute_option_swatch</document>
            </ignore>
            <ignore>
                <document>import_history</document>
            </ignore>
            <ignore>
                <document>sales_creditmemo_grid</document>
            </ignore>
            <ignore>
                <document>sales_invoice_grid</document>
            </ignore>
            <ignore>
                <document>sales_order_grid</document>
            </ignore>
            <ignore>
                <document>sales_shipment_grid</document>
            </ignore>
            <ignore>
                <document>catalogsearch_fulltext_scope*</document>
            </ignore>
            <ignore>
                <document>store</document>
            </ignore>
            <ignore>
                <document>store_group</document>
            </ignore>
            <ignore>
                <document>store_website</document>
            </ignore>
            <ignore>
                <document>log_visitor_info</document>
            </ignore>
            <ignore>
                <document>log_visitor_online</document>
            </ignore>
            <ignore>
                <document>reporting_module_status</document>
            </ignore>
            <ignore>
                <document>reporting_orders</document>
            </ignore>
            <ignore>
                <document>reporting_system_updates</document>
            </ignore>
            <ignore>
                <document>reporting_users</document>
            </ignore>
            <ignore>
                <document>reporting_counts</document>
            </ignore>
            <ignore>
                <document>admin_passwords</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_media_gallery_value_to_entity</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_media_gallery_value_video</document>
            </ignore>
            <ignore>
                <document>catalog_product_entity_tier_price</document>
            </ignore>
            <ignore>
                <document>customer_grid_flat</document>
            </ignore>
            <ignore>
                <document>admin_user_session</document>
            </ignore>
            <ignore>
                <document>password_reset_request_event</document>
            </ignore>
            <ignore>
                <document>oauth_token_request_log</document>
            </ignore>
            <ignore>
                <document>design_config_grid_flat</document>
            </ignore>
        </document_rules>
        <field_rules>
            <transform>
                <field>admin_user.interface_locale</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="en_US" />
                </handler>
            </transform>
            <transform>
                <field>cataloginventory_stock.website_id</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0" />
                </handler>
            </transform>
            <transform>
                <field>cataloginventory_stock_item.website_id</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0" />
                </handler>
            </transform>
            <transform>
                <field>rating.is_active</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0" />
                </handler>
            </transform>
            <transform>
                <field>authorization_role.user_type</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="2" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.base_amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.real_amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.real_base_amount</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="0.0000" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_tax_item.taxable_item_type</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="product" />
                </handler>
            </transform>
            <transform>
                <field>oauth_token.user_type</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="1" />
                </handler>
            </transform>
            <transform>
                <field>widget_instance.theme_id</field>
                <handler class="\Migration\Handler\SetValue">
                    <param name="value" value="3" />
                </handler>
            </transform>
            <transform>
                <field>sales_order_status_state.visible_on_front</field>
                <handler class="\Migration\Handler\SalesOrderStatusState\SetVisibleOnFront"/>
            </transform>
            <transform>
                <field>catalog_product_entity_media_gallery_value.entity_id</field>
                <handler class="\Migration\Handler\Gallery\SetEntityId"/>
            </transform>
            <transform>
                <field>catalog_product_bundle_option_value.parent_product_id</field>
                <handler class="\Migration\Handler\BundleParentProduct">
                    <param name="parentField" value="option_id"/>
                    <param name="documentWithProductId" value="catalog_product_bundle_option"/>
                    <param name="fieldWithProductId" value="parent_id"/>
                </handler>
            </transform>
            <transform>
                <field>catalog_product_bundle_selection_price.parent_product_id</field>
                <handler class="\Migration\Handler\BundleParentProduct">
                    <param name="parentField" value="selection_id"/>
                    <param name="documentWithProductId" value="catalog_product_bundle_selection"/>
                    <param name="fieldWithProductId" value="parent_product_id"/>
                </handler>
            </transform>
            <ignore>
                <field>adminnotification_inbox.expiration_date</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery_value.entity_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery.media_type</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery.disabled</field>
            </ignore>
            <ignore>
                <field>catalog_product_entity_media_gallery_value.record_id</field>
            </ignore>
            <ignore>
                <field>admin_user.refresh_token</field>
            </ignore>
            <ignore>
                <field>adminnotification_inbox.is_amasty</field>
            </ignore>
            <ignore>
                <field>quote_item.is_excluded_product</field>
            </ignore>
            <ignore>
                <field>salesrule_coupon.generated_by_dotmailer</field>
            </ignore>
            <ignore>
                <field>admin_user.interface_locale</field>
            </ignore>
            <ignore>
                <field>admin_user.failures_num</field>
            </ignore>
            <ignore>
                <field>admin_user.first_failure</field>
            </ignore>
            <ignore>
                <field>admin_user.lock_expires</field>
            </ignore>
            <ignore>
                <field>rating.is_active</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.send_email</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.customer_note</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo.customer_note_notify</field>
            </ignore>
            <ignore>
                <field>sales_invoice.send_email</field>
            </ignore>
            <ignore>
                <field>sales_invoice.customer_note</field>
            </ignore>
            <ignore>
                <field>sales_invoice.customer_note_notify</field>
            </ignore>
            <ignore>
                <field>sales_shipment.send_email</field>
            </ignore>
            <ignore>
                <field>sales_shipment.customer_note</field>
            </ignore>
            <ignore>
                <field>sales_shipment.customer_note_notify</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo_item.tax_ratio</field>
            </ignore>
            <ignore>
                <field>sales_invoice_item.tax_ratio</field>
            </ignore>
            <ignore>
                <field>authorization_role.user_type</field>
            </ignore>
            <ignore>
                <field>cataloginventory_stock.website_id</field>
            </ignore>
            <ignore>
                <field>cataloginventory_stock_item.website_id</field>
            </ignore>
            <ignore>
                <field>oauth_nonce.consumer_id</field>
            </ignore>
            <ignore>
                <field>oauth_token.user_type</field>
            </ignore>
            <ignore>
                <field>sales_order_status_state.visible_on_front</field>
            </ignore>
            <ignore>
                <field>widget_instance.theme_id</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.base_amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.real_amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.real_base_amount</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.associated_item_id</field>
            </ignore>
            <ignore>
                <field>sales_order_tax_item.taxable_item_type</field>
            </ignore>
            <ignore>
                <field>sales_creditmemo_grid.updated_at</field>
            </ignore>
            <ignore>
                <field>sales_invoice_grid.updated_at</field>
            </ignore>
            <ignore>
                <field>sales_shipment_grid.updated_at</field>
            </ignore>
            <ignore>
                <field>sales_order.send_email</field>
            </ignore>
            <ignore>
                <field>checkout_agreement.mode</field>
            </ignore>
            <ignore>
                <field>catalog_category_product.entity_id</field>
            </ignore>
            <ignore>
                <field>cms_page.meta_title</field>
            </ignore>
            <ignore>
                <field>catalog_product_bundle_selection_price.parent_product_id</field>
            </ignore>
            <ignore>
                <field>catalog_product_bundle_option_value.parent_product_id</field>
            </ignore>
            <ignore>
                <datatype>quote_payment.cc_exp_month</datatype>
            </ignore>
            <ignore>
                <datatype>weee_tax.state</datatype>
            </ignore>
            <ignore>
                <datatype>sendfriend_log.ip</datatype>
            </ignore>
            <ignore>
                <datatype>rating_option_vote.remote_ip_long</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_consumer.rejected_callback_url</datatype>
            </ignore>
            <ignore>
                <datatype>oauth_token.callback_url</datatype>
            </ignore>
           <ignore>
               <datatype>catalog_product_bundle_price_index.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalog_product_index_price.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalog_product_index_tier_price.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_customer_group.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_group_website.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_product_price.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>customer_group.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>paypal_settlement_report.report_date</datatype>
           </ignore>
           <ignore>
               <datatype>sales_order.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>salesrule_customer_group.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>salesrule_product_attribute.customer_group_id</datatype>
           </ignore>
           <ignore>
               <datatype>catalogrule_product.customer_group_id</datatype>
           </ignore>
		    <ignore>
                <datatype>catalog_product_bundle_price_index.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalog_product_index_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalog_product_index_tier_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_group_website.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>catalogrule_product_price.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_customer_group.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>salesrule_product_attribute.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>sales_order.customer_group_id</datatype>
            </ignore>
            <ignore>
                <datatype>paypal_settlement_report.report_date</datatype>
            </ignore>
        </field_rules>
    </destination>
</map>
