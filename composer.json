{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true, "magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true}, "version": "2.4.7", "require": {"ext-rdkafka": "*", "affirm/magento2": "4.0.6", "affirm/magento2-telesales": "^1.2", "aheadworks/module-giftcard": "1.8.3", "cweagans/composer-patches": "^1.7", "elasticsearch/elasticsearch": "v7.17.0", "extend/module-warranty": "^2.5", "fishpig/magento2-wordpress-integration": "^3.31", "fishpig/magento2-wordpress-integration-yoastseo": "^3.3", "google/apiclient": "^2.18", "hyva-themes/magento2-aheadworks-giftcard": "^1.0", "hyva-themes/magento2-amasty-shopby": "^1.0", "hyva-themes/magento2-amasty-shopby-brand": "^1.0", "hyva-themes/magento2-cms-tailwind-jit": "^1.1", "hyva-themes/magento2-default-theme": "1.3.13", "hyva-themes/magento2-luma-checkout": "^1.1", "hyva-themes/magento2-signifyd-connect": "dev-main", "hyva-themes/magento2-theme-module": "1.3.14", "inchoodev/module-cloudflare-turnstile": "1.2.2", "inchoodev/module-google-tag-manager": "1.2.13", "inchoodev/module-local-store-pickup": "1.1.9", "inchoodev/module-social-connect": "2.1.8", "inchoodev/module-url-rewrite-import": "1.1.4", "inchoodev/theme-frontend-medellin": "1.1.1", "keh/magento2-sentry": "dev-main#3.8.6", "kehcamera/keh-api": "dev-main#0.4.13", "laminas/laminas-barcode": "^2.8", "magento/composer-dependency-version-audit-plugin": "^0.1", "magento/composer-root-update-plugin": "^2.0", "magento/product-community-edition": "2.4.7-p6", "mailjet/mailjet-apiv3-php": "^1.6", "mexbs/module-tieredcoupon": "1.1.15", "mirasvit/module-rma": "*", "pusher/pusher-php-server": "^7.2", "signifyd/module-connect": "^5.8", "snowdog/module-menu": "^2.27", "stripe/module-payments": "^4.1", "stripe/stripe-payments": "^4.1", "taxjar/module-taxjar": "^2.2", "temando/module-shipping": "^2.0"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2", "dealerdirect/phpcodesniffer-composer-installer": "^0.7 || ^1.0", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.22", "hyva-themes/hyva-ui": "^2.4", "lusitanian/oauth": "^0.8", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.7", "pdepend/pdepend": "^2.10", "phpmd/phpmd": "^2.12", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "sebastian/phpcpd": "^6.0", "symfony/finder": "^6.4"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"mirasvit-rma2": {"type": "composer", "url": "https://67603:<EMAIL>/67603:4PM9RZUBYX/"}, "local-modules": {"type": "path", "url": "app/code/*/*"}, "private-packagist": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/keh-com/"}, "aheadworks": {"type": "composer", "url": "https://dist.aheadworks.com/"}, "extend": {"type": "git", "url": "https://github.com/helloextend/magento-extension/"}, "signifydmage2": {"type": "git", "url": "https://github.com/signifyd/magento2.git"}, "0": {"type": "composer", "url": "https://repo.magento.com/"}, "inchoo-google-sync": {"type": "git", "url": "**************:KEHCamera/module-google-sync.git"}, "inchoo-google-tag-manager": {"type": "git", "url": "**************:KEHCamera/module-google-tag-manager.git"}, "inchoo-local-store-pickup": {"type": "git", "url": "**************:KEHCamera/module-local-store-pickup.git"}, "inchoo-slider": {"type": "git", "url": "**************:KEHCamera/module-slider.git"}, "inchoo-social-connect": {"type": "git", "url": "**************:KEHCamera/module-social-connect.git"}, "inchoo-url-rewrite-import": {"type": "git", "url": "**************:KEHCamera/module-url-rewrite-import.git"}, "inchoodev-cloudflare-turnstile": {"type": "git", "url": "**************:KEHCamera/inchoodev-module-cloudflare-turnstile.git"}, "inchoo-component-medellin-toolbox": {"type": "git", "url": "**************:KEHCamera/component-medellin-toolbox.git"}, "inchoo-theme-frontend-medellin": {"type": "git", "url": "**************:KEHCamera/theme-frontend-medellin.git"}, "magento2-sentry": {"type": "git", "url": "**************:KEHCamera/magento2-sentry.git"}}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"magento/module-catalog-search": {"Fix performance of getRelatedProducts": "patches/composer/module-catalog-search/related-products-performance.patch"}, "magento/magento2-base": {"Remove trailing slash": "patches/composer/Redirect-trailing-slash.patch", "INCHOO : magepack fix for effect ui modules": "patches/composer/magepack-non-define-ui-modules-effects-fix.patch"}, "taxjar/module-taxjar": {"Fix for address edit on checkout": "patches/composer/Taxjar-address-checkout.patch"}, "magento/module-cms": {"Disable CMS page route duplication validation": "patches/composer/Magento_Cms-disable-duplicate-route-validation.patch"}, "magento/module-quote": {"Fix for customer quote being flagged as guest": "patches/composer/Ma<PERSON><PERSON>_Quote-customer-quote-fix.patch", "Fix for Extend warranty": "patches/composer/Ma<PERSON>o_Quote-warranty-fix.patch", "Fix quest order creation using Stipe GPay and ApplePay": "patches/composer/magento-quote/fix-guest-order-creation.patch"}, "magento/module-customer": {"Fix password reset": "patches/composer/Ma<PERSON>o_Customer-password-reset-fix.patch", "Fix indexer source": "patches/composer/module-customer/indexer-source.patch"}, "magento/framework": {"Fix the issue with missing products on the frontend during reindexing": "patches/composer/MDVA-40550-framework.patch", "MDVA-43395": "patches/composer/MDVA-43395-framework.patch", "MDVA-43443": "patches/composer/MDVA-43443-framework.patch"}, "magento/module-sales": {"Fix for Extend warranty": "patches/composer/Magento_Sales-warranty-fix.patch", "Fix order grid date filter": "patches/composer/Magento_Sales-order-grid-date-filter-fix.patch", "Authorize virtual orders": "patches/composer/Magento_Sales-authorize-virtual-orders.patch"}, "inchoodev/module-google-tag-manager": {"Success page hotfix": "patches/composer/InchooDev_GoogleTagManager-success-page.patch", "Session events fix": "patches/composer/inchoodev/gtm-session-events.patch"}, "paypal/module-braintree-core": {"Credit cart checkout config provider fix": "patches/composer/PayPal_Braintree-cc-config-provider-fix.patch", "Fix for failed place order": "patches/composer/PayPal_Braintree-place-order-fix.patch", "Fix sales_order_grid in admin": "patches/composer/PayPal_Braintree-sales_order_gird_v2.patch"}, "magento/module-user": {"Admin password reset link fix": "patches/composer/Magento_User-admin-password-reset-link-fix.patch"}, "magento/module-sales-rule": {"Reset quote item applied rules": "patches/composer/Magento_SalesRule-reset-item-applied-rules.patch"}, "inchoodev/module-local-store-pickup": {"Fix for case when there is no shipping method": "patches/composer/InchooDev_LocalStorePickup-shipping-method-fix.patch"}, "signifyd/module-connect": {"Dispatch events": "patches/composer/Signifyd_Connect-dispatch-events.patch", "Prevent order update when case is manually updated in Signifyd": "patches/composer/Signifyd_Connect-disallow-manual-order-update.patch"}, "magento/module-grouped-product": {"Fallback to empty array when there are no associated products defined": "patches/composer/Magento_GroupedProduct-prepare-product-fix.patch"}, "magento/module-customer-import-export": {"Add custom fields to customer import": "patches/composer/magento_customer_import_add_custom_fields.patch"}, "magento/module-csp": {"Report Only config fix": "patches/composer/Magento_Csp-model-mode-configManager-report-only.patch"}, "magento/module-backend": {"ACSD-59582 for backend": "patches/composer/ACSD-59582_2.4.7-backend.patch"}, "magento/module-page-builder": {"ACSD-59582 for page-builder": "patches/composer/ACSD-59582_2.4.7-pagebuilder.patch", "fix require missing blocking product save": "patches/composer/fix-require-not-found.patch"}, "snowdog/module-menu": {"save store id": "patches/composer/snowdog/save-store-id.patch"}, "magento/module-fedex": {"Add international priority express": "patches/composer/module-fedex/add-international-priority-express.patch"}, "magento/module-wishlist": {"Increase wishlist items number in customer data": "patches/composer/module-wishlist/change-wishlist-items-number.patch"}, "mirasvit/module-rma": {"Enable menu": "patches/composer/mirasvit/rma-enable-menu.patch"}, "affirm/magento2-telesales": {"Fix issue with laminas version": "patches/composer/affirm/telesale-laminas-version.patch"}}}, "replace": {"dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-sms": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-dhl": "*", "magento/composer-dependency-version-audit-plugin": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-customer-analytics": "*", "magento/module-version": "*", "hyva-themes/magento2-mollie-theme-bundle": "*"}}