const postcss = require('../node_modules/postcss');
const colorConverter = require("../node_modules/color-convert");
const config = require("../config");
const colorSystem = config.colorSystem;

module.exports = {
  generateColorsVar: mixin => {
      const baseColors = Object.entries(colorSystem.targetColors),
          colorTones = colorSystem.tones;
      let hslColorsRaw = [];

      baseColors.forEach((color, i) => {
          hslColorsRaw.push(colorConverter.hex.hsl(color[1]));
      });

      let shadeColors = [],
          targetColorsProps = Object.keys(colorSystem.targetColors),
          targetColorsHsl = {},
          shadeColorsHsl = {};

      targetColorsProps.forEach((key, i) => targetColorsHsl[key] = hslColorsRaw[i]);

      for (const [key, value] of Object.entries(targetColorsHsl)) {
          let l = targetColorsHsl[key][0];
          shadeColorsHsl[key + '__base'] = targetColorsHsl[key];
          
          colorTones.forEach((tone, index) => {
              let h = targetColorsHsl[key][0];
              let s = targetColorsHsl[key][1];
              let l = tone;
              shadeColorsHsl[key + '__' + l] = [h, s, l];
          });
      }

      const shadeColorsHslFinal = Object.entries(shadeColorsHsl),
            rule = postcss.rule({selector: ":root"});

      shadeColorsHslFinal.forEach((color, i) => {
          let [h, s, l] = color[1];
          
          rule.append({
              prop: '--' + color[0],
              value: 'hsl(' + h + ', ' + s + '%, ' + l + '%)'
          });
          mixin.replaceWith(rule);
      });
  }
}