const baseVars = require("../variables");
const config = require("../../config");
const fontSizes = config.mixinVR.vrSetup;

const rhythmLines = (lines, baseLineHeight, baseFontSize) => {
    var result = lines * (parseFloat(baseLineHeight)),
        toRem = result / parseFloat(baseFontSize, 10) + 'rem';
    if (lines > 0) {
        return toRem
    } else {
        return lines
    }

};

exports.rhythmLines = rhythmLines;

const reducedOutput = input => {
    let output = input.reduce(function (obj, item) {
        let key = Object.keys(item)[0],
            value = item[key];
        obj[key] = value;
        return obj;
    }, {});
    return output;
};

exports.reducedOutput = reducedOutput;

exports.fontSizeAdjust = (inputFontSize, lines, inputBaseFontSize, inputBaseLineHeight) => {
    const fontSize = parseFloat(inputFontSize, 10),
        baseFontSize = parseFloat(inputBaseFontSize, 10),
        baseLineHeight = parseFloat(inputBaseLineHeight, 10),
        linesFinal = ((lines * baseLineHeight) / fontSize),
        fontSizeFinal = fontSize / baseFontSize + 'rem',
        resultArr = [linesFinal,fontSizeFinal];
    return resultArr;
};

exports.applySpacing = (lines, breakPoint, type) => {
    let output = [],
        htmlResult;
    fontSizes.map(function (item, i) {
        const entries = Object.entries(item);
        entries.map((entry, i) => {
            if(entry[0] === breakPoint) {
                const calcLines = rhythmLines(lines, entry[1].baseLineHeight, entry[1].baseFontSize);
                if(type === "margin-leader") {
                    output.push({
                        "margin-top": calcLines
                    });
                }
                if(type === "padding-leader") {
                    output.push({
                        "padding-top": calcLines
                    });
                }
                if(type === "margin-trailer") {
                    output.push({
                        "margin-bottom": calcLines
                    });
                }
                if(type === "padding-trailer") {
                    output.push({
                        "padding-bottom": calcLines
                    });
                }
            } else {
                // console.log('Problem with the output.');
            }
        });
    });
    htmlResult = reducedOutput(output);
    return htmlResult;
};

exports.applyBorders = (borderWidth, lines, borderStyle, type, breakPoint) => {
    var output = [],
        htmlResult;
    fontSizes.map((item, i) => {
        const entries = Object.entries(item);
        entries.map((entry, i) => {
            if(entry[0] === breakPoint) {
                if(lines > 0) {
                    if(type === "trailing") {
                        output.push({
                            "border-bottom": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                        output.push({
                            "padding-bottom": borderSpacingCalc(borderWidth, lines, entry[1].baseFontSize, entry[1].baseLineHeight)
                        });
                    }
                    if(type === "leading") {
                        output.push({
                            "border-top": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                        output.push({
                            "padding-top": borderSpacingCalc(borderWidth, lines, entry[1].baseFontSize, entry[1].baseLineHeight)
                        });
                    }
                    if(type === "all") {
                        output.push({
                            "border-top": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                        output.push({
                            "padding-top": borderSpacingCalc(borderWidth, lines, entry[1].baseFontSize, entry[1].baseLineHeight)
                        });
                        output.push({
                            "border-bottom": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                        output.push({
                            "padding-bottom": borderSpacingCalc(borderWidth, lines, entry[1].baseFontSize, entry[1].baseLineHeight)
                        });
                        output.push({
                            "border-left": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                        output.push({
                            "border-right": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                    }
                } else {
                    if(type === "trailing") {
                        output.push({
                            "border-bottom": borderWidthCalc(borderWidth, entry[1].baseLineHeight, borderStyle)
                        });
                    }
                    if(type === "leading") {
                        output.push({
                            "border-top": borderWidthCalc(borderWidth, entry[1].baseLineHeight, borderStyle)
                        });
                    }
                    if(type === "all") {
                        output.push({
                            "border-top": borderWidthCalc(borderWidth, entry[1].baseLineHeight, borderStyle)
                        });
                        output.push({
                            "border-bottom": borderWidthCalc(borderWidth, entry[1].baseLineHeight, borderStyle)
                        });
                        output.push({
                            "border-left": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                        output.push({
                            "border-right": borderWidthCalc(borderWidth, entry[1].baseFontSize, borderStyle),
                        });
                    }
                }
            } else {
                //console.log('Problem with the output.');
            }
        });
    });

    htmlResult = reducedOutput(output);
    return htmlResult;
}

const borderWidthCalc = (borderWidth, baseFontSize, borderStyle) => {
    let setBorderWidth = parseFloat(borderWidth, 10) / parseFloat(baseFontSize, 10) + "rem";
    return setBorderWidth + " " + borderStyle;
};

const borderSpacingCalc = (borderWidth, lines, baseFontSize, baseLineHeight) => {
    const calcSpacing = lines * parseFloat(baseLineHeight, 10),
        reduceSpacingByBorderWidth = (calcSpacing - parseFloat(borderWidth, 10)) / parseFloat(baseFontSize, 10) + "rem";
    let calculatedResult = reduceSpacingByBorderWidth;
    return calculatedResult;
};
