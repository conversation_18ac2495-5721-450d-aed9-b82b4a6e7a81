const postcss = require('../node_modules/postcss');
const baseVars = require("../mixins/variables");
const config = require("../config");
const fs = require("fs");
const fontSizes = config.mixinVR.vrSetup;
const vrUtils = require('../mixins/vr-utils/vr-utils');

module.exports = {
    setBaseline: (mixin, defaultStyle = 'modern', baseFontSize = 16) => {

        this.calcFontSize = (fontSize, baseFontSize = 16) => {
            const desiredFontSize = parseFloat(fontSize),
                result = desiredFontSize / baseFontSize + 'rem';
            return result;
        },
        this.calcLines = (fontSize, lineHeight, baseFontSize = 16) => {
            const baseLineHeight = parseFloat(lineHeight, 10),
                desiredFontSize = parseFloat(fontSize, 10),
                result = baseLineHeight / desiredFontSize + '';
            return result;
        };

        let self = this,
            output = [],
            test,
            htmlResult;

        fontSizes.map((item, i) => {
            const entries = Object.entries(item);
            entries.map((entry,i) => {
                if (entry[0] === "initial") {
                    if(defaultStyle === "modern") {
                        output.push({
                            ":root": {
                                "--base__font__size": self.calcFontSize(entry[1].baseFontSize),
                                "--base__line__height": self.calcLines(entry[1].baseFontSize, entry[1].baseLineHeight)
                            }
                        });
                        output.push({
                            "html": {
                                "font-size": "var(--base__font__size)",
                                "line-height": "var(--base__line__height)"
                            }
                        });
                    } else if(defaultStyle === "classic") {
                        output.push({
                            "html": {
                                "font-size": self.calcFontSize(entry[1].baseFontSize),
                                "line-height": self.calcLines(entry[1].baseFontSize, entry[1].baseLineHeight)
                            }
                        });
                    } else {
                        console.log('No output. Default style mode should be set (modern or classic)');
                    }
                } else {
                    if(defaultStyle === "modern") {
                        output.push({
                            ["@media only screen and (min-width:" + entry[0] + ")"]: {
                                ":root": {
                                    "--base__font__size": self.calcFontSize(entry[1].baseFontSize),
                                    "--base__line__height": self.calcLines(entry[1].baseFontSize, entry[1].baseLineHeight)
                                }
                            }
                        });
                    } else if(defaultStyle === "classic") {
                        output.push({
                            ["@media only screen and (min-width:" + entry[0] + ")"]: {
                                "html": {
                                    "font-size": self.calcFontSize(entry[1].baseFontSize),
                                    "line-height": self.calcLines(entry[1].baseFontSize, entry[1].baseLineHeight)

                                }
                            }
                        });
                    } else {
                        console.log('No output. Default style mode should be set (modern or classic)');
                    }

                }
            });

        });

        htmlResult = vrUtils.reducedOutput(output);
        return htmlResult;

    },
    debugRhythm: mixin => {
        let output = [],
            htmlResult;
        fontSizes.map((item, i) => {
            const entries = Object.entries(item);
            entries.map((entry,i) => {
                const parsedLineHeight = parseFloat(entry[1].baseLineHeight ,10);
                if (entry[0] === "initial") {
                    output.push({
                        "background-image": "linear-gradient(rgba(200, 50, 50, .25) 1px, transparent 1px)",
                    });
                    output.push({
                        "background-size": "1px " + parsedLineHeight + "px"
                    });
                } else {
                    output.push({
                        ["@media only screen and (min-width:" + entry[0] + ")"]: {
                            "background-image": "linear-gradient(rgba(200, 50, 50, .25) 1px, transparent 1px)",
                            "background-size": "1px " + parsedLineHeight + "px"
                        }
                    });
                }
            });
        });

        htmlResult = vrUtils.reducedOutput(output);
        return htmlResult;
    },
    adjustFontSize: (mixin, fontSize, lines, breakPoint = "initial") => {

        /*var fontSize = parseFloat(fontSize, 10),
            baseFontSize = parseFloat(baseFontSize, 10),
            baseLineHeight = parseFloat(baseLineHeight, 10),
            linesFinal = ((lines * baseLineHeight) / fontSize),
            fontSizeFinal = fontSize / baseFontSize + 'rem';
        */

        var output = [],
            htmlResult;
        fontSizes.map((item, i) => {
            const entries = Object.entries(item);
            entries.map((entry, i) => {
                if(entry[0] === breakPoint) {
                    const calcFontSize = vrUtils.fontSizeAdjust(fontSize, lines, entry[1].baseFontSize, entry[1].baseLineHeight);
                    output.push({
                        "font-size": calcFontSize[1]
                    });
                    output.push({
                        "line-height": calcFontSize[0]
                    });
                }
            });
        });

        htmlResult = vrUtils.reducedOutput(output);
        return htmlResult;
    },
    adjustLineHeight: (mixin, lines, breakPoint = "initial") => {

        var output = [],
            htmlResult;
        fontSizes.map((item, i) => {
            const entries = Object.entries(item);
            entries.map((entry, i) => {
                if(entry[0] === breakPoint) {
                    output.push({
                        "line-height": vrUtils.rhythmLines(lines, entry[1].baseLineHeight, entry[1].baseFontSize)
                    });
                }
            });
        });

        htmlResult = vrUtils.reducedOutput(output);
        return htmlResult;
    },
    setRhythmPosition: (mixin, lines, breakPoint = "initial", position = "top") => {

        var output = [],
            htmlResult;
        fontSizes.map((item, i) => {
            const entries = Object.entries(item);
            entries.map((entry, i) => {
                if(entry[0] === breakPoint) {
                    if(position) {
                        output.push({
                            ["" + position + ""]: vrUtils.rhythmLines(lines, entry[1].baseLineHeight, entry[1].baseFontSize)
                        });
                    }
                }
            });
        });

        htmlResult = vrUtils.reducedOutput(output);
        return htmlResult;
    },
    paddingLeader: (mixin, lines, breakPoint = "initial", type = "padding-leader") => {
        return vrUtils.applySpacing(lines, breakPoint, type);
    },
    paddingTrailer: (mixin, lines, breakPoint = "initial", type = "padding-trailer") => {
        return vrUtils.applySpacing(lines, breakPoint, type);
    },
    marginLeader: (mixin, lines, breakPoint = "initial", type = "margin-leader") => {
        return vrUtils.applySpacing(lines, breakPoint, type);
    },
    marginTrailer: (mixin, lines, breakPoint = "initial", type = "margin-trailer") => {
        return vrUtils.applySpacing(lines, breakPoint, type);
    },
    leadingBorder: (mixin, borderWidth, lines = 0, borderStyle, breakPoint = "initial", type = "leading") => {
        return vrUtils.applyBorders(borderWidth, lines, borderStyle, type, breakPoint)
    },
    trailingBorder: (mixin, borderWidth, lines = 0, borderStyle, breakPoint = "initial", type = "trailing") => {
        return vrUtils.applyBorders(borderWidth, lines, borderStyle, type, breakPoint)
    },
    rhythmBorders: (mixin, borderWidth, lines = 0, borderStyle, breakPoint = "initial", type = "all") => {
        return vrUtils.applyBorders(borderWidth, lines, borderStyle, type, breakPoint)
    },
    rhythm: (mixin, linesPLeader, linesPTrailer, linesMLeader, linesMTrailer, breakPoint = "initial") => {
        let output = [],
            htmlResult;
        fontSizes.map((item, i) => {
            const entries = Object.entries(item);
            entries.map((entry, i) => {
                if(entry[0] === breakPoint) {
                    //console.log(entry);
                    output.push({
                        "padding-top": vrUtils.rhythmLines(linesPLeader, entry[1].baseLineHeight, entry[1].baseFontSize),
                    });
                    output.push({
                        "padding-bottom": vrUtils.rhythmLines(linesPTrailer, entry[1].baseLineHeight, entry[1].baseFontSize),
                    });
                    output.push({
                        "margin-top": vrUtils.rhythmLines(linesMLeader, entry[1].baseLineHeight, entry[1].baseFontSize),
                    });
                    output.push({
                        "margin-bottom": vrUtils.rhythmLines(linesMTrailer, entry[1].baseLineHeight, entry[1].baseFontSize)
                    });
                    return output;
                }
            });
        });
        htmlResult = vrUtils.reducedOutput(output);
        return htmlResult;
    }
};
