const config = require("../config");
const vrUtils = require('./vr-utils/vr-utils');

var output = [],
    htmlResult;
config.mixinVR.vrSetup.forEach(item => {
    const keys = Object.keys(item)[0];
    //console.log(keys, "Keys after file modification with mapping");
    output.push({
        [`breakpoint-${keys}`] : keys
    });
});

htmlResult = vrUtils.reducedOutput(output);

module.exports = htmlResult;