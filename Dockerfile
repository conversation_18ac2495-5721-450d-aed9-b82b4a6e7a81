ARG BASE_IMAGE="gcr.io/keh-internal-development/keh-web/base_image:0367fc47696d81d5db550bf1d6e9631c5c332438"
FROM ${BASE_IMAGE}

ENV INSTALL_DIR=/var/www/keh-magento
WORKDIR $INSTALL_DIR

ARG SELFCERT_C="US"
ARG SELFCERT_ST="GA"
ARG SELFCERT_O="KEH, Inc."
ARG SELFCERT_CN="localhost"
ARG SELFCERT_SAN="DNS:*.com"
ARG SHORT_SHA

RUN openssl req -x509 \
	-nodes \
	-days 90 \
	-subj "/C=${SELFCERT_C}/ST=${SELFCERT_ST}/O=${SELFCERT_O}/CN=${SELFCERT_CN}" \
	-addext "subjectAltName=${SELFCERT_SAN}" \
	-newkey rsa:2048 \
	-keyout /etc/ssl/private/nginx-selfsigned.key \
	-out /etc/ssl/certs/nginx-selfsigned.crt

RUN mkdir /run/php && \
    touch /run/php/keh.sock && \
    chown www-data:www-data /run/php/keh.sock /var/www . && \
    mv /usr/local/etc/php-fpm.d/www.conf /usr/local/etc/php-fpm.d/www.conf.original && \
    mv /usr/local/etc/php-fpm.d/zz-docker.conf /usr/local/etc/php-fpm.d/zz-docker.conf.original

COPY ./etc/php-fpm/. /usr/local/etc/php-fpm.d/
COPY ./etc/php/. /usr/local/etc/php/conf.d/
COPY ./etc/nginx/site.conf /etc/nginx/sites-enabled/default
# copy EVERYTHING
COPY --chown=www-data:www-data --chmod=777 . .
#RUN cp etc/config.php app/etc/config.php
RUN cp etc/env.php app/etc/env.php
# this can be moved to base_image after - it takes 200+ seconds
RUN composer install --prefer-dist --ansi --no-dev --no-interaction
# all these 'finds' take 120 seconds - need to find faster way for that!
RUN find var generated vendor pub/static pub/media app/etc -type f -exec chmod g+w {} + && \
    find var generated vendor pub/static pub/media app/etc -type d -exec chmod g+ws {} + && \
    chown -R :www-data . && \
    chmod u+x bin/magento
# preparing config for bin/magento setup:di:compile
RUN cp etc/env.php app/etc/build.env.php
RUN cp etc/build.env.php app/etc/env.php
# call bin/magento setup:di:compile
RUN php -dmemory_limit=5G bin/magento setup:di:compile
# cleanup config after bin/magento setup:di:compile
RUN cp etc/env.php app/etc/env.php
# install CSS app/design/frontend/Keh/hyva/web/tailwind
RUN cd $INSTALL_DIR/app/design/frontend/Keh/hyva/web/tailwind && \
    npm install && \
    npm run build-prod && \
    chmod a+rwx $INSTALL_DIR/app/design/frontend/Keh/hyva/web/css/styles.css && \
    chown www-data:www-data $INSTALL_DIR/app/design/frontend/Keh/hyva/web/css/styles.css
# install medellin-toolbox
RUN cd $INSTALL_DIR/medellin-toolbox && \
    npm install
# copying startup scripts
RUN cp build_magento.sh start_services.sh post-deploy.sh /
# set cmd
CMD /start_services.sh
# expose
EXPOSE 80
EXPOSE 443
EXPOSE 9000
