<?php
return [
    'modules' => [
        'Magento_Store' => 1,
        'Magento_Config' => 1,
        'Magento_AdminAnalytics' => 1,
        'Magento_Directory' => 1,
        'Magento_AdminNotification' => 1,
        'Magento_AdobeIms' => 1,
        'Magento_AdobeImsApi' => 1,
        'Magento_AdobeStockAdminUi' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_AdobeStockAssetApi' => 1,
        'Magento_AdobeStockClient' => 1,
        'Magento_AdobeStockClientApi' => 1,
        'Magento_AdobeStockImage' => 1,
        'Magento_Theme' => 1,
        'Magento_AdobeStockImageApi' => 1,
        'Magento_AdvancedPricingImportExport' => 1,
        'Magento_Backend' => 1,
        'Magento_Amqp' => 1,
        'Magento_Security' => 1,
        'Magento_ApplicationPerformanceMonitor' => 1,
        'Magento_ApplicationPerformanceMonitorNewRelic' => 0,
        'Magento_AsyncConfig' => 1,
        'Magento_User' => 1,
        'Magento_Authorization' => 1,
        'Magento_Eav' => 1,
        'Magento_Customer' => 1,
        'Magento_AdminAdobeIms' => 1,
        'Magento_Backup' => 1,
        'Magento_Indexer' => 1,
        'Magento_GraphQl' => 1,
        'Magento_BundleImportExport' => 0,
        'Magento_CacheInvalidate' => 1,
        'Magento_Variable' => 1,
        'Magento_Cms' => 1,
        'Magento_Rule' => 1,
        'Magento_Integration' => 1,
        'Magento_GraphQlResolverCache' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Msrp' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_CheckoutAgreementsGraphQl' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_CmsUrlRewriteGraphQl' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_TwoFactorAuth' => 0,
        'Magento_Payment' => 1,
        'Magento_Sales' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_Checkout' => 1,
        'Magento_Contact' => 1,
        'Magento_ContactGraphQl' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_Csp' => 1,
        'Magento_Widget' => 1,
        'Magento_Robots' => 1,
        'Magento_Downloadable' => 1,
        'Magento_Newsletter' => 1,
        'Magento_CustomerImportExport' => 1,
        'Magento_DataExporter' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_GraphQlServer' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_CustomerDownloadableGraphQl' => 1,
        'Magento_ImportExport' => 1,
        'Magento_Bundle' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_Fedex' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GoogleAdwords' => 1,
        'Magento_GoogleAnalytics' => 0,
        'Magento_GoogleGtag' => 1,
        'Magento_Ui' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_PageCache' => 1,
        'Magento_GraphQlNewRelic' => 0,
        'Magento_CatalogCmsGraphQl' => 1,
        'Magento_AdminGraphQlServer' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedImportExport' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_DownloadableImportExport' => 0,
        'Magento_Captcha' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_Analytics' => 1,
        'Magento_IntegrationGraphQl' => 1,
        'Magento_Inventory' => 1,
        'Magento_InventoryAdminUi' => 1,
        'Magento_InventoryAdvancedCheckout' => 1,
        'Magento_InventoryApi' => 1,
        'Magento_InventoryBundleImportExport' => 1,
        'Magento_InventoryBundleProduct' => 1,
        'Magento_InventoryBundleProductAdminUi' => 1,
        'Magento_InventoryBundleProductIndexer' => 1,
        'Magento_InventoryCatalog' => 1,
        'Magento_InventorySales' => 1,
        'Magento_InventoryCatalogAdminUi' => 1,
        'Magento_InventoryCatalogApi' => 1,
        'Magento_InventoryCatalogFrontendUi' => 1,
        'Magento_InventoryCatalogSearch' => 1,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_InventoryConfigurableProduct' => 1,
        'Magento_InventoryConfigurableProductIndexer' => 1,
        'Magento_InventoryConfiguration' => 1,
        'Magento_InventoryConfigurationApi' => 1,
        'Magento_InventoryDistanceBasedSourceSelection' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 1,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 1,
        'Magento_InventoryElasticsearch' => 1,
        'Magento_InventoryExportStockApi' => 1,
        'Magento_InventoryIndexer' => 1,
        'Magento_InventorySalesApi' => 1,
        'Magento_InventoryGroupedProduct' => 1,
        'Magento_InventoryGroupedProductAdminUi' => 1,
        'Magento_InventoryGroupedProductIndexer' => 1,
        'Magento_InventoryImportExport' => 1,
        'Magento_InventoryInStorePickupApi' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 1,
        'Magento_InventorySourceSelectionApi' => 1,
        'Magento_InventoryInStorePickup' => 1,
        'Magento_InventoryInStorePickupGraphQl' => 1,
        'Magento_Shipping' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 1,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 1,
        'Magento_InventoryInStorePickupSales' => 1,
        'Magento_InventoryInStorePickupSalesApi' => 1,
        'Magento_InventoryInStorePickupQuote' => 1,
        'Magento_InventoryInStorePickupShipping' => 1,
        'Magento_InventoryInStorePickupShippingAdminUi' => 1,
        'Magento_Multishipping' => 1,
        'Magento_Webapi' => 1,
        'Magento_InventoryCache' => 1,
        'Magento_InventoryLowQuantityNotification' => 1,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 1,
        'Magento_InventoryMultiDimensionalIndexerApi' => 1,
        'Magento_InventoryProductAlert' => 1,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 1,
        'Magento_InventoryReservations' => 1,
        'Magento_InventoryReservationCli' => 1,
        'Magento_InventoryReservationsApi' => 1,
        'Magento_InventoryExportStock' => 1,
        'Magento_InventorySalesAdminUi' => 1,
        'Magento_CatalogInventoryGraphQl' => 1,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 1,
        'Magento_InventorySetupFixtureGenerator' => 1,
        'Magento_InventoryShipping' => 1,
        'Magento_InventoryShippingAdminUi' => 1,
        'Magento_InventorySourceDeductionApi' => 1,
        'Magento_InventorySourceSelection' => 1,
        'Magento_InventoryInStorePickupFrontend' => 1,
        'Magento_InventorySwatchesFrontendUi' => 1,
        'Magento_InventoryVisualMerchandiser' => 1,
        'Magento_InventoryWishlist' => 1,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_Marketplace' => 0,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_AdobeStockAsset' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_AdobeStockImageAdminUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_ConfigurableImportExport' => 0,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_InventoryInStorePickupMultishipping' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 0,
        'Magento_CustomerGraphQl' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_OrderCancellation' => 1,
        'Magento_OrderCancellationGraphQl' => 1,
        'Magento_OrderCancellationUi' => 1,
        'Magento_Sitemap' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_CardinalCommerce' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_ServiceProxy' => 1,
        'Magento_Vault' => 1,
        'Magento_PaymentServicesDashboard' => 1,
        'Magento_PaymentServicesPaypalGraphQl' => 1,
        'Magento_QueryXml' => 1,
        'Magento_ServicesConnector' => 1,
        'Magento_Paypal' => 1,
        'Magento_PaypalGraphQl' => 1,
        'Magento_Persistent' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_ServicesId' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 0,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaSendFriend' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 1,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_AwsS3' => 1,
        'Magento_Rss' => 1,
        'Magento_PageBuilderAdminAnalytics' => 1,
        'Magento_ServicesIdGraphQlServer' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_SalesAnalytics' => 0,
        'Magento_ServicesIdLayout' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_SalesRuleGraphQl' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_UrlRewrite' => 1,
        'Magento_Elasticsearch7' => 1,
        'Magento_CatalogAnalytics' => 0,
        'Magento_Securitytxt' => 1,
        'Magento_SendFriend' => 1,
        'Magento_SendFriendGraphQl' => 1,
        'Magento_PaymentServicesBase' => 1,
        'Magento_SaaSCommon' => 1,
        'Magento_SalesDataExporter' => 1,
        'Magento_StoreDataExporter' => 1,
        'Magento_PaymentServicesPaypal' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 0,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_PaymentServicesSaaSExport' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_Swagger' => 1,
        'Magento_SwaggerWebapi' => 1,
        'Magento_SwaggerWebapiAsync' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_TaxGraphQl' => 1,
        'Magento_TaxImportExport' => 1,
        'Magento_InventoryGraphQl' => 1,
        'Magento_ThemeGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_AdminAdobeImsTwoFactorAuth' => 0,
        'Magento_GoogleOptimizer' => 0,
        'Magento_SampleData' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_InventoryConfigurableProductFrontendUi' => 1,
        'Magento_PaypalCaptcha' => 1,
        'Magento_VaultGraphQl' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 1,
        'Magento_WebapiAsync' => 1,
        'Magento_WebapiSecurity' => 1,
        'Magento_Weee' => 1,
        'Magento_WeeeGraphQl' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_Wishlist' => 1,
        'Magento_WishlistAnalytics' => 0,
        'Magento_WishlistGraphQl' => 1,
        'Astound_Affirm' => 1,
        'Aheadworks_Ajaxcartpro' => 1,
        'Aheadworks_Giftcard' => 1,
        'Aitoc_AddressAutocomplete' => 1,
        'Aitoc_Core' => 1,
        'Amasty_Base' => 1,
        'Amasty_ShopbyBase' => 1,
        'Amasty_Mage24Fix' => 1,
        'Amasty_Shopby' => 1,
        'Amasty_ShopbyBrand' => 1,
        'Amasty_ShopbySeo' => 1,
        'Amasty_ShopbyGraphQl' => 1,
        'Amasty_ShopbyPage' => 1,
        'Amasty_ShopbyPro' => 1,
        'Amasty_GroupedOptions' => 1,
        'Affirm_Telesales' => 1,
        'Bss_AdminActionLog' => 1,
        'Extend_Warranty' => 1,
        'Extend_CustomProductSyncCommand' => 1,
        'Extend_CustomProductNameAdmin' => 1,
        'FishPig_WordPress' => 1,
        'FishPig_WordPress_Yoast' => 1,
        'Hyva_Theme' => 1,
        'Hyva_AmastyShopby' => 1,
        'Hyva_AmastyShopbyBase' => 1,
        'Hyva_AmastyShopbyBrand' => 1,
        'Hyva_CmsTailwindJit' => 1,
        'Hyva_CompatModuleFallback' => 1,
        'Hyva_Email' => 1,
        'Hyva_GraphqlTokens' => 1,
        'Hyva_GraphqlViewModel' => 1,
        'Hyva_ThemeFallback' => 1,
        'Hyva_OrderCancellationWebapi' => 1,
        'Signifyd_Connect' => 1,
        'Hyva_AheadworksGiftcard' => 1,
        'Hyva_LumaCheckout' => 1,
        'InchooDev_CloudflareTurnstile' => 1,
        'InchooDev_GoogleTagManager' => 1,
        'InchooDev_GoogleSync' => 1,
        'InchooDev_LocalStorePickup' => 1,
        'InchooDev_SocialConnect' => 1,
        'InchooDev_UrlRewriteImport' => 1,
        'Inchoo_AddressFix' => 1,
        'Inchoo_Affirm' => 1,
        'Inchoo_Ajaxcartpro' => 1,
        'Inchoo_AmastyShopby' => 1,
        'Inchoo_AmastyShopbyPage' => 1,
        'Inchoo_AmastyShopbySeo' => 1,
        'Inchoo_Base' => 1,
        'Inchoo_CFImageResize' => 1,
        'Inchoo_Catalog' => 1,
        'Keh_Base' => 1,
        'Inchoo_Checkout' => 1,
        'Inchoo_Cms' => 1,
        'Inchoo_CmsSeo' => 1,
        'Inchoo_CompareProducts' => 1,
        'Inchoo_CustomPromo' => 1,
        'Inchoo_CustomReport' => 1,
        'Inchoo_CustomerAjaxLogin' => 1,
        'Inchoo_Email' => 1,
        'Inchoo_Event' => 1,
        'Inchoo_ExtendWarranty' => 1,
        'Inchoo_FedExHoldAtLocation' => 1,
        'Inchoo_FedexPriceModifier' => 1,
        'Inchoo_GiftCard' => 1,
        'Inchoo_GuestContributor' => 1,
        'Inchoo_HeaderBanner' => 1,
        'Inchoo_InchooDevCloudflareTurnstile' => 1,
        'Inchoo_InchooDevGoogleTagManager' => 1,
        'Inchoo_InchooDevSocialConnect' => 1,
        'Inchoo_KehShipping' => 1,
        'Inchoo_WordPress' => 1,
        'Mexbs_Tieredcoupon' => 1,
        'Inchoo_Migration' => 1,
        'Inchoo_Newsletter' => 1,
        'Inchoo_Noibu' => 1,
        'PayPal_Braintree' => 1,
        'Inchoo_ProductPage' => 1,
        'Keh_Product' => 1,
        'Inchoo_PromoCodeMessages' => 1,
        'Inchoo_Recaptcha' => 1,
        'Inchoo_Rule' => 1,
        'Inchoo_Sales' => 1,
        'Inchoo_SalesRule' => 1,
        'Inchoo_SalesRuleDescriptor' => 1,
        'Inchoo_Search' => 1,
        'Inchoo_SharedCart' => 1,
        'Inchoo_ShippingAddress' => 1,
        'Inchoo_Signifyd' => 1,
        'Inchoo_Sitemap' => 1,
        'Inchoo_Sorting' => 1,
        'Taxjar_SalesTax' => 1,
        'Inchoo_Testimonial' => 1,
        'Inchoo_TrackingOptOut' => 1,
        'Inchoo_Widget' => 1,
        'Inchoo_Wishlist' => 1,
        'Inchoo_KlevuBlogFeed' => 1,
        'JustBetter_Sentry' => 1,
        'Keh_Badge' => 1,
        'Keh_Tracking' => 1,
        'Keh_Bloomreach' => 1,
        'Keh_BloomreachAheadworks' => 1,
        'Keh_BloomreachCache' => 1,
        'Keh_BloomreachEngagement' => 1,
        'Keh_BloomreachEngagementEmails' => 1,
        'Keh_Clarity' => 1,
        'Keh_Csp' => 1,
        'Keh_Customer' => 1,
        'Keh_CustomerSupport' => 1,
        'Keh_FedEx' => 1,
        'Keh_GiftCardTaxjar' => 1,
        'Keh_HappyFox' => 0,
        'Keh_ImageConverter' => 1,
        'Keh_Inventory' => 1,
        'Keh_InventoryReservation' => 1,
        'Keh_Kafka' => 1,
        'Keh_Lcp' => 1,
        'Magestore_Core' => 1,
        'Magestore_Appadmin' => 1,
        'Keh_Meta' => 1,
        'Keh_NetSuite' => 1,
        'Keh_NorthBeam' => 1,
        'Keh_SellApp' => 1,
        'Keh_PBExtend' => 1,
        'Inchoo_ProductPageM1' => 1,
        'Keh_Sales' => 1,
        'Keh_NorthBeamSellApp' => 1,
        'Keh_ShareSale' => 1,
        'Keh_ShopperApproved' => 1,
        'Snowdog_Menu' => 1,
        'Keh_TikTok' => 1,
        'Inchoo_CatalogSearch' => 1,
        'Keh_TradeVoucher' => 1,
        'Magestore_AdjustStock' => 1,
        'Magestore_WebposIntegration' => 1,
        'Magestore_BarcodeSuccess' => 1,
        'Magestore_BranchRequest' => 1,
        'Magestore_Webpos' => 1,
        'Magestore_ClickAndCollectApi' => 1,
        'Magestore_ClickAndCollect' => 1,
        'Magestore_ClickAndCollectFrontend' => 1,
        'Magestore_ClickAndCollectGraphQl' => 1,
        'Magestore_Customercredit' => 1,
        'Magestore_CoreFrontend' => 1,
        'Keh_MagestoreCustomerCredit' => 1,
        'Magestore_CustomercreditGraphQl' => 1,
        'Magestore_Deli' => 1,
        'Magestore_OrderSuccess' => 1,
        'Magestore_FulfilSuccess' => 1,
        'Magestore_FulfilReport' => 1,
        'Magestore_InventoryMovement' => 1,
        'Magestore_InventoryMovementAdminUi' => 1,
        'Magestore_InventoryMovementApi' => 1,
        'Magestore_LoggerApi' => 1,
        'Magestore_Logger' => 1,
        'Magestore_LoggerGraphQl' => 1,
        'Magestore_SupplierSuccess' => 1,
        'Magestore_Payment' => 1,
        'Magestore_PaymentOffline' => 1,
        'Magestore_ReportSuccess' => 1,
        'Magestore_DropshipSuccess' => 1,
        'Magestore_PosReports' => 1,
        'Magestore_Rewardpoints' => 1,
        'Magestore_RewardpointsBugFix' => 1,
        'Magestore_RewardpointsGraphQl' => 1,
        'Magestore_SalesReport' => 1,
        'Magestore_SecondDisplay' => 1,
        'Magestore_Stocktaking' => 1,
        'Magestore_PurchaseOrderSuccess' => 1,
        'Magestore_TransferStock' => 1,
        'Keh_MagestoreWebPos' => 1,
        'Magestore_WebposAdyenTerminal' => 1,
        'Magestore_WebposBugFix' => 1,
        'Magestore_WebposClarity' => 1,
        'Magestore_WebposDojo' => 1,
        'Magestore_WebposDojoAdminUi' => 1,
        'Magestore_ClickAndCollectAdminUi' => 1,
        'Magestore_WebposMobile' => 1,
        'Magestore_WebposMonerisApi' => 1,
        'Magestore_WebposPerformanceAdminUi' => 1,
        'Magestore_WebposMoneris' => 1,
        'Magestore_WebposMonerisGraphQl' => 1,
        'Magestore_WebposPerformance' => 1,
        'Magestore_WebposMonerisAdminUi' => 1,
        'Magestore_WebposPerformanceApi' => 1,
        'Magestore_WebposPusherApi' => 1,
        'Magestore_WebposPusherAdminUi' => 1,
        'Magestore_WebposPusher' => 1,
        'Magestore_WebposShipping' => 1,
        'Magestore_WebposStripe' => 1,
        'Magestore_WebposStripeTerminal' => 1,
        'Magestore_WebposTyro' => 1,
        'Inchoo_MexbsTieredcoupon' => 1,
        'Mirasvit_Core' => 1,
        'Mirasvit_Report' => 1,
        'Mirasvit_Rma' => 1,
        'Inchoo_PayPalBraintree' => 1,
        'PayPal_BraintreeCustomerBalance' => 1,
        'PayPal_BraintreeGiftCardAccount' => 1,
        'PayPal_BraintreeGiftWrapping' => 1,
        'PayPal_BraintreeGraphQl' => 1,
        'Hyva_SignifydConnect' => 1,
        'Keh_SnowdogMenu' => 1,
        'StripeIntegration_Tax' => 1,
        'StripeIntegration_Payments' => 1,
        'Inchoo_Taxjar' => 1,
        'Temando_ShippingRemover' => 1
    ]
];
