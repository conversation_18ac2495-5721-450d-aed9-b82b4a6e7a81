<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\ProductAlert;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var ProductAlert $productAlertViewModel */
$productAlertViewModel = $viewModels->require(ProductAlert::class);

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var Product $product */
$product = $currentProduct->get();

if (!$product->getId() || !$productAlertViewModel->showStockAlert($product)) {
    return;
}

$signupLabel = __("Notify me!");
?>
<div class="product alert stock text-right">
    <a href="<?= $escaper->escapeUrl($productAlertViewModel->getSaveUrl($currentProduct->get(), 'stock')) ?>"
       title="<?= $escaper->escapeHtml($signupLabel) ?>" class="btn btn-primary btn-size-s flex gap-1 items-center">
       <?= $heroicons->bellHtml('w-4 h-4 inline') ?>
       <?= $escaper->escapeHtml($signupLabel) ?>
    </a>
</div>
