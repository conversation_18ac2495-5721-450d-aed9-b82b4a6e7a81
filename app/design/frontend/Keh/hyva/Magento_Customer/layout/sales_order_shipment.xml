<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="order_items">
            <arguments>
                <argument name="th_css_class" xsi:type="string">py-2 text-sm font-normal col text-center</argument>
                <argument name="tr_css_class" xsi:type="string">max-md:border-b max-md:border-neutral-800 flex flex-col my-2 md:p-0</argument>
                <!-- td_css_class is implemented in Magento_Sales::items/renderer/default.phtml -->
            </arguments>
        </referenceBlock>
    </body>
</page>
