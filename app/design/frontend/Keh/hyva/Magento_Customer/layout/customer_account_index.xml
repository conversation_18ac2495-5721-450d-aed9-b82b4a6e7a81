<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Customer My Account (All Pages)" design_abstraction="custom">
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="page_title" xsi:type="string">My Dashboard</argument>
            </action>
        </referenceBlock>

         <referenceBlock name="customer_account_dashboard_top">
            <arguments>
                <argument name="th_css_class" xsi:type="string">py-2 text-sm font-normal col</argument>
                <argument name="tr_css_class" xsi:type="string">border-b max-md:border-neutral-800 flex flex-col md:table-row my-2 md:p-0</argument>
                <argument name="td_css_class" xsi:type="string">
                    <![CDATA[md:p-3 text-center text-sm md:text-base col max-md:flex max-md:gap-2 max-md:justify-between max-md:text-right max-md:before:content-[attr(data-th)] max-md:p-2 max-md:before:text-left max-md:before:uppercase max-md:before:font-bold [&:not(:last-child)]:max-md:border-b]]>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
