<?php
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Address\Grid;
use Magento\Framework\Escaper;

/** @var Grid $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
$td_css_class = $block->getTdCssClass() ?? '';
?>

<div class="block block-addresses-list mt-4">
    <div class="block-title">
        <h3><?= $escaper->escapeHtml(__('Additional Address Entries')) ?></h3>
    </div>
    <div class="block-content" x-data="initAddresses()">
        <?php if ($additionalAddresses = $block->getAdditionalAddresses()): ?>
            <div class="flex flex-col my-6">
                <div class="hidden md:grid grid-cols-8 text-sm bg-neutral-400">
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('Name')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('Street Address')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('City')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('Country')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('State')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('Zip/Postal Code')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('Phone')) ?>
                    </div>
                    <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>">
                        <?= $escaper->escapeHtml(__('Action')) ?>
                    </div>
                </div>

                <?php foreach ($additionalAddresses as $address): ?>
                    <div class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
                        <div class="md:grid grid-cols-8">
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtmlAttr(__('Name')) ?>">
                                <?= $escaper->escapeHtml($address->getFirstname()) ?> <?= $escaper->escapeHtml($address->getLastname()) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtmlAttr(__('Street Address')) ?>">
                                <?= $escaper->escapeHtml($block->getStreetAddress($address)) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtmlAttr(__('City')) ?>">
                                <?= $escaper->escapeHtml($address->getCity()) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtmlAttr(__('Country')) ?>">
                                <?= $escaper->escapeHtml($block->getCountryByCode($address->getCountryId())) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtmlAttr(__('State')) ?>">
                                <?= $escaper->escapeHtml($address->getRegion()->getRegion()) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtmlAttr(__('Zip/Postal Code')) ?>">
                                <?= $escaper->escapeHtml($address->getPostcode()) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> wrap-anywhere" data-th="<?= $escaper->escapeHtmlAttr(__('Phone')) ?>">
                                <?= $escaper->escapeHtml($address->getTelephone()) ?>
                            </div>
                            <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>  text-center flex items-center justify-end md:flex-col md:justify-start"
                                data-th="<?= $escaper->escapeHtmlAttr(__('Action')) ?>">
                                <div>
                                    <a class="action edit inline-block"
                                        title="<?= $escaper->escapeHtmlAttr(__('Edit')) ?>"
                                        href="<?= $escaper->escapeUrl($block->getUrl(
                                            'customer/address/edit',
                                            ['id' => $address->getId()]
                                        )) ?>">
                                        <?= $escaper->escapeHtml(__('Edit')) ?>
                                        <span></span>
                                    </a>
                                    <span class="md:hidden">
                                        <?=$escaper->escapeHtml('|');?>
                                    </span>
                                    <a class="action delete inline-block"
                                        title="<?= $escaper->escapeHtmlAttr(__('Delete')) ?>"
                                        @click.prevent="deleteAddressById(<?= $escaper->escapeJs($address->getId()) ?>)"
                                        href="#">
                                        <?= $escaper->escapeHtml(__('Delete')) ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php if ($block->getChildHtml('pager')): ?>
                <div class="customer-addresses-toolbar toolbar bottom">
                    <?= $block->getChildHtml('pager') ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <p class="empty">
                <?= $escaper->escapeHtml(__('You have no other address entries in your address book.')) ?>
            </p>
        <?php endif ?>

        <script>
            function initAddresses() {
                return {
                    deleteAddressById(id) {
                        if (window.confirm(
                            '<?= $escaper->escapeJs(__('Are you sure you want to delete this address?')) ?>'
                        )) {
                            hyva.postForm(
                                {
                                    "action": '<?= $escaper->escapeJs($block->getDeleteUrl()) ?>',
                                    "data": {
                                        "id": id
                                    }
                                }
                            );
                        }
                    }
                }
            }
        </script>
    </div>
</div>

<div class="actions-toolbar">
    <div class="primary">
        <a href="<?= $escaper->escapeUrl($block->getUrl('customer/address/new')) ?>"
           class="btn btn-primary btn-size-m">
            <?= $escaper->escapeHtml(__('Add New Address')) ?>
        </a>
    </div>
</div>
