<?php
declare(strict_types=1);

use Hyva\Theme\Block\SortableItem;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CustomerRegistration;
use Magento\Framework\Escaper;

/** @var SortableItem $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var CustomerRegistration $customerRegistration */

$customerRegistration = $viewModels->require(CustomerRegistration::class);

$label = $block->getLabel() ? $block->getLabel() : __('Create an Account');
?>

<?php if ($customerRegistration->isAllowed()): ?>
<div class="p-4 bg-neutral-100 text-center pb-8">
    <p class="text-center text-neutral-800 uppercase text-sm my-4">
        <?php echo __('New here? Don\'t have an account?');?>
    </p>
    <a id="<?= $escaper->escapeHtmlAttr($block->getNameInLayout()) ?>"
       class="btn btn-secondary btn-size-s py-2"
       href="<?= $escaper->escapeUrl($customerRegistration->getRegisterUrl()) ?>"
       title="<?= $escaper->escapeHtmlAttr($label) ?>"
    >
        <?= $escaper->escapeHtml($label) ?>
    </a>
</div>
<?php endif; ?>
