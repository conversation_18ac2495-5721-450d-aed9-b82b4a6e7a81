<?php
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Items;
use Magento\Shipping\Helper\Data as ShippingHelper;

/** @var Items $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$order = $block->getOrder();
$shippingHelper = $this->helper(ShippingHelper::class);
$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
?>
<?php foreach ($order->getShipmentsCollection() as $shipment): ?>
    <?php $tracks = $shipment->getTracksCollection(); ?>
    <div class="order-title flex gap-4 items-center md:text-left font-semibold mb-2">
        <p><?= $escaper->escapeHtml(__('Shipment #')) ?><?= $escaper->escapeHtml($shipment->getIncrementId()) ?></p>
        <a href="<?= $escaper->escapeUrl($block->getUrl('*/*/printShipment', ['shipment_id' => $shipment->getId()])) ?>"
           class="text-primary inline-block font-normal"
           target="_blank"
           rel="noopener">
            <?= $heroicons->printerHtml('inline-block', 20, 20); ?>
            <span class="inline-block ml-1"><?= $escaper->escapeHtml(__('Print Shipment')) ?></span>
        </a>
        <?php if ($tracks->count()): ?>
            <a href="<?= $escaper->escapeUrl($shippingHelper->getTrackingPopupUrlBySalesModel($shipment)) ?>"
               target="_blank" rel="noopener" title="<?= $escaper->escapeHtml(__('Track Shipment')) ?>"
               class="block font-normal my-1">
                <?= $heroicons->searchHtml('inline-block', 20, 20); ?>
                <span class="inline-block ml-1"><?= $escaper->escapeHtml(__('Track Shipment')) ?></span>
            </a>
        <?php endif; ?>
    </div>
    <?php if ($tracks->count()): ?>
        <div class="my-2">
            <p><?= $escaper->escapeHtml(__('Tracking Number(s):')) ?></p>
            <div class="tracking-content">
                <?php
                $i    = 1;
                $size = $tracks->count();
                foreach ($tracks as $track): ?>
                    <?php if ($track->isCustom()): ?>
                        <?= $escaper->escapeHtml($track->getNumber()) ?>
                    <?php else: ?>
                        <a href="<?= $escaper->escapeUrl($shippingHelper->getTrackingPopupUrlBySalesModel($track)) ?>"
                           target="_blank" rel="noopener"
                           class="inline-block"><span><?= $escaper->escapeHtml($track->getNumber()) ?></span>
                        </a>
                    <?php endif; ?>
                    <?php if ($i != $size): ?>, <?php endif; ?>
                    <?php $i++;
                endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="order-items shipment">
        <div class="hidden bg-neutral-400 md:grid grid-cols-5 text-sm">
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> col-span-3">
                <?= $escaper->escapeHtml(__('Product Name')) ?>
            </div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('SKU')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Qty Shipped')) ?></div>
        </div>
        <?php $items = $shipment->getAllItems(); ?>
        <?php foreach ($items as $item): ?>
            <?php if (!$item->getOrderItem()->getParentItem()): ?>
                <div class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
                    <?= $block->getItemHtml($item) ?>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
<?php endforeach; ?>
