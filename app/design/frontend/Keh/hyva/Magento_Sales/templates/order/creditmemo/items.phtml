<?php
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Creditmemo\Items;

/** @var Escaper $escaper */
/** @var Items $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$order = $block->getOrder();
$order = $block->getOrder();
$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
?>
<?php foreach ($order->getCreditmemosCollection() as $creditmemo): ?>
    <div class="order-title flex gap-4 items-center md:text-left font-semibold mb-2">
        <p><?= $escaper->escapeHtml(__('Refund #')) ?><?= $escaper->escapeHtml($creditmemo->getIncrementId()) ?></p>
        
        <a href="<?= $escaper->escapeUrl($block->getPrintCreditmemoUrl($creditmemo)) ?>"
            class="text-primary inline-block font-normal"
            target="_blank"
            rel="noopener">
            <?= $heroicons->printerHtml('inline-block', 20, 20); ?>
            <span class="inline-block ml-1 underline">
                <?= $escaper->escapeHtml(__('Print Refund')) ?>
            </span>
        </a>
    </div>

    <?= $block->getCommentsHtml($creditmemo) ?>

    <div class="order-items creditmemo">
        <div class="hidden md:grid grid-cols-7 text-sm bg-neutral-400">
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> col-span-2"><?= $escaper->escapeHtml(__('Product Name')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('SKU')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Qty')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Subtotal')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Discount Amount')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Row Total')) ?></div>
        </div>
        <?php $items = $creditmemo->getAllItems(); ?>
        <?php foreach ($items as $item): ?>
            <?php if (!$item->getOrderItem()->getParentItem()): ?>
                <div class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
                    <?= $block->getItemHtml($item) ?>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        <div class="flex justify-end mt-2">
            <div class="grid grid-cols-4 text-center p-2 gap-4 w-full">
                <?= $block->getTotalsHtml($creditmemo) ?>
            </div>
        </div>
    </div>
<?php endforeach; ?>
