<?php
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Invoice\Items;

/** @var Escaper $escaper */
/** @var Items $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$order = $block->getOrder();
$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
?>
<?php foreach ($order->getInvoiceCollection() as $invoice): ?>
    <div class="order-title flex gap-4 items-center md:text-left font-semibold mb-2">
        <p><?= $escaper->escapeHtml(__('Invoice #')) ?><?= $escaper->escapeHtml($invoice->getIncrementId()) ?></p>
        <a href="<?= $escaper->escapeUrl($block->getPrintInvoiceUrl($invoice)) ?>"
           class="text-primary inline-block font-normal"
           target="_blank"
           rel="noopener">
            <?= $heroicons->printerHtml('inline-block', 20, 20); ?>
            <span class="inline-block"><?= $escaper->escapeHtml(__('Print Invoice')) ?></span>
        </a>
    </div>
    <?= $block->getInvoiceCommentsHtml($invoice) ?>
    <div class="order-items invoice">
        <div class="hidden md:grid grid-cols-6 text-sm bg-neutral-400 ">
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> col-span-3"><?= $escaper->escapeHtml(__('Product Name')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('SKU')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Qty Invoiced')) ?></div>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Subtotal')) ?></div>
        </div>
        <?php $items = $invoice->getAllItems(); ?>
        <?php foreach ($items as $item): ?>
            <?php
            $orderItem = $item->getOrderItem();
            $parentItem = $orderItem->getParentItem();
            if ($parentItem && $parentItem->getData('product_type') === Configurable::TYPE_CODE) {
                continue;
            }
            ?>
             <div class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
                <?= $block->getItemHtml($item) ?>
            </div>
        <?php endforeach; ?>
        <div class="flex justify-end mt-2">
            <div class="grid grid-cols-4 text-center p-2 gap-4 w-full">
                <?= $block->getInvoiceTotalsHtml($invoice) ?>
            </div>
        </div>
    </div>
<?php endforeach; ?>
