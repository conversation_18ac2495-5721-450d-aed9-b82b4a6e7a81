<?php

use Keh\Bloomreach\ViewModel\Search;
use Magento\Catalog\Block\Category\View;
use Magento\Framework\Escaper;
use Magento\LayeredNavigation\Block\Navigation\State;

/** @var View $block */
/** @var Escaper $escaper */

/** @var Search $viewModel */
$viewModel = $block->getData('view_model');

/** @var boolean $isCategoryPage */
$isCategoryPage = $block->getData('is_category_page');

/** @var State $currentState */
$currentState = $block->getChildBlock('state');

$appliedFilters = $viewModel->getCurrentStateFilters($currentState, $isCategoryPage);
$filters = $isCategoryPage && !empty($appliedFilters) ? json_encode($appliedFilters) : '{}';
$extraFilterClass = strlen($filters) > 2 ? 'min-h-[130px] lg:min-h-[76px]' : '';
$priceFormat= $viewModel->getPriceFormatJson();

$productQtyViewModel = $block->getProductQtyViewModel();
$scarcityThreshold = $productQtyViewModel->getScarcityThreshold();
$clearUrl = $isCategoryPage ? $viewModel->getClearUrl($currentState) : '';

//Default mapping for demo purposes only
$categoryId = $isCategoryPage ? $block->getCurrentCategory()->getId() : '';

$categoryName = $isCategoryPage ? $block->getCurrentCategory()->getName() : '';
$categoryPathIds = $isCategoryPage ? $block->getCurrentCategory()->getPathIds() : '';

$handles = $block->getLayout()->getUpdate()->getHandles();
$isAmastyShoByPage = in_array('amshopby_index_index', $handles);
?>
<?= $block->getChildHtml('js-helper') ?>

<?php if ($isAmastyShoByPage) : ?>
<div class="category-image-wrapper">
    <?php echo $block->getChildHtml('category.view.container') ?>
</div>
<?php endif; ?>
<div id="bloomreachCategory"
     class="br-search"
     :class="{ 'br-no-results': products.length <= 0 }"
     x-data="productSearchComponent()">
    <?php echo $block->getChildHtml('loading') ?>

    <div class="br-search-wrapper lg:flex lg:flex-row">
        <?php echo $block->getChildHtml('filters') ?>
        <div id="bloomreachSearchContent" class="br-search-content relative w-full mb-8 lg:ml-5" x-ref="BloomreachSearchContent" >
            <?php echo $block->getChildHtml('popular.brands');?>
            <div class="br-tp-container relative">
                <?php echo $block->getChildHtml('category-chips') ?>
                <div class="z-surface <?php echo $escaper->escapeHtml($extraFilterClass) ?>">
                    <?php echo $block->getChildHtml('toolbar') ?>
                </div>
            </div>

            <?= $block->getChildHtml('product-list') ?>
        </div>
    </div>
    <p x-cloak
       x-show="!isLoading && products.length <= 0 && isFiltered"
       class="br-noresults text-lg text-center px-[5%] py-6"
       data-pw="br-noresults-filtered">
        <?= $escaper->escapeHtml(__('No products found...')); ?>
    </p>
    <p x-cloak
       x-show="!isLoading && products.length <= 0 && !isFiltered"
       class="br-noresults text-lg text-center px-[5%] pb-6"
       data-pw="br-noresults"
    >
        <?= $escaper->escapeHtml(__('Please try another search term...')); ?>
    </p>
</div>
<?php if (!$isAmastyShoByPage) : ?>
    <?php echo $block->getChildHtml('category.view.container') ?>
<?php endif; ?>

<script>
    'use strict'

    function productSearchComponent() {
        return {
            clearUrl: "<?php echo $clearUrl; ?>",
            currentState: <?php echo !empty($appliedFilters) ? json_encode($appliedFilters) : '{}' ?>,
            categoryId: "<?php echo $categoryId; ?>",
            categoryName: "<?php echo $categoryName; ?>",
            categoryPathIds: <?php echo json_encode($categoryPathIds); ?>,
            priceFormat: <?php echo $priceFormat; ?>,
            isCategory: "<?php echo $isCategoryPage; ?>",
            isFiltered: false,
            pageSize: window.innerWidth > 1899 ? <?= $viewModel->getPageSize() ?> || 35 : 36,
            initialCategoryLoad: "<?= (bool)$categoryId?>",
            storeCode: "<?= $viewModel->getStoreCode() ?>",
            amShopByUrl: "<?= $viewModel->getAmastyShopByUrl() ?>",
            connectionConfigs: <?= $viewModel->getConnectionConfigs() ?>,
            facetValueFormat: <?= $viewModel->getSliderFormats() ?>,
            defaultFacetValues: <?= json_encode($viewModel->getFacetsDefaultValues()) ?>,
            filtersWithSearch: <?= json_encode($viewModel->getFiltersWithSearchField()) ?>,
            blFilterLabels: <?= json_encode($viewModel->getBlFiltersLabels()) ?>,
            facetRemove: false,
            facets: [],
            products: [],
            hideFiltersSidebar: false,
            hideCategoryChips: false,
            shimmerWrappers: [
                '#bloomreachSearchSidebar',
                '#bloomreachSearchContent',
                '#bloomreachSearchResults',
                '#bloomreachCurrentFilters',
                '#bloomreachCampaignBanner',
                '#bloomreachCategoryChips'
            ],
            categoryChips: [],
            currentFilters: <?php echo $filters ?>,
            page: 1,
            sortBy: '',
            productsCount: 1,
            filterOpenState: false,
            isLoading: true,
            pagesCount: 1,
            campaignBannerHtml: null,

            init() {
                this.isLoading = true;
                this.getStateFromUrl();

                const urlParams = new URLSearchParams(window.location.search)
                const sortBy = urlParams.get('sortBy');

                if (sortBy) {
                    this.sortBy = sortBy;
                }

                this.updateFiltersWithDefaultValues();
                this.updateResults(false, false, true);
            },

            getStateFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                const filters = {};

                this.isLoading = true;

                for (const [key, value] of urlParams.entries()) {
                    if (key === 'q') continue;

                    if (key === 'pPgNo') {
                        this.page = parseInt(value, 10);
                    } else if (key === 'sortBy') {
                        this.sortBy = value;
                    } else {
                        const facetName = key.replace(/_/g, ' ');

                        if (!this.blFilterLabels.includes(facetName)) {
                            continue;
                        }

                        const facetValue = value.replace(/_/g, ' ');
                        const uniqueKey = `${facetName}_${facetValue}`;

                        filters[uniqueKey] = {
                            facet_name: facetName,
                            facet_value: facetValue
                        };
                    }
                }

                this.currentFilters = filters;
                this.isLoading = false;
            },

            // Update Product Results Based on Filters
            updateResults(appendProducts = false, updateRange, updateUrl) {
                appendProducts = appendProducts ? appendProducts : null;

                let connectionSettings = this.connectionConfigs;
                const query = new URL(window.location).searchParams.get("q");

                this.isLoading = true;

                if (!this.initialCategoryLoad) {
                    delete connectionSettings.timeout;
                }
                const pageParams = {
                    page: this.page,
                    pageSize: this.pageSize,
                    sort: this.sortBy,
                    facets: this.currentFilters,
                    categoryId: this.categoryId ? this.categoryId : 0
                };

                // Call Bloomreach server
                this.getBloomreachData(query, pageParams, connectionSettings)
                    .then((response) => {
                        if (response.timeout) {
                            // Call Magento server to get cached response for the Category
                            return this.getMagentoCache(query, pageParams);
                        }
                        return response;
                    })
                    .then((response) => {
                        if (response && response.keywordRedirect && response.keywordRedirect['redirected url']) {
                            let url = addHttpToUrl(response.keywordRedirect['redirected url']);
                            window.location.href = typeof getCorrectUrl === 'function' ? getCorrectUrl(url) : url
                            return;
                        }

                        this.initialCategoryLoad = false;
                        this.updateUi(response, appendProducts, updateUrl);
                        this.dispatchCategoryEvent(response);

                        if (updateRange) {
                            this.facetRemove = Date.now();
                        }
                    })
                    .catch((error) => {
                        console.error("Error in updateResults:", error);

                        typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                            [{
                                type: "error",
                                text: 'Something went wrong, please reload the page.'
                            }], 5000
                        );
                    })
                    .then(() => {
                        this.isLoading = false;

                        if (!appendProducts && this.$refs.BloomreachSearchContent && !this.isElementInView(this.$refs.BloomreachSearchContent)) {
                            this.$refs.BloomreachSearchContent.scrollIntoView()
                        }
                    });
            },

            updateUi: function (response, appendProducts, updateUrl) {
                cleanShimmerClasses(this.shimmerWrappers);
                insertCampaignBanner(response);

                if (response && response.response && !appendProducts) {
                    this.products = response.response.docs;
                    this.hideFiltersSidebar = this.products.length === 0;

                    const productsCount = response.response.numFound;
                    const facets = this.setFacets(response.facet_counts.facets);
                    const currentFilter = getCurrentFiltersState(this.currentFilters, facets);

                    this.facets = facets;
                    this.productsCount = productsCount;
                    this.pagesCount = Math.ceil(productsCount / this.pageSize)
                    cleanShimmerClasses(['#bloomreachSearchResults']);
                    addProductsCounter(productsCount);

                    if (response.response.docs.length === 0) {
                        this.filterOpenState = false;
                        return;
                    }

                    this.currentFilters = currentFilter;
                    if (updateUrl) {
                        cleanInvalidFiltersFromURL(facets, this.blFilterLabels);
                    } else {
                        setStateUrlForListing(currentFilter, this.page, this.sortBy);
                    }
                } else if (response && response.response) {
                    this.products = this.products.concat(response.response.docs);
                } else {
                    console.error("Can`t updateUi response?.response is false or undefined");
                }
            },

            setFacets(facets) {
                const { isCategory, categoryId } = this;

                return facets
                    .filter(({ type, value }) => !((type === 'number_stats' && value?.end === 0) || (Array.isArray(value) && value.length === 0)))
                    .map(facet => {
                        if (facet.name === 'category') {
                            const categoryChips = isCategory
                                ? facet.value.filter(({ parent }) => parent === categoryId)
                                : facet.value;

                            this.categoryChips = categoryChips;
                            this.hideCategoryChips = categoryChips.length === 0;

                            if (isCategory) {
                                facet.value = categoryChips;
                            }
                        }

                        return facet;
                    });
            },

            isElementInView(element) {
                const rect = element.getBoundingClientRect();
                const windowHeight = window.innerHeight || document.documentElement.clientHeight;

                return (
                    rect.top < windowHeight &&
                    rect.bottom > 0
                );
            },

            getBloomreachData(query, pageParams, connectionConfigs) {
                const url = this.buildRequestForBloomreach(
                    query, pageParams, connectionConfigs
                );

                return Promise.race(getPromises(url, connectionConfigs))
                    .then(function(response) {
                        return response.json();
                    })
                    .catch(function(error) {
                        if (error.message === 'Fetch request timed out') {
                            console.error('Fetch request timed out');
                            return { timeout: true };
                        } else {
                            console.error('Fetch request failed', error);
                            return {};
                        }
                    });
            },

            buildRequestForBloomreach(query, pageParams, connectionConfigs) {
                const page = pageParams.page || 1;
                const pageSize = pageParams.pageSize || 35;
                const sort = pageParams.sort;
                const facets = pageParams.facets || {};
                const categoryId = pageParams.categoryId || 0;
                const brCookieValue = hyva.getCookie('_br_uid_2');
                const br_uid = '_br_uid_2=' + brCookieValue || 'static_br_uid_2_value';

                if (!brCookieValue) {
                   console.error('Cookie wasn\'t found');
                }

                let params = {
                    'auth_key': connectionConfigs.auth_key,
                    'domain_key': connectionConfigs.domain_key,
                    'account_id': connectionConfigs.account_id,
                    'requestId': Date.now(),
                    'ref_url': document.referrer ? document.referrer : '',
                    'url': window.location.href,
                    'request_type': 'search',
                    'search_type': categoryId ? 'category' : 'keyword',
                    'q': categoryId ? categoryId : query,
                    'fl': 'pid,title,thumb_image,thumb_image_webp,url,quantity,min_price,max_price,keh_special_f,keh_manufacturer,grades,keh_badges,keh_film_type,keh_lens_type,keh_product_type,keh_system',
                    'rows': pageSize,
                    'start': (page - 1) * pageSize,
                };

                if (sort) {
                    params.sort = sort;
                }
                const facetsObject = getFacets(facets, this.blFilterLabels);

                if (Object.keys(facetsObject).length > 0) {
                    params.fq = Object.values(facetsObject).toString();
                }

                params = new URLSearchParams(params).toString();
                params = params.replace("ref_url=", `${br_uid}&ref_url=`);

                return connectionConfigs.api_url + '?' + params;
            },

            getMagentoCache(query, pageParams) {
                let url = this.buildRequestForMagento(query, pageParams);
                const options = { headers: { "Content-Type": "application/json" } };

                if (!url) return;

                return fetch(url, options)
                    .then(response => {
                        return response.json();
                    }).then(data => {
                        return JSON.parse(data);
                    })
                    .catch((error) => {
                        console.error("Search fetch error:", error);
                    });
            },

            buildRequestForMagento: function(query, pageParams) {
                const page = 1;
                const pageSize = pageParams.pageSize || 35;
                const sort = '_';
                const facets = '_';
                const categoryId = pageParams.categoryId;
                const currentUrl = encodeURIComponent(window.location.href || '');
                const referrerUrl = encodeURIComponent(document.referrer || window.location.href);

                if (!categoryId) return null;

                return BASE_URL + 'rest/V1/bloomreach_search/'
                    + encodeURIComponent(query ? query.replace('/', '') : '_') + '/'
                    + encodeURIComponent(page) + '/'
                    + encodeURIComponent(pageSize) + '/'
                    + encodeURIComponent(sort) + '/'
                    + encodeURIComponent(encodeURIComponent(facets)) + '/'
                    + encodeURIComponent(encodeURIComponent(currentUrl)) + '/'
                    + encodeURIComponent(encodeURIComponent(referrerUrl)) + '/'
                    + encodeURIComponent(categoryId);
            },

            removeFilter(filter) {
                let filters = this.currentFilters,
                    filterName = filter.facet_name + '_' + filter.facet_value;
                delete filters[filterName];

                this.currentFilters = filters;
                updateURLParam(filter.facet_name, filter.facet_value, 'remove');
                this.updateResults(false, true);
            },

            clearAllFilters() {
                this.currentFilters = {};
                this.updateResults(false, true);
                setStateUrlForListing({}, this.page, this.sortBy);
            },

            loadMore() {
                this.page += 1;
                this.updateResults(true);
            },

            normalize(str) {
                return str.toLowerCase().trim().replace(/\s+/g, ' ');
            },

            formatPrice(minPrice, maxPrice) {
                if (!minPrice && !maxPrice) return '';

                return minPrice !== maxPrice
                    ? hyva.formatPrice(minPrice, false) + '-' + hyva.formatPrice(maxPrice, false)
                    : hyva.formatPrice(minPrice, false);
            },

            updateOnSorting() {
                this.page = 1
                this.updateResults(false);
            },

            toggleFilter(facetName, valueName) {
                const currentValueName = valueName.name || valueName.cat_id;
                const key = `${facetName}_${currentValueName}`;

                if (this.currentFilters[key]) {
                    delete this.currentFilters[key];
                    updateURLParam(facetName, currentValueName, 'remove');
                } else {
                    this.currentFilters[key] = { facet_name: facetName, facet_value: currentValueName };
                    updateURLParam(facetName, currentValueName, 'add');
                }
                this.isFiltered = true;
                this.page = 1;
                this.updateResults();
            },

            isFilterActive(facet, value) {
                const currentValueName = value.name || value.cat_id;

                return !!this.currentFilters[`${facet}_${currentValueName}`];
            },

            setFacetFilterCounter(facet) {
                let count = 0;
                Object.values(this.currentFilters).forEach(filter => {
                    if (filter.facet_name === facet.name) count++;
                });
                return count;
            },

            getFacetValue(facet) {
                const facets = this.facets;
                const targetFacet = 'category';
                const formats = this.facetValueFormat || {};

                if (facet.facet_name === targetFacet) {
                    const catEl = document.querySelector(`[data-value="${facet.facet_value}"]`);
                    if (catEl) {
                        const catValue = catEl.querySelector('.facet-label').textContent;

                        if (catValue) return catValue;
                    }

                    const matchedFacet = findFacet('cat_id', facets, targetFacet, facet.facet_value);

                    return matchedFacet ? matchedFacet.cat_name : null;
                } else if (facet.facet_value.includes('!!') && facet.facet_value.includes(',')) {
                    return formatRangeValue(facet.facet_value.split(',')[0], formats[facet.facet_name]);
                } else {
                    return facet.facet_value;
                }
            },

            updateFiltersWithDefaultValues() {
                const existingFacetNames = new Set(
                    Object.values(this.currentFilters).map(f => f.facet_name)
                );

                for (const key in this.defaultFacetValues) {
                    const { facet_name } = this.defaultFacetValues[key];

                    if (!existingFacetNames.has(facet_name)) {
                        this.currentFilters[key] = this.defaultFacetValues[key];
                    }
                }
            },

            dispatchCategoryEvent(categoryData) {
                document.dispatchEvent(new CustomEvent('br.category.data', {
                    detail: {
                        products: categoryData.response.docs,
                        categories: categoryData.category_map,
                        categoryId: this.categoryId,
                        categoryName: this.categoryName,
                        pathIds: this.categoryPathIds
                    }
                }));
            },
        };
    }
</script>
