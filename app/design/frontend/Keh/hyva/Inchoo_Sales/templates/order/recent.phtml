<?php
declare(strict_types=1);

/** @var Magento\Sales\Block\Order\Recent $block */
/** @var Magento\Framework\Escaper $escaper */

$orders = $block->getOrders();
$count = count($orders);
$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
$td_css_class = $block->getTdCssClass() ?? '';
?>
<div class="block block-dashboard-orders mt-8">
    <div class="flex justify-between items-center">
        <h3 class="strong"><?= __('Recent Orders') ?></h3>
        <?php if ($count > 0): ?>
            <a class="action view" href="<?= $block->getUrl('sales/order/history') ?>">
                <span><?= __('View All') ?></span>
            </a>
        <?php endif; ?>
    </div>
    <div class="block-content">
        <?= $block->getChildHtml() ?>
        <?php if ($count > 0): ?>
            <div class="table-wrapper orders-recent">
                <table class="w-full" id="my-orders-table">
                    <thead class="hidden md:table-header-group">
                        <tr class="bg-neutral-400">
                            <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> id"><?= $escaper->escapeHtml(__('Order #')) ?></th>
                            <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> date"><?= $escaper->escapeHtml(__('Date')) ?></th>
                            <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> shipping"><?= $escaper->escapeHtml(__('Ship To')) ?></th>
                            <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> total"><?= $escaper->escapeHtml(__('Order Total')) ?></th>
                            <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> status"><?= $escaper->escapeHtml(__('Status')) ?></th>
                            <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> actions"><?= $escaper->escapeHtml(__('Action')) ?></th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($orders as $_order): ?>
                        <tr class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
                            <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> id" data-th="<?= $escaper->escapeHtml(__('Order #')) ?>"><?= $_order->getRealOrderId() ?></td>
                            <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> date" data-th="<?= $escaper->escapeHtml(__('Date')) ?>"><?= $block->formatDate($_order->getCreatedAt()) ?></td>
                            <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> shipping" data-th="<?= $escaper->escapeHtml(__('Ship To')) ?>"><?= $_order->getShippingAddress() ? $escaper->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
                            <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> total" data-th="<?= $escaper->escapeHtml(__('Order Total')) ?>"><?= $_order->formatPrice($_order->getGrandTotal()) ?></td>
                            <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> status" data-th="<?= $escaper->escapeHtml(__('Status')) ?>"><?= $this->helper('Inchoo\Sales\Helper\Order\Status')->getStatusHtml($_order) ?></td>
                            <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> actions" data-th="<?= $escaper->escapeHtml(__('Actions')) ?>">
                                <a href="<?= $block->getViewUrl($_order) ?>" class="action view">
                                    <span><?= $escaper->escapeHtml(__('View Order')) ?></span>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="message info empty"><span><?= $escaper->escapeHtml(__('You have placed no orders.')) ?></span></div>
        <?php endif; ?>
    </div>
</div>
