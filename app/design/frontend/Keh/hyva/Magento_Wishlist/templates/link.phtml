<?php
declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */

$label = $block->getData('label') ?? __('My Wish List');
$urlPath = $block->getData('path') ?? 'wishlist';
?>

<a
    id="<?= $escaper->escapeHtmlAttr($block->getNameInLayout()) ?>"
    class="block uppercase text-neutral-700 px-4 py-1 lg:px-5 hover:bg-gray-100 hover:text-keh relative"
    href="<?= $escaper->escapeUrl($block->getUrl($urlPath)) ?>"
    title="<?= $escaper->escapeHtmlAttr($label) ?>"
    x-data="{ itemCount: 0 }"
    @private-content-loaded.window="itemCount = $event.detail.data.wishlist ? parseInt($event.detail.data.wishlist.counter) : 0"
>
    <?= $escaper->escapeHtml($label) ?>
    <template x-if="itemCount">
        <span class="tabular-nums" x-text="`(${itemCount})`"></span>
    </template>
</a>
