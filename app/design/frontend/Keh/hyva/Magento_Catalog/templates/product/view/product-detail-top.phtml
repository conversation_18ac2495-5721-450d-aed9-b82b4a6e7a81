<?php
declare(strict_types=1);

use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

/** @var Product $product */
$product = $block->getProduct();
$productType = $product->getTypeId();

?>

<div class="flex flex-col mt-3 md:mt-0 mb-3">
    <?= $block->getChildHtml('page.main.title'); ?>
    <div class="flex flex-wrap gap-x-2 text-xs">
        <?= $block->getChildHtml('product.info.sku'); ?>
        <?= $block->getChildHtml('product.info.lens_not_included'); ?>
    </div>
    <div class="affirm-global-message">
        <?php echo $block->getChildHtml('aslowas.after.price'); ?>
    </div>
    <?php foreach ($block->getGroupChildNames('product.info.top.additional') as $childName): ?>
        <?=  $block->getChildBlock($childName)->toHtml() ?>
    <?php endforeach ?>
    <?= $block->getChildHtml('oos-wrapper'); ?>
</div>
