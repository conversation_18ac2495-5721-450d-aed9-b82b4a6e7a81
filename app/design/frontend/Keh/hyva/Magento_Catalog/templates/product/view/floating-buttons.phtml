<?php
declare(strict_types=1);

use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;
use Hyva\Theme\ViewModel\Wishlist;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\GroupedProduct\Block\Product\View\Type\Grouped;
use Keh\Inventory\ViewModel\StockAvailability;

/** @var Grouped $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var \Keh\Product\ViewModel\Grades $gradesViewModel */
$gradesViewModel = $block->getGradesViewModel();
/** @var StockAvailability $stockAvailabilityViewModel */
$stockAvailabilityViewModel = $block->getStockAvailabilityViewModel();

/** @var Product $product */
$product = $currentProduct->get();

/** @var \Inchoo\Catalog\ViewModel\DisableAddToCart $disableAddToCartViewModel */
$disableAddToCartViewModel = $block->getData('disable_add_to_cart_view_model');

$associatedProducts = $stockAvailabilityViewModel->getAvailableProducts($block->getAssociatedProducts());
$hasAssociatedProducts = count($associatedProducts) > 0;

if (!$product->getId() || !$hasAssociatedProducts) {
    return;
}

$preselected = $gradesViewModel->getPreselectedVariant((int)$product->getId(), $associatedProducts);
$disableAddToCart = (bool)$preselected?->getData('disallow_add_to_cart');
$disallowMessage = $disableAddToCartViewModel->getButtonLabel();
?>
<div class="flex items-center gap-2 fixed px-4 py-2 bg-white bottom-0 left-0 right-0 z-30 border border-t border-keh-border drop-shadow-floating lg:hidden">
    <?php if ($wishlistViewModel->isEnabled()) : ?>
        <button
            x-data="initFloatingWishlist('<?= $product->getSku() ?>')"
            x-defer="intersect"
            data-pw="floating-add-to-wish-list"
            @click="toggleWishlistItem('<?= $product->getSku() ?>', $data)"
            type="button"
            title="<?= $escaper->escapeHtmlAttr( __('Add to list') ); ?>"
            class="inline-flex gap-2 items-center justify-center relative flex-none btn btn-size-xl px-2.5"
            id="floatihg-product-addtocart-button">
            <span :class="$store.wishList.itemActive ? 'hidden' : ''"><?= $heroicons->starHtml('', 20, 20) ?></span>
            <span x-cloak
                :class="$store.wishList.itemActive ? '' : 'hidden'">
                <?= $heroiconsSolid->starHtml('', 20, 20) ?>
            </span>
            <span x-text="$store.wishList.itemActive ? '<?= $escaper->escapeHtml(__('Added to List')) ?>' : '<?= $escaper->escapeHtml(__('Add to List')) ?>'">
                <?= $escaper->escapeHtml(__('Add to List')) ?>
            </span>
        </button>
        <script>
            function initFloatingWishlist(sku) {
                return {
                    init() {
                        const item = this.getWishlistItem(sku);

                        Alpine.store('wishList', { itemActive: !!(item) });
                    },

                    getWishlistItem(sku) {
                        const mageStorage = JSON.parse(
                            hyva.getBrowserStorage().getItem('mage-cache-storage')
                        ) || {};
                        const wishlistItems = mageStorage.wishlist || {};

                        if (!wishlistItems.items) return false;

                        return wishlistItems.items.find(user => user.product_sku === sku.toString());
                    }
                }
            }
        </script>
    <?php endif; ?>
    <button
        type="submit"
        form="product_addtocart_form"
        title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
        data-addto="cart"
        class="btn btn-primary btn-size-xl px-0.5 w-full"
        id="floatihg-product-addtocart-button"
        <?php echo $disableAddToCart ? 'disabled' : '' ?>
        data-title="<?= $escaper->escapeHtml('Add to Cart'); ?>"
        data-title-disabled="<?= $escaper->escapeHtml($disallowMessage); ?>"
    >
        <?= $escaper->escapeHtml(__($disableAddToCart ? $disallowMessage : 'Add to Cart')); ?>
    </button>
</div>

<style>
    @media (max-width: 1023px) {
        iframe#hfc-frame.hfc-badge  {
            bottom: 65px;
        }
    }
</style>
