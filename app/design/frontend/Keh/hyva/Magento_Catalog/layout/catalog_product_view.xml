<?xml version="1.0"?>
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="hyva_modal" />

    <body>
        <attribute name="itemtype" value=""/>
        <attribute name="itemscope" value=""/>
        <referenceBlock remove="true" name="view.addto.compare" />
        <referenceBlock remove="true" name="product.info.addtocompare" />
        <referenceBlock remove="true" name="related"/>
        <referenceBlock remove="true" name="upsell"/>
        <referenceBlock remove="true" name="crosssell"/>

        <referenceBlock name="script-alpine-js">
            <block name="alpine-plugin-collapse" template="Magento_Theme::page/js/plugins/collapse.phtml"/>
        </referenceBlock>
        <referenceBlock name="product.media">
            <block class="Magento\Framework\View\Element\Template"
                   name="lcp-gallery-element"
                   template="Magento_Theme::lcp/lcp-element.phtml" />
        </referenceBlock>

        <referenceBlock name="page.main.title">
            <arguments>
                <argument name="css_class" xsi:type="string">page-title-wrapper</argument>
            </arguments>
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Magento_Catalog::product/title.phtml</argument>
            </action>
        </referenceBlock>

        <referenceBlock name="breadcrumbs">
            <arguments>
                <argument name="is_product_page" xsi:type="boolean">true</argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="product.info.wrapper">
            <container name="product.form.wrapper" htmlTag="div" htmlClass="product-form-wrapper" />
        </referenceContainer>

        <referenceContainer name="main.content" htmlClass="pt-1"/>

        <referenceBlock name="product.detail.page">
            <block class="Magento\Catalog\Block\Product\View"
                   name="product.links.container"
                   before="-"
                   group="product_detail_top"
                   template="Magento_Catalog::product/view/product-detail-top.phtml">
                <block class="Magento\Catalog\Block\Product\View" name="product.info.sku" template="Magento_Catalog::product/view/sku.phtml" after="product.info.type">
                    <arguments>
                        <argument name="at_call" xsi:type="string">getSku</argument>
                        <argument name="at_code" xsi:type="string">sku</argument>
                        <argument name="css_class" xsi:type="string">sku</argument>
                        <argument name="at_label" xsi:type="string">default</argument>
                        <argument name="add_attribute" xsi:type="string">itemprop="sku"</argument>
                    </arguments>
                </block>

                <block class="Magento\Catalog\Block\Product\View" name="product.info.lens_not_included" template="Magento_Catalog::product/view/lensNotIncluded.phtml" after="product.info.sku">
                    <arguments>
                        <argument name="body_only_product_view_model" xsi:type="object">Inchoo\Catalog\ViewModel\BodyOnlyProduct</argument>
                        <argument name="at_call" xsi:type="string">getKehProductType</argument>
                        <argument name="at_code" xsi:type="string">keh_product_type</argument>
                        <argument name="css_class" xsi:type="string">keh_product_type</argument>
                        <argument name="at_label" xsi:type="string">default</argument>
                    </arguments>
                </block>
            </block>
        </referenceBlock>

        <referenceBlock name="product.links.container">
            <container name="oos-wrapper" htmlTag="div" htmlClass="oos-wrapper">
                <block class="Magento\Catalog\Block\Product\View"
                    name="oos-container"
                    template="Magento_Catalog::product/out-of-stock/container.phtml">
                    <block class="Magento\Framework\View\Element\Template" name="oos.block.1" 
                        template="Magento_Catalog::product/out-of-stock/info.phtml" />
                    <block class="Magento\Cms\Block\Block" name="oos.block.2">
                        <arguments>
                            <argument name="block_id" xsi:type="string">oos-info-block</argument>
                        </arguments>
                    </block>
                </block>
            </container>
        </referenceBlock>
        <move element="productalert.stock" destination="oos.block.1" />

        <referenceBlock name="product.info.form">
            <arguments>
                <argument name="product_data_attributes" xsi:type="array">
                    <item name="sku" xsi:type="boolean">true</item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="product.info.form">
            <arguments>
                <argument name="product_data_attributes" xsi:type="array">
                    <item name="sku" xsi:type="boolean">true</item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="product.info.addto">
            <block class="Magento\Catalog\Block\Product\View"
                   name="product.addto.share"
                   template="Magento_Catalog::product/view/addto/share.phtml" />
        </referenceContainer>

        <move element="page.main.title" destination="product.links.container" before="-"/>

        <referenceBlock name="product.media" group="product_detail_top">
            <block class="Magento\Framework\View\Element\Template" name="product.info.gallery.disclaimer" template="Magento_Catalog::product/view/gallery-disclaimer.phtml"/>
        </referenceBlock>
        <referenceBlock name="product.info" group="product_detail_top"/>

        <move element="product.media" before="-" destination="product.detail.page" />
        <move element="product.info.addtocart" destination="product.info.form" />
        <move element="product.info.addtowishlist" destination="product.info.addtocart" />

        <referenceBlock name="product.info.addtocart" class="Magento\Catalog\Block\Product\View"/>

        <referenceBlock name="description">
            <arguments>
                <argument name="title" xsi:type="string" translate="true">Description</argument>
                <argument name="title_template" xsi:type="string">Magento_Catalog::product/view/sections/default-section-title.phtml</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="product.attributes">
            <arguments>
                <argument name="grouped_attributes_view_model" xsi:type="object">Inchoo\Catalog\ViewModel\GroupedAttributes</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="product.info.addtowishlist">
            <arguments>
                <argument name="hyva_js_block_dependencies" xsi:type="array">
                    <item name="category.products.list.js.wishlist" xsi:type="boolean">true</item>
                </argument>
            </arguments>
        </referenceBlock>

        <move element="description" destination="product-details-accordion-description" />
        <move element="product.attributes" destination="product-details-accordion-description" />

        <referenceContainer name="main.content">
            <block class="Magento\GroupedProduct\Block\Product\View\Type\Grouped" name="product.info.floating.buttons"  template="Magento_Catalog::product/view/floating-buttons.phtml" after="-"/>
        </referenceContainer>

        <referenceBlock name="product.info.floating.buttons">
            <arguments>
                <argument name="grades_view_model" xsi:type="object">Keh\Product\ViewModel\Grades</argument>
                <argument name="stock_availability_view_model" xsi:type="object">Keh\Inventory\ViewModel\StockAvailability</argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="main.content">
            <container name="wrapper-2" htmlTag="div" htmlClass="bg-neutral-100" htmlId="wrapper-2">
                <container name="container-2" htmlTag="div" htmlClass="columns py-8 max-lg:pb-0 gap-y-0">
                    <block class="Magento\Catalog\Block\Product\View" name="footnotes"
                        template="Magento_Catalog::product/view/footnotes.phtml">
                        <arguments>
                            <argument name="associated_products_view_model" xsi:type="object">Inchoo\ProductPageM1\ViewModel\GroupedAssociatedProducts</argument>
                        </arguments>
                        <block class="Magento\Cms\Block\Block" name="returns.pdp.messaging.block">
                            <arguments>
                                <argument name="block_id" xsi:type="string">returns_pdp_messaging</argument>
                            </arguments>
                        </block>
                        <block class="Magento\Cms\Block\Block" name="talk.with.expert">
                            <arguments>
                                <argument name="block_id" xsi:type="string">talk_with_expert</argument>
                            </arguments>
                        </block>
                    </block>

                    <block name="product-details-accordion" template="Magento_Theme::elements/accordion/accordion.phtml" after="footnotes">
                        <arguments>
                            <argument name="child_template" xsi:type="string">Magento_Catalog::product/view/details.phtml</argument>
                            <argument name="multiselectable" xsi:type="boolean">true</argument>
                            <argument name="divider" xsi:type="boolean">false</argument>
                            <argument name="classes" xsi:type="string">max-lg:-mx-4 lg:flex lg:flex-col lg:gap-y-4</argument>
                        </arguments>

                        <block name="product-details-accordion-accessories">
                            <arguments>
                                <argument name="title" xsi:type="string" translate="true">Complete Your Kit with Compatible Accessories</argument>
                                <argument name="open" xsi:type="boolean">true</argument>
                                <argument name="id" xsi:type="string">compatible-accessories</argument>
                                <argument name="content_class" xsi:type="string">bg-white</argument>
                            </arguments>
                            <container name="compatible-accessories-wrapper" htmlTag="div" htmlId="compatible-accessories-wrapper" label="Compatible Accessories" />
                        </block>
                        <block name="product-details-accordion-description">
                            <arguments>
                                <argument name="title" xsi:type="string" translate="true">Product Details</argument>
                                <argument name="open" xsi:type="boolean">false</argument>
                                <argument name="id" xsi:type="string">product-details</argument>
                            </arguments>
                        </block>
                        <block name="product-details-accordion-additional">
                            <arguments>
                                <argument name="title" xsi:type="string" translate="true">Additional Information</argument>
                                <argument name="open" xsi:type="boolean">false</argument>
                                <argument name="id" xsi:type="string">additional-information</argument>
                            </arguments>
                        </block>
                    </block>
                </container>
           </container>
        </referenceContainer>
    </body>
</page>
