<?php 
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var ViewModelRegistry $viewModels */
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
?>

<div x-data="initDropdown()"
     class="relative text-base leading-6"
     @click.outside="open = false"
     @keydown.escape="open = false">

    <select :name="name"
            class="hidden"
            :required="required"
            :disabled="disabled"
            x-model="selectedValue">
        <option value="">Select an option</option>
        <template x-for="option in options" :key="option.value">
            <option :value="option.value" x-text="option.label"></option>
        </template>
    </select>

    <button type="button"
            @click="toggle()"
            @keydown="handleKeydown($event)"
            :disabled="disabled"
            :class="{ 'bg-charcoal-50 border-charcoal-200 text-charcoal-400 cursor-not-allowed': disabled }"
            class="flex items-center gap-2 w-full py-2.5 px-3.5 border border-charcoal-800 rounded-sm
                placeholder-charcoal-500 hover:border-orange-300 active:border-orange-500
                focus:border-orange-400 focus:shadow-focusPrimary">
        <span x-text="selectedLabel || placeholder"></span>
        <?= $heroicons->dropdownHtml('', 20, 20, ["aria-hidden" => "true"]) ?>
    </button>

    <div x-show="open"
         x-cloak
         x-transition.origin.top.left
         class="absolute mt-1 z-10 w-full overflow-auto bg-white shadow-lg rounded-md"
         role="listbox">
        <template x-for="option in options" :key="option.value">
            <div @click="select(option)"
                 class="relative py-2.5 px-3.5 select-none cursor-pointer hover:bg-orange-50"
                 :class="{ 'bg-orange-500': selectedValue === option.value }">
                <span x-text="option.label"></span>
            </div>
        </template>
    </div>
</div>

<script>
    function initDropdown(config) {
        return {
            options: config.options || [],
            placeholder: config.placeholder || 'Select an option',
            required: config.required || false,
            selectedValue: config.selectedValue || '',
            selectedLabel: config.selectedLabel || '',
            open: config.open || false,
            disabled: config.disabled || false,

            init() {
                if (this.selectedValue) {
                    const option = this.options.find(opt => opt.value === this.selectedValue);
                    if (option) {
                        this.selectedLabel = option.label;
                    }
                }

                this.$watch('open', value => {
                    if (value) {
                        this.$nextTick(() => {
                            this.$el.querySelector('[role="listbox"]').focus();
                        });
                    }
                });
            },

            toggle() {
                if (!this.disabled) {
                    this.open = !this.open;
                }
            },

            select(option) {
                if (!this.disabled) {
                    this.selectedValue = option.value;
                    this.selectedLabel = option.label;
                    this.open = false;
                    this.$dispatch('dropdown-changed', {
                        value: option.value,
                        label: option.label,
                        name: this.name
                    });
                }
            },

            handleKeydown(event) {
                if (this.disabled) return;

                const options = this.options;
                const currentIndex = options.findIndex(opt => opt.value === this.selectedValue);

                switch (event.key) {
                    case 'ArrowDown':
                        event.preventDefault();
                        if (currentIndex < options.length - 1) {
                            this.select(options[currentIndex + 1]);
                        }
                        break;
                    case 'ArrowUp':
                        event.preventDefault();
                        if (currentIndex > 0) {
                            this.select(options[currentIndex - 1]);
                        }
                        break;
                    case 'Enter':
                    case 'Space':
                        event.preventDefault();
                        this.toggle();
                        break;
                }
            }
        }
    }
</script>
