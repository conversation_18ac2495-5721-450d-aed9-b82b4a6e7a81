<?php
declare(strict_types=1);

use Magento\Catalog\Block\Product\View\BaseImage;
use Magento\Catalog\Pricing\Price\TierPrice;
use Magento\Framework\Escaper;
use Magento\GroupedProduct\Block\Product\View\Type\Grouped;
use Keh\Inventory\ViewModel\StockAvailability;
use Inchoo\Catalog\ViewModel\GradeMessages;
use Inchoo\Catalog\ViewModel\ProductQty;
use Inchoo\Catalog\Api\GradeMessagesProviderInterface;
use Keh\Product\ViewModel\Grades;

/**
 * @var BaseImage $block
 * @var Grouped $block
 * @var Escaper $escaper
 * @var StockAvailability $stockAvailabilityViewModel
 * @var GradeMessages $gradeMessages
 * @var ProductQty $productQtyViewModel
 * @var Grades $gradesViewModel
 */

$stockAvailabilityViewModel = $block->getStockAvailabilityViewModel();
$gradeMessages = $block->getGradeMessagesViewModel();
$productQtyViewModel = $block->getProductQtyViewModel();
$gradesViewModel = $block->getGradesViewModel();

$block->setPreconfiguredValue();
$product = $block->getProduct();
/** @var \Magento\Catalog\Model\Product[] $associatedProducts */
$associatedProducts = $gradesViewModel->sortVariants($stockAvailabilityViewModel->getAvailableProducts($block->getAssociatedProducts()));
$hasAssociatedProducts = count($associatedProducts) > 0;
$brandAttributeTextValue = $product->getKehManufacturer() ? (string)$product->getAttributeText('keh_manufacturer') : '';
$scarcityThreshold = (int)$productQtyViewModel->getScarcityThreshold();
?>
<?php if ($hasAssociatedProducts): ?>
<?php $preselectedProduct = $gradesViewModel->getPreselectedVariant((int)$product->getId(), $associatedProducts); ?>
<script>
    function initGroupedOptions() {
        return {
            products: [],
            isValid: true,
            defaultQuantity: 0,
            selectedQuantity: 1,

            selectedProduct: {
                id: <?php echo $escaper->escapeJs($preselectedProduct->getId()); ?>,
            },

            validateForm() {
                const validateInputs = Array.from(
                    document.querySelectorAll('input[name^=super_group]')
                );

                // at least one of the inputs has to have a qty > 0
                this.isValid = validateInputs.filter(
                    input => input.value > 0
                ).length;

                // we set or unset validity for all fields at once
                // and empty string un-sets invalidity
                validateInputs.map(input =>
                    input.setCustomValidity(this.isValid ?
                        "" :
                        "<?= $escaper->escapeJs(__('Please specify the quantity of product(s).')) ?>")
                )

                if (!this.isValid) {
                    // this triggers an immediate display of the form errors
                    document.querySelector("#product_addtocart_form").reportValidity();
                    return false;
                }
                return true;
            },

            init() {
                document.querySelector("#product_addtocart_form")
                    .addEventListener("submit", (e) => {
                        // don't submit on errors
                        if (!this.validateForm()) {
                            e.preventDefault();
                        }
                    });
            },

            selectProduct(id) {
                const product = this.$root.querySelector('[data-id="'+id+'"]');
                const sku = product?.getAttribute('data-sku');
                const disAllowAddToCart = product?.getAttribute('data-disallow-add-to-cart');

                this.selectedProduct = {
                    id,
                    quantity: this.selectedQuantity
                };

                document.querySelectorAll('.grouped-item').forEach((item) => item.classList.remove('selected'));
                document.querySelector('[data-id="' + id + '"]').classList.add('selected');

                document.dispatchEvent(
                    new CustomEvent(
                        'grouped_product_selected',
                        {
                            detail: {
                                id: id,
                                sku: sku,
                                qty: this.selectedQuantity,
                                disAllowAddToCart: disAllowAddToCart === '1'
                            }
                        }
                    )
                );
            }
        }
    }
</script>
<div class="flex flex-col gap-2" x-data="initGroupedOptions()">
    <?php foreach ($associatedProducts as $item): ?>
        <?php
            $grade = $gradesViewModel->getGradeCodeFromLabel($item);
            $formatPrice = number_format((float)$item->getFinalPrice(), 2);
            ?>
        <div itemscope itemtype="https://schema.org/Product"
             class="<?= $escaper->escapeHtmlAttr($grade); ?> grouped-item flex flex-wrap border-neutral-D9D border w-full items-center justify-between cursor-pointer transition-colors trigger-a2c-button <?= $item->getId() == $preselectedProduct->getId() ? 'selected' : '' ?>"
            data-id="<?php echo $escaper->escapeHtmlAttr($item->getId()) ?>"
            data-sku="<?php echo $escaper->escapeHtmlAttr($item->getSku()) ?>"
            data-disallow-add-to-cart="<?php echo $escaper->escapeHtmlAttr($item->getDisallowAddToCart() ?: 0) ?>"
            @click.prevent="selectProduct(<?php echo $escaper->escapeHtmlAttr($item->getId()); ?>);"
        >
            <meta itemprop="name" content="<?= $escaper->escapeHtml($item->getName()) ?>"/>
            <meta itemprop="sku" content="<?= $escaper->escapeHtml($item->getSku()); ?>"/>
            <meta itemprop="brand" content="<?= $escaper->escapeHtml($brandAttributeTextValue); ?>"/>
            <meta itemprop="image" content="<?= $escaper->escapeUrl($block->getImage($item, 'product_base_image')->getImageUrl()); ?>"/>

            <div class="text-left flex gap-x-2 items-center justify-between w-full mb-0 min-h-[55px] lg:min-h-[65px] overflow-hidden">
                <div class="border-r border-neutral-D9D flex flex-col self-stretch flex-[0_0_65px]">
                    <?php $gradeLabel = (string)$item->getAttributeText(GradeMessagesProviderInterface::KEH_GRADE_ATTRIBUTE_CODE); ?>
                    <span class="h-1/2 text-center self-stretch flex items-center justify-center relative after:absolute after:bottom-0 after:left-0 after:block after:w-full after:border-b after:border-neutral-D9D font-apercu font-bold">
                        <?php echo $escaper->escapeHtml($gradesViewModel->getGradeCodeByName($gradeLabel));?>
                    </span>
                    <span class="h-1/2 uppercase text-xs3 px-1 translate-y-1px leading-tight text-center flex items-center justify-center"><?= $escaper->escapeHtml($gradeLabel); ?></span>
                </div>
                <div class="item-grade-info flex-1" itemprop="description">
                    <span class="text-sm xl:text-lg my-1 block"><?= $escaper->escapeHtml($item->getData('keh_exception_code')); ?></span>
                    <small class="grade-msg"><?= $gradeMessages->getGradeMessage($item); ?></small>
                </div>

                <?php if ($block->getCanShowProductPrice($product) && $block->getCanShowProductPrice($item)): ?>
                    <div class="flex flex-col justify-center items-end pr-3" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
                        <meta itemprop="price" content="<?= $escaper->escapeHtml(str_replace(',', '', $formatPrice)); ?>"/>
                        <meta itemprop="priceCurrency" content="USD"/>
                        <meta itemprop="availability" content="https://schema.org/InStock">
                        <meta itemprop="itemCondition" content="<?= '',($grade === 'new' ? 'NewCondition' : 'UsedCondition'); ?>" />
                        <meta itemprop="url" content="<?= $escaper->escapeUrl($product->getProductUrl()); ?>"/>
                        <?= $block->getProductPrice($item) ?>
                        <div class="flex gap-2 items-center item-promo-info">
                            <?php $availableQty = (int)$stockAvailabilityViewModel->getStockQty($item->getSku()); ?>
                            <?php if ($availableQty > 0 && $availableQty <= $scarcityThreshold) : ?>
                                <span class="text-blue-500"><?= $escaper->escapeHtml(__('Only %1 left!', $availableQty)); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($block->getCanShowProductPrice($product) && $block->getCanShowProductPrice($item)
                && trim($block->getProductPriceHtml($item, TierPrice::PRICE_CODE))): ?>
                <div class="flex pt-2 w-full items-center">
                    <?= $block->getProductPriceHtml($item, TierPrice::PRICE_CODE) ?>
                </div>
            <?php endif; ?>

        </div>
    <?php endforeach; ?>

    <?php if ($product->isSaleable()): ?>
        <div class="flex flex-col min-h-6">
            <?php foreach($associatedProducts as $item): ?>
                <?php $escapedHtmlId = $escaper->escapeHtmlAttr($item->getId()); ?>

            <div class="selected-product-wrapper warranty-wrapper"
             :class="selectedProduct && selectedProduct.id == '<?= $escapedHtmlId ?>' ? '' : 'hidden'" x-cloak>
                <div class="field required hidden">
                    <label class="label" for="super_group[<?= $escapedHtmlId ?>]">
                        <?= $escaper->escapeHtml(__('Quantity')) ?>
                    </label>
                    <div class="control">
                        <input type="number"
                            :value="selectedProduct && selectedProduct.id == '<?= $escapedHtmlId ?>' ? selectedQuantity : defaultQuantity"
                            id="super_group[<?= $escapedHtmlId ?>]"
                            name="super_group[<?= $escapedHtmlId ?>]"
                            value="1"
                            title="<?= $escaper->escapeHtmlAttr(__('Qty')) ?>"
                            readonly
                            class="form-input"
                            placeholder="<?= $escaper->escapeHtmlAttr(__('Qty')) ?>"
                            @input.debounce="validateForm()"
                        >
                    </div>
                    <?= $block->getChildHtml('product.info.stockstatus') ?>
                </div>
                <?php echo $block->getChildBlock('extend_warranty')
                    ->setData('product', $item)
                    ->toHtml();?>
            </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

<?php else: ?>
    <?= $block->getChildHtml('oos.block') ?>
<?php endif; ?>
</div>
