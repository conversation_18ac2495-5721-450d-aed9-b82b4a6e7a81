<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock remove="true" name="product.info.stockstatus" />

        <attribute name="class" value="page-product-grouped"/>

        <referenceContainer name="product.info.form.content">
            <container name="groupedContainer" htmlTag="div" htmlId="grouped-product" before="-">
                <block class="Magento\GroupedProduct\Block\Product\View\Type\Grouped"
                       name="product.info.grouped" before="product.info.addtocart"
                       template="Magento_GroupedProduct::product/view/type/grouped.phtml">
                </block>
            </container>
        </referenceContainer>

        <referenceBlock name="product.info.options.grouped">
            <arguments>
                <argument name="url_params_view_model" xsi:type="object">Inchoo\ProductPageM1\ViewModel\UrlParams</argument>
                <argument name="grade_messages_view_model" xsi:type="object">Inchoo\Catalog\ViewModel\GradeMessages</argument>
                <argument name="product_qty_view_model" xsi:type="object">Inchoo\Catalog\ViewModel\ProductQty</argument>
                <argument name="stock_availability_view_model" xsi:type="object">Keh\Inventory\ViewModel\StockAvailability</argument>
                <argument name="grades_view_model" xsi:type="object">Keh\Product\ViewModel\Grades</argument>
                <argument name="product_data_attributes" xsi:type="array">
                    <item name="sku" xsi:type="boolean">true</item>
                </argument>
            </arguments>

             <block class="Magento\Cms\Block\Block" name="oos.block">
                <arguments>
                    <argument name="block_id" xsi:type="string">oos-info-block</argument>
                </arguments>
            </block>
        </referenceBlock>

         <referenceBlock name="product.info">
            <block class="Magento\GroupedProduct\Block\Product\View\Type\Grouped"
                    name="price.and.affirm.block"
                    template="Magento_GroupedProduct::product/view/type/price-and-affirm.phtml">
                <arguments>
                    <argument name="affirm_config_view_model" xsi:type="object">Inchoo\ProductPageM1\ViewModel\AffirmConfig</argument>
                    <argument name="grades_view_model" xsi:type="object">Keh\Product\ViewModel\Grades</argument>
                    <argument name="stock_availability_view_model" xsi:type="object">Keh\Inventory\ViewModel\StockAvailability</argument>
                </arguments>
            </block>
         </referenceBlock>

        <referenceBlock name="product.info.addtocart">
            <arguments>
                <argument name="grades_view_model" xsi:type="object">Keh\Product\ViewModel\Grades</argument>
                <argument name="stock_availability_view_model" xsi:type="object">Keh\Inventory\ViewModel\StockAvailability</argument>
            </arguments>
        </referenceBlock>


        <referenceBlock name="product.info.floating.buttons">
            <arguments>
                <argument name="disable_add_to_cart_view_model" xsi:type="object">Inchoo\Catalog\ViewModel\DisableAddToCart</argument>
            </arguments>
        </referenceBlock>

        <move element="product.info.addtocart" destination="product.info" />
    </body>
</page>
