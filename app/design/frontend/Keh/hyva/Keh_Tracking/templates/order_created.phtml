<?php
declare(strict_types=1);

use Keh\Tracking\ViewModel\OrderDataProvider;
use Magento\Checkout\Block\Onepage\Success;
use Magento\Framework\Escaper;

/** @var Success $block */
/** @var Escaper $escaper */

/** @var OrderDataProvider $orderDataProvider */
$orderDataProvider = $block->getData('order_data_provider');

$orderData = $orderDataProvider->getOrderMarketingDataArray();
?>
<?php if (!empty($orderData)): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.dispatchEvent(
                new CustomEvent('keh.order.placed', {
                    detail: {
                        name: 'Order placed',
                        data: <?php echo $orderDataProvider->serialize($orderData);?>
                    }
                })
            );
        })
    </script>
<?php endif;?>
