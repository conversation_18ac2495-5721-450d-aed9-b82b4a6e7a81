define([
    'uiComponent',
    'ko'
], function (Component, ko) {
    'use strict';

    return Component.extend({
        showToGuest: !window.isCustomerLoggedIn,
        showToCustomer: window.isCustomerLoggedIn,

        // if user is logged in window.customerData is Object literal
        // if user is not logged in, window.customerData is empty Array
        isCustomerAlreadySubscribed: Object.getPrototypeOf(window.customerData) === Object.prototype ?
            window.customerData.extension_attributes.is_subscribed :
            false,

        subscriptionStatus: ko.observable(1),

        initialize: function () {
            this._super();

            return this;
        }
    });
});
