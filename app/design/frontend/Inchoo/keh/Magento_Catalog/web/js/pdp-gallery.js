define([
    'jquery',
    'js/vendor/slick.min',
    'jquery-ui-modules/widget'
], function ($) {
    'use strict';

    $.widget('inchoo.pdpGallery', {
        _init: function () {
            $(this.options.mainImgSelector).slick({
                slidesToShow: 1,
                slidesToScroll: 1,
                arrows: false,
                lazyLoad: 'ondemand',
                swipe: false,
                fade: true,
                asNavFor: this.options.thumbnailsSelector
            });

            if (this.options.thumbnailsSelector) {
                $(this.options.thumbnailsSelector).slick({
                    slidesToShow: 5,
                    focusOnSelect: true,
                    lazyLoad: 'ondemand',
                    asNavFor: this.options.mainImgSelector,
                    swipeToSlide: true,
                    centerPadding: 0,
                    centerMode: true,
                    slidesToScroll: 1,
                    infinite: true,
                    mobileFirst: true,
                    arrows: false,
                    responsive: [{
                        breakpoint: 567,
                        settings: {
                            slidesToShow: 5,
                            slidesToScroll: 1,
                            arrows: true
                        }
                    }]
                });
            }
        }
    });

    return $.inchoo.pdpGallery;
})
