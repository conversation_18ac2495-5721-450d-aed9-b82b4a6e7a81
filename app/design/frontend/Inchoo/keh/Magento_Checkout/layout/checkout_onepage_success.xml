<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="page.main.wrapper" htmlClass="page-main-wrapper padding t-1" />

        <referenceContainer name="order.success.additional.info">
            <block class="Magento\Checkout\Block\Onepage\Success" name="tracking.purchase.pixel" template="Magento_Checkout::tracking/purchase-pixel.phtml">
                <arguments>
                    <argument name="success_view_model" xsi:type="object">Inchoo\Checkout\ViewModel\Success</argument>
                </arguments>
            </block>
            <block class="Magento\Checkout\Block\Onepage\Success"
                   name="tracking.purchase.rt"
                   template="Magento_Checkout::tracking/purchase-rt.phtml" />
        </referenceContainer>
    </body>
</page>
