<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="checkout"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <meta name="description" content="Checkout page"/>

        <remove src="css/print.css"/>
        <remove src="jquery/uppy/dist/uppy-custom.css"/>
        <remove src="StripeIntegration_Payments::css/wallets.css"/>
        <remove src="Magestore_Customercredit::css/customercredit.css"/>
        <remove src="Magestore_Rewardpoints::css/rewardpoints.css"/>
        <remove src="Magestore_Rewardpoints::css/ion.rangeSlider.css"/>
        <remove src="Magestore_Rewardpoints::css/ion.rangeSlider.skinModern.css"/>
        <remove src="StripeIntegration_Payments::css/stripe_payments.css"/>
    </head>
    <body>
        <referenceContainer name="head.additional">
            <block class="Magento\Framework\View\Element\Template" name="critical-lcp" before="-" template="Magento_Checkout::lcp-critical.phtml"/>
        </referenceContainer>
        <referenceContainer name="header.navigation" remove="true" />
        <referenceContainer name="header-wrapper" remove="true" />
        <referenceContainer name="header-navigation" remove="true" />
        <referenceContainer name="top.banner" remove="true" />
        <referenceBlock name="catalog.compare.sidebar" remove="true"/>
        <referenceBlock name="catalog.compare.link" remove="true"/>
        <referenceBlock name="related.product.addto.compare" remove="true"/>
        <referenceBlock name="category.product.addto.compare" remove="true"/>
        <referenceBlock name="multiple-wishlist_sidebar" remove="true" />
        <referenceContainer name="top.search" remove="true" />
        <referenceContainer name="newsletter.container" remove="true" />
        <referenceBlock name="page.main.title" remove="true" />
        <referenceBlock name="footer-links" remove="true" />
        <referenceBlock name="cookie_notices" remove="true" />

        <referenceContainer name="header.container">
            <block name="checkout.bottom-bar" template="Magento_Checkout::header/bottom-bar.phtml" after="-"/>
        </referenceContainer>

        <referenceContainer name="header.panel.inner">
            <block name="checkout.contact" template="Magento_Checkout::header/contact.phtml" after="-"/>
        </referenceContainer>

        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="shipping-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="shippingAddress" xsi:type="array">
                                                    <item name="children" xsi:type="array">
                                                        <item name="customer-email" xsi:type="array">
                                                            <item name="tooltip" xsi:type="boolean">false</item>
                                                        </item>
                                                        <item name="shipping-address-fieldset" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="telephone" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Magento_Checkout/js/form/element/telephone</item>
                                                                    <item name="config" xsi:type="array">
                                                                        <item name="formId" xsi:type="string">co-shipping-form</item>
                                                                        <item name="LS_key" xsi:type="string">getShippingAddressFromData</item>
                                                                        <item name="tooltip" xsi:type="boolean">false</item>
                                                                        <item name="elementTmpl" xsi:type="string">Magento_Checkout/form/element/telephone</item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="main.content">
            <block name="checkout.steps" template="Magento_Checkout::checkout-steps.phtml" before="columns" />
        </referenceContainer>

        <referenceContainer name="div.sidebar.main">
            <block name="checkout.estimation" template="Magento_Checkout::checkout-estimation.phtml" />
        </referenceContainer>
        <referenceContainer name="footer-container" htmlClass="page-footer page-footer--checkout">
            <referenceContainer name="footer" htmlClass="footer content container">
                <referenceBlock name="footer-main" remove="true" />
                <block class="Magento\Cms\Block\Block" name="footer.norton.security" before="-">
                    <arguments>
                        <argument name="block_id" xsi:type="string">checkout-footer-norton</argument>
                    </arguments>
                </block>
            </referenceContainer>
        </referenceContainer>
    </body>
</page>
