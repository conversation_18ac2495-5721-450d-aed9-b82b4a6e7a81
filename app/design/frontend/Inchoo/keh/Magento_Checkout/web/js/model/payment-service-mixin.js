define([
    'mage/utils/wrapper',
    'Magento_Checkout/js/action/select-payment-method',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/payment/method-list',
], function (wrapper, selectPaymentMethod, quote, methodList) {

    return function (paymentService) {
        var isFreePaymentMethod = function (paymentMethod) {
                return paymentMethod.method === 'free';
            },

            /**
             * Grabs the grand total from quote
             * @return {Number}
             */
            getGrandTotal = function () {
                return quote.totals()['grand_total'];
            };

        /**
         * fix issue when CC is in vault and using Braintree
         * @type {Function|(function(): *)}
         */
        paymentService.setPaymentMethods = wrapper.wrapSuper(paymentService.setPaymentMethods, function (methods) {
            var freeMethod,
                filteredMethods,
                methodIsAvailable,
                methodNames;

            freeMethod = _.find(methods, isFreePaymentMethod);
            this.isFreeAvailable = !!freeMethod;

            if (freeMethod && getGrandTotal() <= 0) {
                methods.splice(0, methods.length, freeMethod);
                selectPaymentMethod(freeMethod);
            }

            filteredMethods = _.without(methods, freeMethod);

            if (filteredMethods.length === 1) {
                selectPaymentMethod(filteredMethods[0]);
            } else if (quote.paymentMethod()) {
                methodIsAvailable = methods.some(function (item) {
                    // inchoo - start
                    // Braintree CC card (when stored in Vault) has numerical suffix,
                    // for example braintree_cc_vault_124763
                    // which does not return true when explicitly comparing strings and throws an error
                    return (item.method === quote.paymentMethod().method) ||
                        (quote.paymentMethod().method.indexOf(item.method));
                    // inchoo - end
                });

                //Unset selected payment method if not available
                if (!methodIsAvailable) {
                    selectPaymentMethod(null);
                }
            }

            /**
             * Overwrite methods with existing methods to preserve ko array references.
             * This prevent ko from re-rendering those methods.
             */
            methodNames = _.pluck(methods, 'method');
            _.map(methodList(), function (existingMethod) {
                var existingMethodIndex = methodNames.indexOf(existingMethod.method);

                if (existingMethodIndex !== -1) {
                    methods[existingMethodIndex] = existingMethod;
                }
            });

            methodList(methods);
        });

        return paymentService
    }
});
