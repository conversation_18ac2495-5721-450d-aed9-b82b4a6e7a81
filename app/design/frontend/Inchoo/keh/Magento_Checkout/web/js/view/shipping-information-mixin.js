define([
	'jquery',
	'Magento_Checkout/js/model/step-navigator'
], function ($, stepNavigator) {
	'use strict';

	return function (target) {
		return target.extend({
			back: function () {
				this.closeOffcanvas();
				stepNavigator.navigateTo('shipping');
			},

			backToShippingMethod: function () {
				this.closeOffcanvas();
				stepNavigator.navigateTo('shipping', 'opc-shipping_method');
			},

			closeOffcanvas: function () {
				$('.block-summary-content .ui-close').trigger('click');
			}
		});
	};
});
