define([
	'jquery',
    'mage/utils/wrapper'
], function ($, wrapper) {
    'use strict';

    return function (stepNavigator) {
        stepNavigator.navigateTo = wrapper.wrapSuper(stepNavigator.navigateTo, function (code, scrollToElementId) {
            document.body.classList.remove("shipping-step");

            var sortedItems = stepNavigator.steps().sort(this.sortItems),
                bodyElem = $('html, body');

            scrollToElementId = scrollToElementId || null;

            if (!this.isProcessed(code)) {
                return;
            }

			sortedItems.forEach(function (element) {
                if (element.code == code) { //eslint-disable-line eqeqeq
                    element.isVisible(true);

					stepNavigator.setHash(code);

					const target = scrollToElementId || code;
					const targetOffset = $('#' + target).offset().top - getHeaderHeight();
					bodyElem.animate({
                        scrollTop: targetOffset + 'px'
                    }, 0);
                } else {
                    element.isVisible(false);
                }

            });
        });

		function getHeaderHeight() {
			return $('header').outerHeight();
		}

        return stepNavigator;
    };
});
