define([
    'jquery',
    'underscore',
    'ko',
    'uiComponent',
    'Magento_Checkout/js/model/step-navigator'
], function ($, _, ko, uiComponent, stepNavigator) {
    'use strict';

    var steps = stepNavigator.steps;

    return function (uiComponent) {
        return uiComponent.extend({
            steps: steps,

            initialize: function () {
                this._super();

                if (window.location.hash.substring(1) === 'shipping') {
                    document.body.scrollTop = document.documentElement.scrollTop = 0;
                }

                return this;
            }
        })
    }
});
