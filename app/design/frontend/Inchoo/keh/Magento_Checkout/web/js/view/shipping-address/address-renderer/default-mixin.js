define([
    'jquery',
    "Inchoo_FedExHoldAtLocation/js/fedex-hold-at-location"
], function($, fedexHoldAtLocation) {
    'use strict';

    return function(Component) {
        return Component.extend({
            selectAddress: function () {
                this._super();

                // if customer selects an existing shipping address, uncheck FedEx checkbox
                if (this.address().getType() !== 'new-customer-address' && 
                    $('#zip-toggle').prop('checked') === true) {
                    fedexHoldAtLocation().onShipHereButtonClick();
                }
            },
        });
    }
});
