define([
    'jquery'
], function ($) {
   'use strict';

   return {
       showOverlay: function (e) {
           if ($(e).length) {
               $(e).show()
           } else {
               this.checkOverlay(e, true);
           }

       }, hideOverlay: function (e) {
           if ($(e).length) {
               $(e).hide()
           } else {
               this.checkOverlay(e, false);
           }
       },

       checkOverlay: function (e, show) {
           var overlayInterval = setInterval(function () {
               if ($(e).length) {
                   clearInterval(overlayInterval)

                   if (show) {
                       $(e).show()
                   } else {
                       $(e).hide()
                   }
               }
           }, 50)
       }
   }
});
