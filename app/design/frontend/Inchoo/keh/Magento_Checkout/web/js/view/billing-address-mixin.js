define([
    'jquery',
    'ko',
    'Magento_Customer/js/model/customer',
    'Magento_Checkout/js/action/create-billing-address',
    'Magento_Checkout/js/action/select-billing-address',
    'Magento_Checkout/js/checkout-data'
], function (
    $,
    ko,
    customer,
    createBillingAddress,
    selectBillingAddress,
    checkoutData
) {
    'use strict';

    var addressUpdated = false;

    return function (Component) {
        return Component.extend({
            /**
             *  Update address action.
             * KEH, manage a case with a missed "address" param in an API call to Magento.
             * This happens when a user submits amn empty or not validated form.
             * Added only if() to manage that edge case, see KEH tag.
             */
            updateAddress: function () {
                var addressData, newBillingAddress;

                addressUpdated = true;

                if (this.selectedAddress() && !this.isAddressFormVisible()) {
                    selectBillingAddress(this.selectedAddress());
                    checkoutData.setSelectedBillingAddress(this.selectedAddress().getKey());
                } else {
                    this.source.set('params.invalid', false);
                    this.source.trigger(this.dataScopePrefix + '.data.validate');

                    if (this.source.get(this.dataScopePrefix + '.custom_attributes')) {
                        this.source.trigger(this.dataScopePrefix + '.custom_attributes.data.validate');
                    }

                    if (!this.source.get('params.invalid')) {
                        addressData = this.source.get(this.dataScopePrefix);

                        if (customer.isLoggedIn() && !this.customerHasAddresses) { //eslint-disable-line max-depth
                            this.saveInAddressBook(1);
                        }
                        addressData['save_in_address_book'] = this.saveInAddressBook() ? 1 : 0;
                        newBillingAddress = createBillingAddress(addressData);
                        // New address must be selected as a billing address
                        selectBillingAddress(newBillingAddress);
                        checkoutData.setSelectedBillingAddress(newBillingAddress.getKey());
                        checkoutData.setNewCustomerBillingAddress(addressData);
                    }

                    //KEH change
                    if (this.source.get('params.invalid') && this.isAddressFormVisible()) {
                        console.info("The billing form is not validated, please fill up all fields.");
                        return;
                    }
                }
                this.updateAddresses(true);
            },
        });
    };
});
