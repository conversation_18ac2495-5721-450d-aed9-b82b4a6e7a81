define([
    'uiComponent',
    'jquery',
    'Magento_Customer/js/model/customer',
    'Magento_Checkout/js/view/form/element/email',
    'Magento_Checkout/js/model/quote',
    'ko',
    'Magento_Checkout/js/view/overlay-blocker',
    'Magento_Customer/js/model/address-list'
], function (Component, $, customer, email, quote, ko, blocker, addressList) {
    'use strict';

    return function (Component) {
        return Component.extend({
            emailField: '#customer-email',
            emailForm: '.form-login',
            emailRegex: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            shippingAddressForm: '#co-shipping-form',
            shippingAddressOverlay: '#shipping-address-overlay',
            shippingMethodOverlay: '#shipping-method-overlay',

            showShippingMethodOverlay: ko.observable(null),
            isEmailFieldValid: ko.observable(null),
            isShippingAddressValid: ko.observable(false),
            isFedexSelected: ko.observable(false),
            isFedexLocationSelected: ko.observable(false),
            showEmailMessage: ko.observable(true),
            showFedExMessage: ko.observable(true),

            names: ['firstname', 'lastname', 'street[0]', 'country_id', 'region_id', 'city', 'postcode', 'telephone'],
            selectors: [],
            isShippingAddressFormValid: null,
            isCustomer: customer.isLoggedIn(),
            addressListVisible: addressList().length | 0,

            initialize: function () {
                this._super();

                /* manage overlay of shipping address */
                this.isEmailFieldValid.subscribe(function (value) {
                    if (value) {
                        blocker.hideOverlay(this.shippingAddressOverlay);
                    } else {
                        blocker.showOverlay(this.shippingAddressOverlay);
                    }
                }.bind(this));

                /* manage overlay of shipping methods */
                ko.computed(function () {
                    if (this.isEmailFieldValid() &&
                        this.isShippingAddressValid() &&
                        (!this.isFedexSelected() || (this.isFedexSelected() && this.isFedexLocationSelected())
                        )) {
                        blocker.hideOverlay(this.shippingMethodOverlay);
                    } else {
                        blocker.showOverlay(this.shippingMethodOverlay);
                    }

                }.bind(this));


                if (customer.isLoggedIn()) {
                    this.isEmailFieldValid(true);

                    if (Object.keys(customer.customerData.addresses).length) {
                        this.isShippingAddressValid(this.validateSavedShippingAddress());
                    } else {
                        this.observeShippingForm();
                    }
                } else {
                    this.observeEmailField();

                    this.observeShippingForm();
                }

                this.observeFedexEvents();

                return this;
            },

            observeEmailField: function () {
                if (!$(this.emailForm).length) {
                    /*observe when email field appears on page*/
                    const emailInterval = setInterval($.proxy(function () {
                        if ($(this.emailForm).length) {
                            clearInterval(emailInterval);
                            this.validateEmailField();
                        }
                    }, this), 50);
                }
            },

            validateEmailField: function () {
                /*on page load*/
                if ($(this.emailField).val()) {
                    this.testRegexOfEmail();
                }

                /* detect chrome autofill */
                $(this.emailField).on('webkitAnimationStart onanimationstart msAnimationStart animationstart',  $.proxy(function(e) {
                    if (e.target.validity.valid) {
                        this.testRegexOfEmail();
                    }
                }, this));

                /*on blur event*/
                $(this.emailField).on('blur', $.proxy(function() {
                    this.testRegexOfEmail();
                }, this));
            },

            testRegexOfEmail: function () {
                if (this.emailRegex.test($(this.emailField).val())) {
                    this.isEmailFieldValid(true);
                } else {
                    this.isEmailFieldValid(false);
                    this.showEmailMessage(true);
                    this.showFedExMessage(false);
                }
            },

            /**
             * @description used only for guest users
             */
            observeShippingForm: function () {
                /*build field selectors in form that need to be observed*/
                this.selectors = this.names
                    .map(name => this.shippingAddressForm + ' [name="'+name+'"]')
                    .join(',');

                /*observe when shipping form fields appear on page*/
                const shippingAddressInterval = setInterval($.proxy(function () {
                    if ($(this.selectors).length === this.names.length) {

                        clearInterval(shippingAddressInterval);

                        this.fieldsValid = Array.from({length: this.names.length}, () => -1);

                        this.isShippingAddressValidOnPageLoad();
                        this.isShippingAddressValidOnFieldBlur();
                    }
                }, this), 50)
            },

            isShippingAddressValidOnPageLoad: function () {
                var value = null;

                $(this.selectors).each($.proxy(function (i, selector) {
                    if ($(selector).is('select')) {
                        value = $(selector).find(":selected").val();
                    } else {
                        value = $(selector).val()
                    }

                    if (!!value) {
                        this.fieldsValid[i] = 1
                    } else {
                        this.fieldsValid[i] = 0
                    }
                }, this));

                this.isShippingFormValid(this.fieldsValid)
            },

            isShippingAddressValidOnFieldBlur: function () {
                var self = this;

                $(this.selectors).each($.proxy(function (i, selector) {
                    $(selector).on('change', function () {
                        var name = $(this).attr('name');

                        var fieldIsValid = Number(!!$(this).val()) && Number($(this).siblings('.field-error').length === 0);
                        var index = self.names.findIndex(el => el === name);

                        self.fieldsValid[index] = fieldIsValid;

                        self.isShippingFormValid(self.fieldsValid);
                    });
                }));
            },

            isShippingFormValid: function (values) {
                var uniqueFieldValues = values.filter(function (x, i, a) {
                    return a.indexOf(x) === i;
                });

                if (uniqueFieldValues.length === 1 && uniqueFieldValues[0] === 1) {
                    console.log('shipping form is valid');
                    this.isShippingAddressValid(true);
                    this.showEmailMessage(false);
                    this.showFedExMessage(false);
                }

                if (uniqueFieldValues.length > 1) {
                    console.log('shipping form is invalid');
                    this.isShippingAddressValid(false)
                    this.showEmailMessage(true);
                    this.showFedExMessage(false);
                }
            },

            observeFedexEvents: function () {
                var self = this;

                if (!$('#zip-toggle').length) {

                    /*observe when FedEx checkbox appears on page*/
                    const fedexInterval = setInterval($.proxy(function () {
                        if ($('#zip-toggle').length) {
                            clearInterval(fedexInterval);

                            /*toggle shipping methods overlay if user checks/unchecks usage of FedEx*/
                            $('#zip-toggle').on('change', function () {
                                self.isFedexSelected($(this).prop('checked'))

                                if ($(this).prop('checked')) {
                                    self.showEmailMessage(false);
                                    self.showFedExMessage(true);
                                } else {
                                    self.showEmailMessage(false);
                                    self.showFedExMessage(false);
                                }
                            });

                            $(document).on('click', 'input[name="fedex-store"]', function () {
                                self.isFedexLocationSelected(true);
                            })
                        }
                    }, this), 50)
                }
            },

            /**
             * @description for customers, validate if shipping details are OK
             * */
            validateSavedShippingAddress: function () {
                var address = quote.shippingAddress();

                if (address.firstname &&
                    address.lastname &&
                    address.street[0] &&
                    address.regionId &&
                    address.city &&
                    address.postcode &&
                    address.telephone) {
                    return true
                }

                this.showEmailMessage(true);
                this.showFedExMessage(false);
                return false
            },

            setShippingInformation: function () {
                this._super();

                document.body.classList.remove("shipping-step", "payment-step");
            },
        })
    }
});
