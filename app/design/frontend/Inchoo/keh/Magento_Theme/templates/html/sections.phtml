<?php
// @codingStandardsIgnoreFile
?>

<?php
$group = $block->getGroupName();
$groupCss = $block->getGroupCss();
$groupBehavior = $block->getGroupBehaviour() ? $block->getGroupBehaviour() : '{"tabs":{"openedState":"active"}}';
?>
<?php if ($detailedInfoGroup = $block->getGroupChildNames($group, 'getChildHtml')):?>
    <div class="sections <?= /* @escapeNotVerified */ $groupCss ?>">
        <button class="action ui-canvas-close main-nav-canvas-close"><span><?= "Close" ?></span><span class="icon-close"></span></button>
        <?php $layout = $block->getLayout(); ?>
        <div class="section-items <?= /* @escapeNotVerified */ $groupCss ?>-items" data-mage-init='<?= /* @escapeNotVerified */ $groupBehavior ?>'>
            <?php foreach ($detailedInfoGroup as $name):?>
                <?php
                    $html = $layout->renderElement($name);
                    if (!trim($html) && ($block->getUseForce() != true)) {
                        continue;
                    }
                    $alias = $layout->getElementAlias($name);
                    $label = $block->getChildData($alias, 'title');
                ?>
                <div class="section-item-title <?= /* @escapeNotVerified */ $groupCss ?>-item-title" data-role="collapsible">
                    <a class="<?= /* @escapeNotVerified */ $groupCss ?>-item-switch" data-toggle="switch" href="#<?= /* @escapeNotVerified */ $alias ?>"><?= /* @escapeNotVerified */ $label ?></a>
                </div>
                <div class="section-item-content <?= /* @escapeNotVerified */ $groupCss ?>-item-content" id="<?= /* @escapeNotVerified */ $alias ?>" data-role="content"><?= /* @escapeNotVerified */ $html ?></div>
            <?php endforeach;?>
        </div>
    </div>
    <div class="overlay off-canvas-overlay"></div>
<?php endif; ?>
