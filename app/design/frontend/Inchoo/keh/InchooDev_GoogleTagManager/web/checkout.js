define(
    [
        'underscore',
        'Magento_Customer/js/customer-data',
        'jquery',
        'Magento_Checkout/js/model/step-navigator',
        'Magento_Checkout/js/model/quote',
    ],
    function (_, customerData, $, navigator, quote) {
        'use strict';

        return function (enhancedData) {

            /** Push Enchanced Ecommerce Checkout Events */
            // Trigger on refresh or first load
            triggerStep(1, getCartProducts());

            // Trigger On shippingMethod update
            quote.shippingMethod.subscribe(function (shipping) {
                if (typeof shipping !== 'undefined' && shipping !== null) {
                    if (typeof shipping.carrier_code !== 'undefined') {
                        let option = shipping.carrier_code;
                        if (null !== option) {
                            sendOptionData(1, option);
                        }
                    }
                }
            }, this);

            let triggeredPaymentStep = false;
            $(document).ajaxComplete(function () {
                if (window.location.href.indexOf('#payment') !== -1 && !triggeredPaymentStep) {
                    triggerStep(2, getCartProducts());
                    triggeredPaymentStep = true;
                }
            });

            // Trigger On paymentMethod update
            quote.paymentMethod.subscribe(function (payment) {
                if (!payment) {
                    return
                }

                let option = payment.method;
                if (null !== option) {
                    sendOptionData(2, option);
                }
            }, this);

            /**
             * Send checkout option data when changing step
             * @param step
             * @param option
             */
            function sendOptionData(step, option)
            {
                let data = {
                    'event': 'checkoutOption',
                    'ecommerce': {
                        'checkout_option': {
                            'actionField': {'step': step, 'option': option},
                        }
                    },
                };
                dataLayer.push(data);
            }

            function getCartProducts()
            {
                let cartItems = enhancedData.cartItemsCollection;

                if (!_.isEmpty(cartItems)) {
                    let products = [];
                    for (let i in cartItems) {
                        let itemData = cartItems[i];
                        products.push(itemData);
                    }

                    return products;
                }
            }

            function triggerStep(step, products)
            {
                if (products !== undefined && products.length != 0) {
                    let checkoutData = {
                        'event': 'checkout',
                        'ecommerce': {
                            'checkout': {
                                'actionField': {'step': step},
                                'products': products
                            }
                        }
                    };

                    dataLayer.push(checkoutData);
                }
            }
        };
    }
);
