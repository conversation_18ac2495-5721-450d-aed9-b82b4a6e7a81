define([
    'uiComponent',
    'Magento_Checkout/js/model/shipping-service',
    'ko',
    'jquery'
], function (Component,shippingService,ko, $) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'InchooDev_LocalStorePickup/shipping/shipping-list'
        },
        myRates: ko.observableArray([]),
        isVisible:ko.observable(false),

        initialize: function () {

            this._super();
            var self = this;
            shippingService.getShippingRates().subscribe(function (rates) {
                var checker = false;
                self.myRates([]);
                _.each(rates, function (rate) {
                    var pos = self.myRates.map(function (e) {
                        return e.carrier_code; }).indexOf(rate['carrier_code']);
                    if (rate['carrier_code'] === 'localstorepickup') {
                        self.myRates.push(rate);
                        checker = true;
                        self.isVisible(!$('#zip-toggle').is(':checked'));
                    }
                });
                if (checker === false) {
                    self.isVisible(false);
                }
            });

            $(document).on('change', '#zip-toggle',  function(event) {
                self.isVisible(!event.target.checked);
            });
        },
    });
});
