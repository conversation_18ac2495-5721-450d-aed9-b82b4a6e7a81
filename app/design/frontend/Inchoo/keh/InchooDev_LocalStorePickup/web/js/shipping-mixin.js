define([
    'Magento_Checkout/js/model/shipping-service',
    'ko',
], function (
    shippingService,
    ko
) {
    'use strict';

    return function (target) {
        return target.extend(
            {
                rates: ko.observableArray([]),

                initialize: function () {

                    this._super();
                    var self = this;
                   shippingService.getShippingRates().subscribe(function (rates) {
                       self.rates([]);
                       _.each(rates, function (rate) {
                           var pos = self.rates.map(function (e) {
            return e.carrier_code; }).indexOf(rate['carrier_code']);
                               if (rate['carrier_code'] !== 'localstorepickup') {
                                   self.rates.push(rate);
                               }
                       });
                    });
                },

            }
        );
    };
});
