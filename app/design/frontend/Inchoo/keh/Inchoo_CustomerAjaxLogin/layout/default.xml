<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer-login">
            <container name="customer-account-list" htmlTag="ul"
                       htmlClass="account-list tooltip-list padding y-half">
                <block class="Magento\Framework\View\Element\Html\Link" name="customer-account-logout" after="-">
                    <arguments>
                        <argument name="label" xsi:type="string" translate="false">Logout</argument>
                        <argument name="path" xsi:type="string" translate="false">customer/account/logout</argument>
                    </arguments>
                </block>
            </container>
            <container name="turnstile.header">
                <!-- Customer Login -->
                <block name="turnstile_customer_login_header"
                       after="-"
                       template="InchooDev_CloudflareTurnstile::turnstile.phtml"
                       ifconfig="cloudflare_turnstile/frontend/enabled_for/customer_login">
                    <arguments>
                        <argument name="for_type" xsi:type="const">InchooDev\CloudflareTurnstile\Model\Config::TYPE_CUSTOMER_LOGIN</argument>
                        <argument name="view_model" xsi:type="object">InchooDev\CloudflareTurnstile\ViewModel\Turnstile</argument>
                    </arguments>
                </block>
            </container>
        </referenceBlock>

        <move element="my-account-link" destination="customer-account-list" before="-"/>
        <move element="multiple-wish-list-link" destination="customer-account-list" after="my-account-link"/>
        <move element="customer-login" destination="top.links" />

        <referenceBlock name="invitation_link_top" remove="true"/>
        <referenceBlock name="register-link" remove="true"/>
        <referenceBlock name="authorization-link" remove="true"/>
    </body>
</page>
