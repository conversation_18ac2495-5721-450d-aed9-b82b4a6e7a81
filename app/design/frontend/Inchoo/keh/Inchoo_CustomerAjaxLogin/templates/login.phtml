<?php

// @codingStandardsIgnoreFile

/** @var \Magento\Customer\Block\Form\Login $block */
/** @var \Magento\Framework\Escaper $escaper */
?>

<li class="customer-wrapper" data-bind="scope: 'customer'">
    <!-- ko if: customer().fullname  -->
    <?= $block->getChildHtml('customer-account-list'); ?>
    <!-- /ko -->
    <!-- ko ifnot: customer().fullname  -->
    <div data-block="mini_login" class="block-customer-login clear-margin">
        <div class="block-content padding xy-half" aria-labelledby="block-customer-login-heading">
            <form class="form form-login"
                  method="post"
                  id="header-login-form"
                  action="<?= $escaper->escapeUrl($block->getPostActionUrl()) ?>"
                  data-mage-init='{"validation":{}}'>
                <?= $block->getBlockHtml('formkey') ?>
                <fieldset class="fieldset login" data-hasrequired="<?= $escaper->escapeHtml(__('* Required Fields')) ?>">
                    <div class="field email required">
                        <label class="label no-display" for="email2">
                            <span><?= $escaper->escapeHtml(__('Email')) ?></span>
                        </label>
                        <div class="control">
                            <input name="login[username]"
                                   value="<?= $escaper->escapeHtmlAttr($block->getUsername()) ?>" <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                                   id="email2" type="email" class="input-text"
                                   title="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                                   placeholder="<?= $escaper->escapeHtmlAttr(__('Email')) ?>"
                                   data-mage-init='{"mage/trim-input":{}}'
                                   data-validate="{required:true, 'validate-email':true}">
                        </div>
                    </div>
                    <div class="field password required">
                        <label for="password2" class="label no-display">
                            <span><?= $escaper->escapeHtml(__('Password')) ?></span>
                        </label>
                        <div class="control">
                            <input name="login[password]"
                                   type="password" <?php if ($block->isAutocompleteDisabled()): ?> autocomplete="off"<?php endif; ?>
                                   class="input-text" id="password2" title="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                                   placeholder="<?= $escaper->escapeHtmlAttr(__('Password')) ?>"
                                   data-validate="{required:true}">
                        </div>
                    </div>
                    <?= $block->getChildHtml('form_additional_info') ?>
                    <div class="margin b-half">
                        <?= $block->getChildHtml('turnstile.header') ?>
                    </div>
                    <div class="actions-toolbar">
                        <div class="secondary margin b-half">
                            <a class="action remind cta text-capital font-weight-normal"
                               href="<?= $escaper->escapeUrl($block->getForgotPasswordUrl()) ?>">
                                <span><?= $escaper->escapeHtml(__('Forgot Your Password?')) ?></span>
                            </a>
                        </div>
                        <div class="primary">
                            <button type="submit" class="action primary full-width action-login" name="send" id="send2">
                                <span><?= $escaper->escapeHtml(__('Login with your email')) ?></span>
                            </button>
                        </div>
                    </div>

                </fieldset>
            </form>
            <hr class="hr-divider hr-divider--1" data-content="OR">
            <?= $block->getChildHtml('social-login') ?>
        </div>
        <div class="text-center header-create-account text-align center padding y-1">
            <p class="text-uppercase margin b-half"><?= $escaper->escapeHtml(__('New here? Don’t have an account?')) ?></p>
            <a title="Create an Account" class="action secondary fluid"
               href="<?= /* @escapeNotVerified */ $escaper->escapeUrl($block->getUrl('customer/account/create')) ?>">
                <span><?= $escaper->escapeHtml(__('Create an Account')) ?></span>
            </a>
        </div>
    </div>
    <!-- /ko -->
</li>


