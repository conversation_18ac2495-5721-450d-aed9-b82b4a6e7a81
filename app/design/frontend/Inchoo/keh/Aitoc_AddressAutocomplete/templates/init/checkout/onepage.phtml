<?php /** @codingStandardsIgnoreFile */ ?>
<?php /* @var $block \Aitoc\AddressAutocomplete\Block\Init */?>
<?php if ($block->canShow()): ?>
    <script type="text/x-magento-init">
    {
      "*": {
        "aitoc-autocomplete": {
          "apiKey": "<?php echo $block->getConfig()->getGooglePlacesApiKey(); ?>",
          "canUseBrowserGeolocation": <?php var_export($block->getConfig()->isGoogleUseBrowserGeo()); ?>,
          "locale": "<?php echo $block->getLocaleCode(); ?>",
          "hidePoweredByGoogleLabel": <?php var_export($block->getConfig()->isGoogleHideLogo()); ?>,
          "useCountryRestriction": <?php var_export($block->getConfig()->isGoogleUseCountryRestriction()); ?>,
          "regionConfig": <?php echo $block->getRegionConfig(); ?>,
          "components": {
            "region": "<?php echo $block->getConfig()->getAddressComponentRegion(); ?>",
            "city": "<?php echo $block->getConfig()->getAddressComponentCity(); ?>",
            "postcode": "<?php echo $block->getConfig()->getAddressComponentPostcode(); ?>",
            "street1": {
              "name": "<?php echo $block->getConfig()->getAddressComponentStreetOne(); ?>",
              "processor": "<?php echo ($block->getConfig()->isAddressComponentStreetCombined()) ? '_prepareCombinedStreet1' : ''; ?>"
            },
            "street2": {
              "name": "<?php echo $block->getConfig()->getAddressComponentStreetTwo(); ?>",
              "processor": "<?php echo ($block->getConfig()->isAddressComponentStreetCombined()) ? '_prepareCombinedStreet2' : ''; ?>"
            }
          }
        }
      }
    }
    </script>
<?php endif; ?>
