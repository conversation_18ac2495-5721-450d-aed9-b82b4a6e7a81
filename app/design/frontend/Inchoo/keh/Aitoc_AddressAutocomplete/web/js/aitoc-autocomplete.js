define([
    'jquery',
    'Aitoc_AddressAutocomplete/js/google-places'
], function(
    $
) {
    'use strict';

    return function (config) {
        var shipping = jQuery.extend({}, Aitoc.AddressAutocomplete.GooglePlaces, {
            /* customize method - fix loading of lib file by setting callback param */
            _buildGooglePlacesSrc: function () {
                var src = '//maps.googleapis.com/maps/api/js?';
                var query = jQuery.param(
                    {
                        'v': '3.36',
                        'key': this.getApiKey(),
                        'libraries': 'places',
                        'language': this.config.locale ? this.config.locale : '',
                        'callback': 'Function.prototype',
                        'loading': 'async'
                    }
                );
                return src + query;
            },
        });
        shipping.initialize({
            'readySelector': '#co-shipping-form',
            'apiKey': config.apiKey,
            'canUseBrowserGeolocation': config.canUseBrowserGeolocation,
            'locale': config.locale,
            'hidePoweredByGoogleLabel': config.hidePoweredByGoogleLabel,
            'useCountryRestriction': config.useCountryRestriction,
            'countryInput': '#co-shipping-form  [name="country_id"]',
            'regionConfig': config.regionConfig,
            'triggerAjaxList': [],
            'locationInput': '#co-shipping-form [name="street[0]"]',
            'addressForm': {
                'country': {
                    'inputId': '#co-shipping-form  [name="country_id"]',
                        'component': 'country',
                        'name_type': 'short_name'
                },
                'region_id': {
                    'inputId': '#co-shipping-form [name="region_id"]',
                        'component': 'administrative_area_level_1',
                        'name_type': 'short_name',
                        'processorName': '_prepareRegionId'
                },
                'region': {
                    'inputId': '#co-shipping-form [name="region"]',
                        'component': 'administrative_area_level_1',
                        'name_type': config.components.region
                },
                'city': {
                    'inputId': '#co-shipping-form [name="city"]',
                        'component': 'locality',
                        'name_type': config.components.city
                },
                'postcode': {
                    'inputId': '#co-shipping-form [name="postcode"]',
                        'component': 'postal_code',
                        'name_type': config.components.postcode
                },
                'street1': {
                    'inputId': '#co-shipping-form [name="street[0]"]',
                        'component': 'route',
                        'name_type': config.components.street1.name,
                        'processorName': config.components.street1.processor
                },
                'street2': {
                    'inputId': '#co-shipping-form [name="street[1]"]',
                        'component': 'street_number',
                        'name_type': config.components.street2.name,
                        'processorName': config.components.street2.processor
                }
            }
        });

        var _fieldId = '.billing-address-form #billing-new-address-form';

        if (jQuery(_fieldId).length == 0) {
            _fieldId = '.billing-address-form .fieldset.address';
        }

        function initLoadAutocompleteForPayment() {
            if (!jQuery(_fieldId).length) {
                setTimeout(initLoadAutocompleteForPayment, 500);
            } else {
                loadAutocompleteForPayment();
            }
        };

        function loadAutocompleteForPayment() {
            //create for each payment method new billing address
            jQuery(_fieldId).each(function(){
                var street1Id = jQuery(this).find('[name="street[0]"]').attr('id');
                var street2Id = jQuery(this).find('[name="street[1]"]').attr('id');
                var countryId = jQuery(this).find('[name="country_id"]').attr('id');
                var regionId = jQuery(this).find('[name="region_id"]').attr('id');
                var region = jQuery(this).find('[name="region"]').attr('id');
                var city = jQuery(this).find('[name="city"]').attr('id');
                var postcode = jQuery(this).find('[name="postcode"]').attr('id');
                var billing = jQuery.extend({}, Aitoc.AddressAutocomplete.GooglePlaces);
                billing.initialize({
                    'readySelector': '.billing-address-form #billing-new-address-form',
                    'apiKey': config.apiKey,
                    'canUseBrowserGeolocation': config.canUseBrowserGeolocation,
                    'locale': config.locale,
                    'hidePoweredByGoogleLabel': config.hidePoweredByGoogleLabel,
                    'useCountryRestriction': config.useCountryRestriction,
                    'countryInput': '#' + countryId,
                    'regionConfig': config.regionConfig,
                    'triggerAjaxList': [],
                    'locationInput': '#'+street1Id,
                    'addressForm': {
                        'country': {
                            'inputId': '#'+countryId,
                                'component': 'country',
                                'name_type': 'short_name'
                        },
                        'region_id': {
                            'inputId': '#'+regionId,
                                'component': 'administrative_area_level_1',
                                'name_type': 'short_name',
                                'processorName': '_prepareRegionId'
                        },
                        'region': {
                            'inputId': '#'+region,
                                'component': 'administrative_area_level_1',
                                'name_type': config.components.region
                        },
                        'city': {
                            'inputId': '#'+city,
                                'component': 'locality',
                                'name_type': config.components.city
                        },
                        'postcode': {
                            'inputId': '#'+postcode,
                                'component': 'postal_code',
                                'name_type': config.components.postcode
                        },
                        'street1': {
                            'inputId': '#'+street1Id,
                                'component': 'route',
                                'name_type': config.components.street1.name,
                                'processorName': config.components.street1.processor
                        },
                        'street2': {
                            'inputId': '#'+street2Id,
                                'component': 'street_number',
                                'name_type': config.components.street2.name,
                                'processorName': config.components.street2.processor
                        }
                    }
                });
            });
        };

        initLoadAutocompleteForPayment();
    }
});
