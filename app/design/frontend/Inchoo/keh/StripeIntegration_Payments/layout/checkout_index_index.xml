<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<body>
		<referenceBlock name="checkout.root">
			<arguments>
				<argument name="jsLayout" xsi:type="array">
					<item name="components" xsi:type="array">
						<item name="checkout" xsi:type="array">
							<item name="children" xsi:type="array">
								<item name="sidebar" xsi:type="array">
									<item name="children" xsi:type="array">
										<item name="subscription-update" xsi:type="array">
											<item name="config" xsi:type="array">
												<item name="componentDisabled" xsi:type="boolean">true</item>
											</item>
										</item>
									</item>
								</item>
							</item>
						</item>
					</item>
				</argument>
			</arguments>
		</referenceBlock>
	</body>
</page>
