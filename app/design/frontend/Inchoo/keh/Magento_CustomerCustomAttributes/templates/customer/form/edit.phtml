<?php
/** @var Magento\CustomerCustomAttributes\Block\Form $block */
?>
<form class="form form-edit-account"
      action="<?= /* @noEscape */ $block->getUrl('customer/account/editPost') ?>"
      method="post" id="form-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>" autocomplete="off">
    <fieldset class="fieldset info">
        <?= $block->getBlockHtml('formkey') ?>
        <legend class="legend"><span><?= $block->escapeHtml(__('Account Information')) ?></span></legend><br>
        <?= $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Name::class)->setObject($block->getCustomer())->toHtml() ?><?php /* @codingStandardsIgnoreLine */ ?>

        <?php $_dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
        <?php $_taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
        <?php $_gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
        <?php if ($_dob->isEnabled()) : /* @codingStandardsIgnoreLine */ ?>
            <?= $_dob->setDate($block->getCustomer()->getDob())->toHtml() /* @codingStandardsIgnoreLine */ ?>
        <?php endif ?>
        <?php if ($_taxvat->isEnabled()) : /* @codingStandardsIgnoreLine */ ?>
            <?= $_taxvat->setTaxvat($block->getCustomer()->getTaxvat())->toHtml() /* @codingStandardsIgnoreLine */ ?>
        <?php endif ?>
        <?php if ($_gender->isEnabled()) : /* @codingStandardsIgnoreLine */ ?>
            <?= $_gender->setGender($block->getCustomer()->getGender())->toHtml() /* @codingStandardsIgnoreLine */ ?>
        <?php endif ?>
        <div class="field choice">
            <input type="checkbox" name="change_email" id="change-email" data-role="change-email" value="1"
                   title="<?= $block->escapeHtmlAttr(__('Change Email')) ?>" class="checkbox" />
            <label class="label" for="change-email">
                <span><?= $block->escapeHtml(__('Change Email')) ?></span>
            </label>
        </div>
        <div class="field choice">
            <input type="checkbox" name="change_password" id="change-password" data-role="change-password" value="1"
                   title="<?= $block->escapeHtmlAttr(__('Change Password')) ?>"
                <?php if ($block->getChangePassword()) : ?>
                    checked="checked"
                <?php endif; ?>
                   class="checkbox" />
            <label class="label" for="change-password">
                <span><?= $block->escapeHtml(__('Change Password')) ?></span>
            </label>
        </div>
        <?php $userDefinedAttributes = $block->getLayout()->getBlock('customer_form_user_attributes'); ?>
        <?php if ($userDefinedAttributes) :?>
            <?php $userDefinedAttributes->setEntityType('customer')->setShowContainer(false);?>
            <?php $block->restoreSessionData($userDefinedAttributes->getMetadataForm());?>
            <?= $userDefinedAttributes->toHtml() ?>
        <?php endif;?>
    </fieldset>

    <?= /* @noEscape */ $block->getChildHtml('form_fields_before_in_form') ?>

    <fieldset class="fieldset password" data-container="change-email-password">
        <legend class="legend">
            <span data-title="change-email-password"><?= $block->escapeHtml(__('Change Email and Password')) ?></span>
        </legend><br>
        <div class="field email required" data-container="change-email">
            <label class="label no-display" for="email"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email" autocomplete="email" name="email" id="email" data-input="change-email"
                       value="<?= $block->escapeHtmlAttr($block->getCustomer()->getEmail()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Email')) ?>"
                       placeholder="<?= $block->escapeHtmlAttr(__('Email')) ?>"
                       class="input-text" data-validate="{required:true, 'validate-email':true}" />
            </div>
        </div>
        <div class="field password current required">
            <label class="label no-display" for="current-password">
                <span><?= $block->escapeHtml(__('Current Password')) ?></span>
            </label>
            <div class="control">
                <input type="password" class="input-text" name="current_password"
                       id="current-password"
                       placeholder="<?= $block->escapeHtml(__('Current Password')) ?>"
                       data-input="current-password" autocomplete="off" />
            </div>
        </div>
        <div class="field new password required" data-container="new-password">
            <label class="label no-display" for="password">
                <span><?= $block->escapeHtml(__('New Password')) ?></span>
            </label>
            <div class="control">
                <input type="password" class="input-text" name="password" id="password"
                    data-password-min-length="<?= $block->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                    data-password-min-character-sets="<?= $block->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"<?php /* @codingStandardsIgnoreLine */ ?>
                    data-input="new-password"
                       placeholder="<?= $block->escapeHtml(__('New Password')) ?>"
                    data-validate="{required:true, 'validate-customer-password':true}"
                    autocomplete="off" />
                <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                    <div id="password-strength-meter" class="password-strength-meter">
                        <?= $block->escapeHtml(__('Password Strength')) ?>:
                        <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                            <?= $block->escapeHtml(__('No Password')) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="field confirm password required" data-container="confirm-password">
            <label class="label no-display" for="password-confirmation">
                <span><?= $block->escapeHtml(__('Confirm New Password')) ?></span>
            </label>
            <div class="control">
                <input type="password" class="input-text" name="password_confirmation" id="password-confirmation"
                    data-input="confirm-password"
                       placeholder="<?= $block->escapeHtml(__('Confirm New Password')) ?>"
                    autocomplete="off" />
            </div>
        </div>

        <div class="margin b-half">
            <?= $block->getChildHtml('form_additional_info') ?>
        </div>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action save primary" title="<?= $block->escapeHtmlAttr(__('Save')) ?>">
                <span><?= $block->escapeHtml(__('Save')) ?></span>
            </button>
        </div>
        <div class="secondary">
            <a class="action back" href="<?= $block->escapeUrl($block->getBackUrl()) ?>">
                <span><?= $block->escapeHtml(__('Go back')) ?></span>
            </a>
        </div>
    </div>
</form>

<script type="text/x-magento-init">
    {
        "[data-role=change-email], [data-role=change-password]": {
            "changeEmailPassword": {
                "titleChangeEmail": "<?= $block->escapeJs(__('Change Email')) ?>",
                "titleChangePassword": "<?= $block->escapeJs(__('Change Password')) ?>",
                "titleChangeEmailAndPassword": "<?= $block->escapeJs(__('Change Email and Password')) ?>"
            }
        },
        "*": {
            "Magento_CustomerCustomAttributes/validation": {
                "ignore": <?= (int) ($_dob->isEnabled() || $userDefinedAttributes->hasUserDefinedAttributes()) ?>,
                "hasUserDefinedAttributes": <?= (int) $userDefinedAttributes->hasUserDefinedAttributes() ?>,
                "isDobEnabled": <?= (int) $_dob->isEnabled() ?>,
                "disableAutoComplete": true,
                "mixins": [
                    "Magento_CustomerCustomAttributes/error-placement",
                    "Magento_CustomerCustomAttributes/validation-ignore"
                ]
            }
        },
        "[data-container=new-password]": {
            "passwordStrengthIndicator": {
                "formSelector": "form.form-edit-account"
            }
        }
    }
</script>
