<?php
// phpcs:disable Magento2.Templates.ThisInTemplate
// @codingStandardsIgnoreFile
?>
<?php
/**
 * Create account form template
 *
 * @var $block \Magento\Customer\Block\Form\Register
 */
?>
<?= $block->getChildHtml('form_fields_before') ?>
<?php /* Extensions placeholder */ ?>

<?= $block->getChildHtml('customer.form.register.extra') ?>
<form class="form create form-create-account container-narrow--s"
      action="<?= $block->escapeUrl($block->getPostActionUrl()) ?>" method="post" id="form-validate"
      enctype="multipart/form-data" autocomplete="off">
    <?= /* @noEscape */
    $block->getChildHtml('form_fields_before_in_form') ?>
    <?= /* @noEscape */
    $block->getBlockHtml('formkey'); ?>
    <div class="social-login-area">
        <p class="form-label-highlight social-media-login__label text-align center">
            <?= $block->escapeHtml(__('Create an account instantly using your Facebook or Google account:')) ?></p>
        <div class="social-login"><?= /* @noEscape */
            $block->getChildHtml('social-login') ?></div>
        <hr class="hr-divider" data-content="OR">
        <p class="form-label-highlight">
            <?= $block->escapeHtml(__('Please enter the following information to create your account.')) ?></p>
    </div>
    <fieldset class="fieldset fieldset--with-border create info">
        <p class="form-label-highlight"><?= $block->escapeHtml(__('Personal Information')) ?></p>
        <input type="hidden" name="success_url" value="<?= $block->escapeUrl($block->getSuccessUrl()) ?>"/>
        <input type="hidden" name="error_url" value="<?= $block->escapeUrl($block->getErrorUrl()) ?>"/>
        <?= $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Name::class)->setObject($block->getFormData())->setForceUseCustomerAttributes(true)->toHtml() ?>

        <div class="field required">
            <label for="email_address" class="label no-display"><span><?= $block->escapeHtml(__('Email')) ?></span></label>
            <div class="control">
                <input type="email" name="email" id="email_address" autocomplete="email"
                       value="<?= $block->escapeHtmlAttr($block->getFormData()->getEmail()) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Email')) ?>" class="input-text"
                       placeholder="<?= $block->escapeHtml(__('Email')) ?>"
                       data-validate="{required:true, 'validate-email':true}"/>
            </div>
        </div>

        <?php $_dob = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Dob::class) ?>
        <?php if ($_dob->isEnabled()) : ?>
            <?= $_dob->setDate($block->getFormData()->getDob())->toHtml() ?>
        <?php endif ?>

        <?php $_taxvat = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Taxvat::class) ?>
        <?php if ($_taxvat->isEnabled()) : ?>
            <?= $_taxvat->setTaxvat($block->getFormData()->getTaxvat())->toHtml() ?>
        <?php endif ?>

        <?php $_gender = $block->getLayout()->createBlock(\Magento\Customer\Block\Widget\Gender::class) ?>
        <?php if ($_gender->isEnabled()) : ?>
            <?= $_gender->setGender($block->getFormData()->getGender())->toHtml() ?>
        <?php endif ?>

        <?php $customerAttributes = $block->getChildBlock('customer_form_user_attributes'); ?>
        <?php if ($customerAttributes) : ?>
            <?php $customerAttributes->setObject($block->getFormData())->setEntityType('customer')->setShowContainer(false); ?>
            <?php $block->restoreSessionData($customerAttributes->getMetadataForm()); ?>
            <?= $customerAttributes->toHtml() ?>
        <?php endif; ?>

        <div class="field password required">
            <label for="password" class="label no-display"><span><?= $block->escapeHtml(__('Password')) ?></span></label>
            <div class="control">
                <input type="password" name="password" id="password"
                       title="<?= $block->escapeHtmlAttr(__('Password')) ?>"
                       class="input-text"
                       placeholder="<?= $block->escapeHtmlAttr(__('Password')) ?>"
                       data-password-min-length="<?= $block->escapeHtmlAttr($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?= $block->escapeHtmlAttr($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">
                <div id="password-strength-meter-container" data-role="password-strength-meter" aria-live="polite">
                    <div id="password-strength-meter" class="password-strength-meter">
                        <?= $block->escapeHtml(__('Password Strength')) ?>:
                        <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                        <?= $block->escapeHtml(__('No Password')) ?>
                    </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="field confirmation required">
            <label for="password-confirmation"
                   class="label no-display"><span><?= $block->escapeHtml(__('Confirm Password')) ?></span></label>
            <div class="control">
                <input type="password" name="password_confirmation"
                       title="<?= $block->escapeHtmlAttr(__('Confirm Password')) ?>" id="password-confirmation"
                       placeholder="<?= $block->escapeHtmlAttr(__('Confirm Password')) ?>"
                       class="input-text" data-validate="{required:true, equalTo:'.form-create-account #password'}"/>
            </div>
        </div>
        <?= $block->getChildHtml('form_additional_info') ?>

    </fieldset>

    <?php if ($block->getShowAddressFields()) : ?>
        <fieldset class="fieldset address">
            <legend class="legend"><span><?= $block->escapeHtml(__('Address Information')) ?></span></legend>
            <br>
            <input type="hidden" name="create_address" value="1"/>
            <div class="field company">
                <label for="company" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('company') ?></span></label>
                <div class="control">
                    <input type="text" name="company" id="company"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getCompany()) ?>"
                           title="<?= $block->escapeHtmlAttr(__('Company')) ?>" class="input-text <?= /* @noEscape */
                    $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('company') ?>"/>
                </div>
            </div>
            <div class="field telephone required">
                <label for="telephone" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('telephone') ?></span></label>
                <div class="control">
                    <input type="text" name="telephone" id="telephone"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getTelephone()) ?>"
                           title="<?= /* @noEscape */
                           $block->getAttributeData()->getFrontendLabel('telephone') ?>"
                           class="input-text <?= /* @noEscape */
                           $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('telephone') ?>"/>
                </div>
            </div>

            <?php $_streetValidationClass = $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('street'); ?>

            <div class="field street required">
                <label for="street_1" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('street') ?></span></label>
                <div class="control">
                    <input type="text" name="street[]"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getStreetLine(1)) ?>"
                           title="<?= $block->escapeHtmlAttr(__('Street Address')) ?>" id="street_1"
                           class="input-text <?= /* @noEscape */
                           $_streetValidationClass ?>"/>
                    <div class="nested">
                        <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
                        <?php for ($_i = 2, $_n = $this->helper(\Magento\Customer\Helper\Address::class)->getStreetLines(); $_i <= $_n; $_i++) : ?>
                            <div class="field additional">
                                <label class="label" for="street_<?= /* @noEscape */
                                $_i ?>">
                                    <span><?= $block->escapeHtml(__('Street Address %1', $_i)) ?></span>
                                </label>
                                <div class="control">
                                    <input type="text" name="street[]"
                                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getStreetLine($_i - 1)) ?>"
                                           title="<?= $block->escapeHtmlAttr(__('Street Address %1', $_i)) ?>"
                                           id="street_<?= /* @noEscape */
                                           $_i ?>" class="input-text <?= /* @noEscape */
                                    $_streetValidationClass ?>"/>
                                </div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            </div>

            <div class="field required">
                <label for="city" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('city') ?></span></label>
                <div class="control">
                    <input type="text" name="city"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getCity()) ?>"
                           title="<?= /* @noEscape */
                           $block->getAttributeData()->getFrontendLabel('city') ?>"
                           class="input-text <?= /* @noEscape */
                           $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('city') ?>"
                           id="city"/>
                </div>
            </div>

            <div class="field region required">
                <label for="region_id" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('region') ?></span></label>
                <div class="control">
                    <select id="region_id" name="region_id" title="<?= /* @noEscape */
                    $block->getAttributeData()->getFrontendLabel('region') ?>" class="validate-select region_id"
                            style="display:none;">
                        <option value=""><?= $block->escapeHtml(__('Please select a region, state or province.')) ?></option>
                    </select>
                    <input type="text" id="region" name="region"
                           value="<?= $block->escapeHtmlAttr($block->getRegion()) ?>" title="<?= /* @noEscape */
                    $block->getAttributeData()->getFrontendLabel('region') ?>" class="input-text <?= /* @noEscape */
                    $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('region') ?>"
                           style="display:none;"/>
                </div>
            </div>

            <div class="field zip required">
                <label for="zip" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('postcode') ?></span></label>
                <div class="control">
                    <input type="text" name="postcode"
                           value="<?= $block->escapeHtmlAttr($block->getFormData()->getPostcode()) ?>"
                           title="<?= /* @noEscape */
                           $block->getAttributeData()->getFrontendLabel('postcode') ?>" id="zip"
                           class="input-text validate-zip-international <?= /* @noEscape */
                           $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('postcode') ?>"
                           data-validate="{'required':true, 'validate-zip-international':true}"/>
                </div>
            </div>

            <div class="field country required">
                <label for="country" class="label"><span><?= /* @noEscape */
                        $block->getAttributeData()->getFrontendLabel('country_id') ?></span></label>
                <div class="control">
                    <?= $block->getCountryHtmlSelect() ?>
                </div>
            </div>
            <?php $addressAttributes = $block->getChildBlock('customer_form_address_user_attributes'); ?>
            <?php if ($addressAttributes) : ?>
                <?php $addressAttributes->setEntityType('customer_address'); ?>
                <?php $addressAttributes->setFieldIdFormat('address:%1$s')->setFieldNameFormat('address[%1$s]'); ?>
                <?php $block->restoreSessionData($addressAttributes->getMetadataForm(), 'address'); ?>
                <?= $addressAttributes->setShowContainer(false)->toHtml() ?>
            <?php endif; ?>
            <input type="hidden" name="default_billing" value="1"/>
            <input type="hidden" name="default_shipping" value="1"/>
        </fieldset>

    <?php endif; ?>

    <div class="actions-toolbar">
        <?php if ($block->isNewsletterEnabled()) : ?>
            <div class="field choice newsletter">
                <input type="checkbox" name="is_subscribed"
                       title="<?= $block->escapeHtmlAttr(__('Sign Up for Newsletter')) ?>" value="1"
                       id="is_subscribed"<?php if ($block->getFormData()->getIsSubscribed()) :
                    ?> checked="checked"<?php endif; ?> class="checkbox"/>
                <label for="is_subscribed"
                       class="label"><span><?= $block->escapeHtml(__('Sign Up for Newsletter')) ?></span></label>
            </div>
            <?php /* Extensions placeholder */ ?>
            <?= $block->getChildHtml('customer.form.register.newsletter') ?>
        <?php endif ?>
        <div class="primary">
            <button type="submit" class="action submit primary"
                    title="<?= $block->escapeHtmlAttr(__('Register')) ?>"><?= $block->escapeHtml(__('Register')) ?></button>
        </div>
    </div>
</form>
<?php if ($block->getShowAddressFields()) : ?>
    <script type="text/x-magento-init">
    {
        "#country": {
            "regionUpdater": {
                "optionalRegionAllowed": <?= /* @noEscape */
        ($block->getConfig("general/region/display_all") ? "true" : "false") ?>,
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */
        $this->helper(\Magento\Directory\Helper\Data::class)->getRegionJson() ?>,
                "defaultRegion": "<?= /* @noEscape */
        $block->getFormData()->getRegionId() ?>",
                "countriesWithOptionalZip": <?= /* @noEscape */
        $this->helper(\Magento\Directory\Helper\Data::class)->getCountriesWithOptionalZip(true) ?>
            }
        }
    }

    </script>
<?php endif; ?>
<script type="text/x-magento-init">
    {
        "*": {
            "Magento_CustomerCustomAttributes/validation": {
                "ignore": <?= (int)($_dob->isEnabled() || $customerAttributes->hasUserDefinedAttributes()) ?>,
                "hasUserDefinedAttributes": <?= (int)$customerAttributes->hasUserDefinedAttributes() ?>,
                "isDobEnabled": <?= (int)$_dob->isEnabled() ?>,
                "disableAutoComplete": true,
                "mixins": [
                    "Magento_CustomerCustomAttributes/error-placement",
                    "Magento_CustomerCustomAttributes/validation-ignore"
                ]
            }
        },
        ".field.password": {
            "passwordStrengthIndicator": {
                "formSelector": "form.form-create-account"
            }
        }
    }

</script>
