<?php
/**
 * Edit customer address template
 *
 * @var $block \Magento\Customer\Block\Address\Edit
 */
/** @var \Magento\Customer\Helper\Address $addressHelper */
$addressHelper = $this->helper(\Magento\Customer\Helper\Address::class);/** @codingStandardsIgnoreLine */
$requiredAttributesPrechecked = $block->getData('required_attributes_prechecked');
$streetAddressMaxLengthViewModel = $block->getData('addressStreetMaxLengthViewModel');
$streetAddressMaxLength = $streetAddressMaxLengthViewModel ? $streetAddressMaxLengthViewModel->getMaxLength() : 35;
?>
<form class="form-address-edit"
      action="<?= $block->escapeUrl($block->getSaveUrl()) ?>"
      method="post" id="form-validate"
      enctype="multipart/form-data"
      data-hasrequired="<?= $block->escapeHtmlAttr(__('* Required Fields')) ?>"
      data-validate='{"validation":{}}'
      autocomplete="off">
    <fieldset class="fieldset">
        <legend class="legend">
            <span><?= $block->escapeHtml(__('Contact Information')) ?></span>
        </legend><br>
        <?= $block->getBlockHtml('formkey') ?>
        <input type="hidden" name="success_url" value="<?= $block->escapeUrl($block->getSuccessUrl()) ?>"/>
        <input type="hidden" name="error_url" value="<?= $block->escapeUrl($block->getErrorUrl()) ?>"/>
        <?= $block->getNameBlockHtml() ?>
        <?php if ($addressHelper->isAttributeVisible('company')) : ?>
        <div class="field company
            <?php if (in_array('company', $requiredAttributesPrechecked)) : ?>
            required
            <?php endif; ?>">
            <label class="label no-display" for="company"><span><?= $block->escapeHtml(__('Company')) ?></span></label>
            <div class="control">
                <input type="text" name="company" id="company" title="<?= $block->escapeHtmlAttr(__('Company')) ?>"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getCompany()) ?>"
                       placeholder="<?= $block->escapeHtmlAttr(__('Company')) ?>"
                       class="input-text <?= $block->escapeHtmlAttr($addressHelper->getAttributeValidationClass('company')) ?>" /><?php /** @codingStandardsIgnoreLine */ ?>
            </div>
        </div>
        <?php endif; ?>
        <?php if ($addressHelper->isAttributeVisible('telephone')) : ?>
            <div class="field telephone
            <?php if (in_array('telephone', $requiredAttributesPrechecked)) : ?>
                required
            <?php endif; ?>">
            <label class="label no-display" for="telephone">
                <span><?= $block->escapeHtml(__('Phone Number')) ?></span>
            </label>
            <div class="control">
                <input type="text" name="telephone"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getTelephone()) ?>"
                        placeholder="<?= $block->escapeHtml(__('Phone Number')) ?>"
                       title="<?= $block->escapeHtmlAttr(__('Phone Number')) ?>"
                       class="input-text <?= /* @noEscape */ $addressHelper->getAttributeValidationClass('telephone') ?>"<?php /** @codingStandardsIgnoreLine */ ?>
                       id="telephone"/>
                <?php if (in_array('telephone', $requiredAttributesPrechecked)) : ?>
                    <span class="field-required-info" id="field-required-telephone"><?= $block->escapeHtml(__('Required field')) ?></span>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php if ($addressHelper->isAttributeVisible('fax')) : ?>
        <div class="field fax">
            <label class="label" for="fax"><span><?= $block->escapeHtml(__('Fax')) ?></span></label>
            <div class="control">
                <input type="text" name="fax" id="fax" title="<?= $block->escapeHtmlAttr(__('Fax')) ?>"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getFax()) ?>"
                       placeholder="<?= $block->escapeHtmlAttr(__('Fax')) ?>"
                       class="input-text <?= /* @noEscape */ $addressHelper->getAttributeValidationClass('fax') ?>"/><?php /** @codingStandardsIgnoreLine */ ?>
            </div>
        </div>
        <?php endif; ?>
    </fieldset>
    <fieldset class="fieldset">
        <legend class="legend">
            <span><?= $block->escapeHtml(__('Address')) ?></span>
        </legend><br>
        <?php $_streetValidationClass = $addressHelper->getAttributeValidationClass('street'); ?>
        <div class="field street required">
            <label for="street_1" class="label no-display">
                <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('street') ?></span>
            </label>
            <div class="control">
                <div class="field">
                    <input type="text" name="street[0]"
                           value="<?= $block->escapeHtmlAttr($block->getStreetLine(1)) ?>"
                           title="<?= $block->escapeHtmlAttr(__('Street Address')) ?>"
                           placeholder="<?= $block->escapeHtmlAttr(__('Street Address')) ?>"
                           id="street_1" class="input-text <?= /* @noEscape */ $_streetValidationClass ?>"
                           maxlength="<?= $block->escapeHtml($streetAddressMaxLength) ?>"
                           data-validate='{
                                "maxlength": <?= $block->escapeHtml($streetAddressMaxLength) ?>
                           }'
                           data-msg-maxlength="<?= $block->escapeHtml(__("Required field. Maximum %1 character limit", $streetAddressMaxLength)) ?>"
                    />
                    <span class="field-required-info"
                          id="field-required-street_1">
                    <?= $block->escapeHtml(__("Required field. Maximum %1 character limit", $streetAddressMaxLength)) ?>
                    </span>
                </div>
                <div class="nested">
                    <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?><?php /** @codingStandardsIgnoreLine */ ?>
                    <?php for ($_i = 2, $_n = $addressHelper->getStreetLines(); $_i <= $_n; $_i++) : ?>
                        <div class="field additional">
                            <label class="label no-display" for="street_<?= /* @noEscape */ $_i ?>">
                                <span><?= $block->escapeHtml(__('Street Address %1', $_i)) ?></span>
                            </label>
                            <div class="control">
                                <input type="text" name="street[<?= /* @noEscape */ $_i - 1 ?>]"
                                       value="<?= $block->escapeHtmlAttr($block->getStreetLine($_i)) ?>"
                                       title="<?= $block->escapeHtmlAttr(__('Street Address %1', $_i)) ?>"
                                       placeholder="<?= $block->escapeHtmlAttr(__('Street Address %1 (optional)', $_i)) ?>"
                                       id="street_<?= /* @noEscape */ $_i ?>"
                                       class="input-text <?= /* @noEscape */ $_streetValidationClass ?>"
                                       maxlength="<?= $block->escapeHtml($streetAddressMaxLength) ?>"
                                       data-validate='{
                                            "maxlength": <?= $block->escapeHtml($streetAddressMaxLength) ?>
                                       }'
                                       data-msg-maxlength="<?= $block->escapeHtml(__("Maximum %1 character limit", $streetAddressMaxLength)) ?>"
                                />
                                <span class="field-required-info"
                                      id="field-required-street_<?= /* @noEscape */ $_i ?>">
                                    <?= $block->escapeHtml(__("Maximum %1 character limit", $streetAddressMaxLength)) ?>
                                </span>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        </div>

        <?php if ($addressHelper->isVatAttributeVisible()) : ?>
            <div class="field taxvat">
                <label class="label no-display" for="vat_id">
                    <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?></span>
                </label>
                <div class="control">
                    <input type="text" name="vat_id"
                           value="<?= $block->escapeHtmlAttr($block->getAddress()->getVatId()) ?>"
                           placeholder="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?>"
                           title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('vat_id') ?>"
                           class="input-text <?= /* @noEscape */ $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('vat_id') ?>"<?php /** @codingStandardsIgnoreLine */ ?>
                           id="vat_id"/>
                </div>
            </div>
        <?php endif; ?>
        <div class="field city required">
            <label class="label no-display" for="city">
                <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?></span>
            </label>
            <div class="control">
                <input type="text" name="city"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getCity()) ?>"
                       placeholder="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                       title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('city') ?>"
                       class="input-text <?= /* @noEscape */ $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('city') ?>"<?php /** @codingStandardsIgnoreLine */ ?>
                       id="city"/>
                <span class="field-required-info" id="field-required-city"><?= $block->escapeHtml(__('Required field')) ?></span>
            </div>
        </div>
        <div class="field region required">
            <label class="label no-display" for="region_id">
                <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?></span>
            </label>
            <div class="control">
                <select id="region_id" name="region_id"
                        title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                        class="validate-select region_id" <?= (!$block->getConfig('general/region/display_all')) ? ' disabled="disabled"' : '';?>><?php /** @codingStandardsIgnoreLine */ ?>
                    <option value=""><?= $block->escapeHtml(__('Please select a region, state or province.')) ?></option><?php /** @codingStandardsIgnoreLine */ ?>
                </select>
                <input type="text" id="region" name="region" value="<?= $block->escapeHtmlAttr($block->getRegion()) ?>"
                       title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                       placeholder="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('region') ?>"
                       class="input-text <?= /* @noEscape */ $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('region') ?>"<?= (!$block->getConfig('general/region/display_all')) ? ' disabled="disabled"' : '';?>/><?php /** @codingStandardsIgnoreLine */ ?>
            </div>
        </div>
        <div class="field zip required">
            <label class="label no-display" for="zip">
                <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?></span>
            </label>
            <div class="control">
                <input type="text" name="postcode"
                       value="<?= $block->escapeHtmlAttr($block->getAddress()->getPostcode()) ?>"
                       placeholder="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                       title="<?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('postcode') ?>"
                       id="zip"
                       class="input-text validate-zip-international <?= /* @noEscape */ $this->helper(\Magento\Customer\Helper\Address::class)->getAttributeValidationClass('postcode') ?>"/><?php /** @codingStandardsIgnoreLine */ ?>
                <div role="alert" class="message warning" style="display:none">
                    <span></span>
                </div>
                <span class="field-required-info" id="field-required-zip"><?= $block->escapeHtml(__('Required field')) ?></span>
            </div>
        </div>
        <div class="field country required">
            <label class="label no-display" for="country">
                <span><?= /* @noEscape */ $block->getAttributeData()->getFrontendLabel('country_id') ?></span>
            </label>
            <div class="control">
                <?= $block->getCountryHtmlSelect() ?>
            </div>
        </div>

        <?php $userDefinedAttributes = $block->getLayout()->getBlock('customer_form_address_user_attributes'); ?>
        <?php if ($userDefinedAttributes) :?>
            <?= $userDefinedAttributes->setShowContainer(false)->toHtml() ?>
        <?php endif;?>
        <?php if ($block->isDefaultBilling()) : ?>
            <div class="message info">
                <span><?= $block->escapeHtml(__("This is your default billing address.")) ?></span>
            </div>
        <?php elseif ($block->canSetAsDefaultBilling()) : ?>
            <div class="field choice set billing">
                <input type="checkbox" id="primary_billing" name="default_billing" value="1" class="checkbox"/>
                <label class="label" for="primary_billing">
                    <span><?= $block->escapeHtml(__('Use as my default billing address')) ?></span>
                </label>
            </div>
        <?php else : ?>
            <input type="hidden" name="default_billing" value="1"/>
        <?php endif; ?>

        <?php if ($block->isDefaultShipping()) : ?>
            <div class="message info">
                <span><?= $block->escapeHtml(__("This is your default shipping address.")) ?></span>
            </div>
        <?php elseif ($block->canSetAsDefaultShipping()) : ?>
            <div class="field choice set shipping">
                <input type="checkbox" id="primary_shipping" name="default_shipping" value="1" class="checkbox"/>
                <label class="label" for="primary_shipping">
                    <span><?= $block->escapeHtml(__('Use as my default shipping address')) ?></span>
                </label>
            </div>
        <?php else : ?>
            <input type="hidden" name="default_shipping" value="1"/>
        <?php endif; ?>

        <?= $block->getChildHtml('form.additional.info') ?>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action submit primary" data-action="save-address"
                    title="<?= $block->escapeHtmlAttr(__('Save Address')) ?>">
                <span><?= $block->escapeHtml(__('Save Address')) ?></span>
            </button>
        </div>
        <div class="secondary">
            <a class="action back" href="<?= $block->escapeUrl($block->getBackUrl()) ?>">
                <span><?= $block->escapeHtml(__('Go back')) ?></span>
            </a>
        </div>
    </div>
</form>
<script type="text/x-magento-init">
    {
        "#form-validate": {
            "addressValidation": {
                "postCodes": <?= /* @noEscape */ $block->getPostCodeConfig()->getSerializedPostCodes(); ?>
            }
        },
        "#country": {
            "regionUpdater": {
                "optionalRegionAllowed": <?= /* @noEscape */ ($block->getConfig('general/region/display_all') ? 'true' : 'false') ?>,<?php /** @codingStandardsIgnoreLine */ ?>
                "regionListId": "#region_id",
                "regionInputId": "#region",
                "postcodeId": "#zip",
                "form": "#form-validate",
                "regionJson": <?= /* @noEscape */ $this->helper(\Magento\Directory\Helper\Data::class)->getRegionJson() ?>,<?php /** @codingStandardsIgnoreLine */ ?>
                "defaultRegion": "<?= /* @noEscape */ $block->getRegionId() ?>",
                "countriesWithOptionalZip": <?= /* @noEscape */ $this->helper(\Magento\Directory\Helper\Data::class)->getCountriesWithOptionalZip(true) ?><?php /** @codingStandardsIgnoreLine */ ?>
            }
        },
        "#telephone": {
            "telephone-custom-validation": {
                "telephoneInputSelector": "#telephone",
                "countryInputSelector": "#country"
            }
        }
    }
</script>
