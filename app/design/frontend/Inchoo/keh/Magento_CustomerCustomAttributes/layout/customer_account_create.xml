<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="page.main.title">
            <arguments>
                <argument name="css_class" xsi:type="string">container-narrow--s text-align center</argument>
            </arguments>
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Create An Account</argument>
            </action>
        </referenceBlock>
    </body>
</page>
