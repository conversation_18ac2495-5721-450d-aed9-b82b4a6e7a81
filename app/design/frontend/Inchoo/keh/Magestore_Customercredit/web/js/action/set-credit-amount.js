/**
 * Customer store credit(balance) application
 */
/*global define,alert*/
define(
    [
        'ko',
        'jquery',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/model/resource-url-manager',
        'Magento_Checkout/js/model/error-processor',
        'Magestore_Customercredit/js/model/payment/discount-messages',
        'mage/storage',
        'Magento_Checkout/js/action/get-payment-information',
        'Magento_Checkout/js/model/totals',
        'mage/translate',
        'Magestore_Customercredit/js/action/reload-shipping-method',
        'mage/url'
    ],
    function (ko, $, quote, urlManager, errorProcessor, messageContainer, storage, getPaymentInformationAction, totals, $t, reloadShippingMethod, urlBuilder) {
        'use strict';

        return function (amount, isApplied, isLoading, creditdata) {
            var credit_amount = amount.call();
            var url = urlBuilder.build('customercredit/checkout/amountPost');
            var params = {
                credit_amount: Number(credit_amount)
            };

            if (Number.isNaN(params.credit_amount) || params.credit_amount <= 0) {
                let response = {
                    status: null,
                    responseText: JSON.stringify({
                        message: $t('Invalid credit amount.')
                    })
                }
                isLoading(false);
                totals.isLoading(false);
                errorProcessor.process(response, messageContainer);
                return;
            }

            return $.post(
                url,
                params
            ).done(
                function (response) {
                    var res = JSON.parse(response);
                    amount(res.credit_amount);
                    $('#credit_balance').text(res.getFormatedBalance);
                    var deferred = $.Deferred();
                    totals.isLoading(true);
                    getPaymentInformationAction(deferred);
                    reloadShippingMethod();
                    $.when(deferred).done(function () {
                        messageContainer.addSuccessMessage({'message': 'Store credit was applied.'});
                        isApplied(true);
                        totals.isLoading(false);
                    });
                }
            ).fail(
                function (response) {
                    messageContainer.addErrrMessage({'message': 'An error occurred. Please try again.'});

                    totals.isLoading(false);
                    errorProcessor.process(response, messageContainer);
                }
            ).always(
                function () {
                    isLoading(false);
                }
            );
        };
    }
);
