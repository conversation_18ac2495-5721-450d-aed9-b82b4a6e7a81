<!-- ko if: isEnable() -->
<div class="payment-option _collapsible opc-payment-additional discount-code"
     data-bind="mageInit: {'collapsible':{'openedState': '_active'}}">
    <div class="payment-option-title field choice" data-role="title">
        <span class="action action-toggle with-icon" id="block-discount-heading" role="heading" aria-level="2">
            <span><!-- ko i18n: 'Apply Credit Discount'--><!-- /ko --></span>
        </span>
    </div>

    <div class="payment-option-content" data-role="content">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <!-- ko if: isLoggedin()  -->
        <!-- ko ifnot: isInGroup() -->
        <span data-bind="i18n: 'We are sorry that you can not use Store Credit'"></span>
        <!-- /ko -->
        <!-- ko if: isInGroup() -->
        <!-- ko if: isHasCreditItem() -->
        <strong><span data-bind="i18n: 'Store Credit cannot be used to purchase Store Credit products'"></span></strong>
        <!-- /ko -->
        <!-- ko ifnot: isCreditItemOnly() -->
        <form class="form form-discount form-discount-credit" id="discount-credit-form"
              data-bind="blockLoader: isLoading">
            <div class="payment-option-inner">
                <div class="field">
                    <label class="label" for="discount-credit">
                        <span data-bind="i18n: 'Your current credit Balance is '"></span>
                        <strong id="credit_balance" data-bind="text: creditdata.getFormatedBalance"></strong>
                        </br>
                        <!-- ko if: creditdata.credit_balance_raw > 0 -->
                        <span data-bind="i18n: 'Enter a credit amount if you want to pay by Customer Credit'"></span>
                        <!-- /ko -->
                    </label>
                    <!-- ko if: creditdata.credit_balance_raw > 0 -->
                    <div class="control">
                        <input class="input-text"
                               type="text"
                               id="discount-credit"
                               name="discount_credit"
                               data-validate="{'required-entry':true}"
                               data-bind="value: amount, attr:{placeholder: $t('Enter credit amount')} "/>
                    </div>
                    <!-- /ko -->
                </div>
            </div>
            <!-- ko if: creditdata.credit_balance_raw > 0 -->
            <div class="actions-toolbar">
                <div class="primary">
                    <button class="action action-apply primary" type="submit"
                            data-bind="'value': $t('Apply'), click: apply">
                        <span><!-- ko i18n: 'Apply'--><!-- /ko --></span>
                    </button>
                    <!-- ko if: isApplied() -->
                    <button class="action action-apply secondary"
                            type="submit"
                            data-bind="'value': $t('Cancel'), click: cancel">
                        <span><!-- ko i18n: 'Cancel'--><!-- /ko --></span>
                    </button>
                    <!-- /ko -->
                </div>
            </div>
            <!-- /ko -->
        </form>
        <!-- /ko -->
        <!-- /ko -->
        <!-- /ko -->
        <!-- ko ifnot: isLoggedin() -->
        <div class="discount">
            <div class="checkout-cart-credit-amount">
                <span data-bind="i18n: 'Please'"></span>
                <a data-bind="attr: { href: loginLink()}, i18n: 'login'"></a>
                <span data-bind="i18n: 'to use customer credit'"></span>
            </div>
        </div>
        <!-- /ko-->
    </div>
</div>
<!-- /ko -->
