define([
    "jquery",
    "jquery-ui-modules/widget",
    'matchMedia'
], function($, widget, matchMedia) {
    "use strict";

    $.widget('inchoo.menuMedellin', {
        _init: function () {
            var bodyStyles = window.getComputedStyle(document.documentElement);
            var bpLarge = bodyStyles.getPropertyValue('--large');

            this.classes = {
                noScrollClass: 'no-scroll',
                navToggleIconClass: 'toggle-nav--bar--transformed',
                navigationToggleClass: 'header-navigation--visible',
                navWrapClass: 'nav__wrap',
                navWrapActiveClass: 'nav__wrap--active',
                lvl0ActiveClass: 'nav__item--lvl-0--active',
                lvl1ActiveClass: 'nav__item--lvl-1--active',
                lvl2ActiveClass: 'nav__link__lvl-2--active',
                parentItemActiveClass: 'nav__item--parent--active',
                viewAllClass: 'nav__link--view-all'
            };

            this.navToggleEl = $('#toggle-nav');
            this.navToggleIconEl = $('.toggle-nav--bar');
            this.navigationEl = $('.header-navigation');
            this.parentElMobile = $('.nav__link--parent');
            this.backElMobile = $('.nav__link--back');
            this.htmlEl = $('html');
            this.level0Items = $('.nav__link__lvl-0');
            this.level1Items = $('.nav__link__lvl-1');
            this.navWrapSelector = '.' + this.classes.navWrapClass;
            this.navWrapActiveSelector = '.' + this.classes.navWrapActiveClass;
            this.parentActiveSelector = '.' + this.classes.parentItemActiveClass;
            this.minicartButtonEl = $('.action.showcart');
            this.userAccountButtonEl = $('.action.account-menu');
            this.searchFieldEl = $('#search');

            if ((typeof bpLarge !== 'undefined') && (bpLarge === '')) {
                bpLarge = '1024px';
            }

            matchMedia({
                media: '(min-width:' + bpLarge + ')',
                entry: $.proxy(function () {
                    this.loadLevel0Events();
                    this.loadLevel1Events();
                    this.identifyCurrentLink();

                    this.navToggleEl.off('click');
                    this.parentElMobile.off('click');
                    this.backElMobile.off('click');

                    this.htmlEl.on('click', this.onBodyClick.bind(this));
                    this.navigationEl.on('mouseleave', this.onNavigationLeave.bind(this));

                    this.cleanup();
                }, this),
                exit: $.proxy(function () {
                    this.navToggleEl.on('click', this.onNavToggleClick.bind(this));
                    this.parentElMobile.on('click', this.onParentClick.bind(this));
                    this.backElMobile.on('click', this.onBackClick.bind(this));
                    this.minicartButtonEl.on('click', this.toggleOffcanvas.bind(this));
                    this.userAccountButtonEl.on('click', this.toggleOffcanvas.bind(this));
                    this.searchFieldEl.on('focus', this.toggleOffcanvas.bind(this));

                    this.htmlEl.off('click');
                }, this)
            });

            this.manageCompareProducts();
        },

        /**
         * @description on desktop, perform cleanup of navigation (close)
         */
        onNavigationLeave: function () {
            $(this.parentActiveSelector).removeClass(this.classes.parentItemActiveClass);
        },

        /**
         * @description remove all custom css classes when entering desktop media query
         * */
        cleanup: function () {
            this.htmlEl.removeClass(this.classes.noScrollClass);
            this.navToggleIconEl.removeClass(this.classes.navToggleIconClass);
            this.navigationEl.removeClass(this.classes.navigationToggleClass);
            this.level0Items.removeClass(this.classes.lvl0ActiveClass);
            this.level1Items.removeClass(this.classes.lvl1ActiveClass);

            $(this.navWrapSelector).removeClass(this.classes.noScrollClass);
            $(this.navWrapActiveSelector).removeClass(this.classes.navWrapActiveClass);
        },

        /**
         * @description reset offcanvas on account icon click, minicart click, search field focus (mobile only)
        * */
        toggleOffcanvas: function () {
            if (this.navigationEl.hasClass(this.classes.navigationToggleClass)) {
                this.navigationEl.removeClass(this.classes.navigationToggleClass);
                this.navToggleIconEl.removeClass(this.classes.navToggleIconClass);
                this.htmlEl.removeClass(this.classes.noScrollClass);
            }
        },

        /**
         * @description when user clicks on menu icon in header
         */
        onNavToggleClick: function () {
            // console.log('on nav toggle click');

            /* toggle icon display */
            if ( this.navToggleIconEl.hasClass(this.classes.navToggleIconClass)) {
                 this.navToggleIconEl.removeClass(this.classes.navToggleIconClass)
            } else {
                 this.navToggleIconEl.addClass(this.classes.navToggleIconClass)
            }

            /* toggle offcanvas display */
            if (this.navigationEl.hasClass(this.classes.navigationToggleClass)) {
                this.navigationEl.removeClass(this.classes.navigationToggleClass)
            } else {
                this.navigationEl.addClass(this.classes.navigationToggleClass);
            }

            if (this.htmlEl.hasClass(this.classes.noScrollClass)) {
                this.htmlEl.removeClass(this.classes.noScrollClass);
            } else {
                this.htmlEl.addClass(this.classes.noScrollClass);
            }
        },
        /**
         * @description when user clicks on parent menu link in offcanvas navigation
         */
        onParentClick: function (e) {
            // console.log('on parent click');
            e.preventDefault();

            // open child subcategory
            $(e.currentTarget).next().addClass(this.classes.navWrapActiveClass);

            // if list overflows, scroll to top of list at the moment of displaying child subcategory
            $(e.currentTarget)
                .parents(this.navWrapSelector)
                .animate({
                    scrollTop: 0
                }, "fast");

            // disable parent category scroll
            if ($(e.currentTarget).parents(this.navWrapActiveSelector).length) {
                $(e.currentTarget).parents(this.navWrapActiveSelector)
                    .addClass(this.classes.noScrollClass);
            }
        },

        /**
         * @description when user clicks on "back to {{category}}" link in offcanvas navigation
         */
        onBackClick: function (e) {
            // console.log('on back click');
            e.preventDefault();

            // close current category
            $(e.currentTarget).closest(this.navWrapActiveSelector).removeClass(this.classes.navWrapActiveClass);

            // enabler scroll on grandparent category only
            $(e.currentTarget).parents(this.navWrapActiveSelector).eq(0)
                .removeClass(this.classes.noScrollClass)
        },

        /**
         * @description manage tab navigation events on lvl 0 items
         */
        loadLevel0Events: function () {
            var self = this;

            this.level0Items.each(function (i, item) {
                $(item).on('mouseenter', function () {
                    // console.log('lvl 0 mouse enter');
                    $(this).parent()
                        .addClass(self.classes.parentItemActiveClass)
                        .siblings().removeClass(self.classes.parentItemActiveClass)
                });

                $(item).on('focus', function () {
                    // console.log('lvl 0 focus');
                    $(this)
                        .parent().addClass(self.classes.parentItemActiveClass)
                        .siblings().removeClass(self.classes.parentItemActiveClass)
                });
            });
        },

        /**
         * @description manage tab navigation events on lvl 1 items
         */
        loadLevel1Events: function () {
            var self = this;

            this.level1Items.each(function (i, item) {
                $(item).on('mouseenter', function () {
                    // console.log('lvl 1 mouse enter');
                    $(this).parent()
                        .addClass(self.classes.parentItemActiveClass)
                        .siblings().removeClass(self.classes.parentItemActiveClass);
                });

                $(item).on('focus', function () {
                    // console.log('lvl 1 focus');
                    $(this)
                        .parent().addClass(self.classes.parentItemActiveClass)
                        .siblings().removeClass(self.classes.parentItemActiveClass);
                });
            });
        },

        /**
         * @description user has clicked somewhere outside navigation
         */
        onBodyClick: function (e) {
            if (!$(e.target).parents('.header-navigation').length) {
                /* reset all subnavs */

                $(this.parentActiveSelector).removeClass(this.classes.parentItemActiveClass);
                this.htmlEl.removeClass(this.classes.noScrollClass);
            }
        },

        /**
         * @description highlight active link (and its parent(s)) in navigation (desktop only)
         */
        identifyCurrentLink: function () {
            var suffix = '.html';
            var pathname = window.location.pathname.replace(suffix,'').substr(1);
            var slugs = pathname.split('/');

            // remove any trailing slash that might be an issue when targeting elements with that URL
            pathname = pathname.replace(/\/+$/, '');

            if (pathname) {
                if (pathname.indexOf('shop') > -1) {
                    switch (pathname) {
                        case 'shop/brands':
                        case 'shop/all-lenses-mounts':
                            $('a.nav__link__lvl-1[href*="'+pathname+'"]')
                                .parent().addClass(this.classes.parentItemActiveClass)
                            break;
                        default:
                            var counter = 0,
                                self = this;

                            slugs.every(function(slug, i){
                                var pingHref = slugs.slice(0, slugs.length-counter).join('/');

                                if (!$('body').hasClass('is-blog')) {
                                    pingHref += suffix
                                }

                                var pingLink = $('a[href$="'+pingHref+'"]');

                                if (pingLink.length) {
                                    pingLink.each(function (i, link) {
                                        switch ($(link).parents(self.navWrapSelector).length) {
                                            case 1:
                                                // link is subnav lvl 1 link
                                                self._highlightLvl1Link(link);
                                                break;

                                            case 2:
                                                // link is subnav lvl 2 link
                                                self._highlightLvl2Link(link);
                                                break;
                                        }
                                    });

                                    return false
                                }

                                counter++;
                                return true
                            });

                            break;
                    }
                }

                // because of /sell and /reversecheckout/cart/sell in top menu
                if (pathname.indexOf('sell') > -1) {
                    var href = window.location.href.replace(/\/+$/, '');

                    this._highlightLvl1Link($('a[href="' + href + '"].nav__link__lvl-1'))
                }

                // because of /sell/events in top menu
                if (pathname.indexOf('events') > -1) {
                    $('a[href*="events"].nav__link__lvl-1')
                        .parent()
                        .addClass(this.classes.parentItemActiveClass)
                }

                // because of /blog categories
                if (pathname.indexOf('blog') > -1) {
                    var categorySlugs = pathname.split('/');
                    var slugCount = categorySlugs.length;

                    switch (slugCount) {
                        case 3:
                            this._highlightLvl1Link($('a.nav__link__lvl-1[href$="' + pathname + '"]'))
                            break;

                        case 4:
                            this._highlightLvl2Link($('a.nav__link[href$="' + pathname + '"]'))
                            break;

                    }
                }
            }
        },

        /**
         *
         * @param link element to target
         * @description helper method that highlights active current link in lvl1 menu
         * @private
         */
        _highlightLvl1Link: function(link) {
            $(link)
                .parent()
                .addClass(this.classes.parentItemActiveClass);
        },

        /**
         *
         * @param link element to target
         * @description helper method that highlights active current link in lvl1 menu
         * @private
         */
        _highlightLvl2Link: function(link) {
            if (!$(link).hasClass(this.classes.viewAllClass)) {
                $(link).addClass(this.classes.lvl2ActiveClass);
            }

            $(link)
                .parents('.nav__item--lvl-1')
                .addClass(this.classes.parentItemActiveClass);
        },

        /**
         * @description hide <li> that displays the product compare block if there is no products to compare
         */
        manageCompareProducts: function () {
            var cacheStorage = JSON.parse(localStorage.getItem('mage-cache-storage'));

            if (!cacheStorage) {
                return false;
            }

            var compareProducts = cacheStorage['compare-products'];
            if (!compareProducts) {
                return false
            }

            var compareCount = compareProducts.count;

            if (!compareCount) {
                $('.compare-products-link').hide();
            }
        }
    });

    return $.inchoo.menuMedellin;
});
