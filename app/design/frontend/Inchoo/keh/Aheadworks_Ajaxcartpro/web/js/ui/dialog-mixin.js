define([
    'jquery',
    'Aheadworks_Ajaxcartpro/js/config',
    'jquery/ui'
], function ($, awAcpConfig) {
    "use strict";

    return function (acp) {
        $.widget('awacp.acpDialog', acp, {
            options: {
                actionClose: '[data-action-custom="close"]'
            },

            _create: function () {
                this._super();

                var self = this;

                $('body').delegate('.aw-acp-popup ' + this.options.actionClose, 'click', function (e) {
                    self.onCancelClick(e);
                });
            },

            onContinueClick: function (event) {
                event.preventDefault();

                this._close();
            }
        });

        return $.awacp.acpDialog;
    }
});
