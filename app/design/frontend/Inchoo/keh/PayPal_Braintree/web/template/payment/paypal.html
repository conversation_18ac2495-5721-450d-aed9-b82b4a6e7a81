<div class="payment-method" data-bind="css: {'_active': isActive()}" id="payment-method-braintree-paypal">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()">
        <label class="label" data-bind="attr: {'for': getCode()}">
            <!-- PayPal Logo -->
            <img data-bind="attr: {src: getPaymentAcceptanceMarkSrc(), alt: $t('Acceptance Mark')}"
                 class="payment-icon">
            <!-- PayPal Logo -->
            <span text="getTitle()"></span>
        </label>
    </div>

    <div class="payment-method-content">
        <each args="getRegion('messages')" render=""></each>
        <fieldset class="braintree-paypal-fieldset" data-bind='attr: {id: "payment_form_" + getCode()}'>
            <div id="paypal-container"></div>
        </fieldset>
        <div class="checkout-agreements-block">
            <each args="$parent.getRegion('before-place-order')" render=""></each>
        </div>
        <!-- ko if: (vaultEnabler.isVaultEnabled())-->
        <div class="field choice" data-bind="visible: !isReviewRequired()">
            <input type="checkbox"
                   name="vault[is_enabled]"
                   class="checkbox"
                   data-bind="attr: {'id': getCode() + '_enable_vault'}, checked: vaultEnabler.isActivePaymentTokenEnabler">
            <label class="label" data-bind="attr: {'for': getCode() + '_enable_vault'}">
                <span><!-- ko i18n: 'Save for later use.'--><!-- /ko --></span>
            </label>
            <div class="field-tooltip toggle">
                <span class="field-tooltip-action action-vault"
                      tabindex="0"
                      data-toggle="dropdown"
                      data-bind="attr: {title: $t('What is this?')}, mageInit: {'dropdown':{'activeClass': '_active'}}">
                    <span translate="'What is this?'"></span>
                </span>
                <div class="field-tooltip-content"
                     data-target="dropdown"
                     translate="'We store you payment information securely on Braintree servers via SSL.'"></div>
            </div>
        </div>
        <!-- /ko -->
        <div class="braintree-paypal-actions" data-bind="visible: isReviewRequired()">
            <div class="payment-method-billing-address">
                <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
                <!-- ko template: getTemplate() --><!-- /ko -->
                <!--/ko-->
            </div>
            <br>
            <p class="payment-method-item braintree-paypal-account">
                <span class="payment-method-type">PayPal</span>
                <span class="payment-method-description" text="customerEmail()"></span>
            </p>
            <div class="actions-toolbar">
                <div class="primary">
                    <button data-button="paypal-place" data-role="review-save"
                            type="submit"
                            data-bind="attr: {id: getCode() + '_place_order', title: $t('Place Order')}, enable: (isActive()), click: placeOrder"
                            class="action primary checkout"
                            disabled>
                        <span translate="'Place Order'"></span>
                    </button>
                </div>
            </div>
        </div>
        <div class="actions-toolbar" data-bind="visible: !isReviewRequired()">
            <div class="primary">
                <div data-bind="attr: {id: getPayPalButtonId()}"></div>
                <!-- ko if: (isCreditEnabled())-->
                <div data-bind="attr: {id: getCreditButtonId()}"></div>
                <!-- /ko -->
                <!-- ko if: (isPaylaterEnabled())-->
                <div data-bind="attr: {id: getPaylaterButtonId()}"></div>
                <!-- /ko -->
                <!-- ko if: (getIsPaylaterMessageEnabled())-->
                <div data-pp-message data-pp-placement="payment"
                     data-bind='attr: {"data-pp-amount": getGrandTotalAmount(), "data-pp-style-layout": getMessagingLayout(), "data-pp-style-logo-type": getMessagingLogo(), "data-pp-style-logo-position": getMessagingLogoPosition(), "data-pp-style-text-color": getMessagingTextColor()}'
                ></div>
                <!-- /ko -->
            </div>
        </div>
    </div>
</div>
