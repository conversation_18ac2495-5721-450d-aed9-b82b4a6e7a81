/* CSS of Klevu Search Result Landing Page */

/* klevu container for fix width layout*/

.kuContainer{
    width:100%;
    margin:0 auto;
    font-family: inherit;
    font-size: 12px;
    background-color:#ffffff;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    padding:10px;
}


/* klevu container for fluid layout*/
.kuProListing{
    margin-top:20px;
    margin-bottom:20px;
}

/* klevu filters */
.kuFilters{
    float:left;
    width: 20%;
}

/* set the height of each filter*/
.kuFilterBox{
    height:190px;
    overflow:hidden;
    margin-bottom: 30px;
}

/* heading of filters i.e brand, color */
.kuFilterHead{
    padding: 6px;
    font-size: 13px;
    text-align: left;
    line-height: 1.8;
    border-bottom: 1px solid #c7c8ca;
    text-transform: uppercase;
    font-weight: 600;
    color: #414042;
}

/* down arrow div in filters */
.kuShowOpt{
    text-align: left;
    padding: 3px;
    padding-left: 35px;
    margin-bottom: 20px;
    margin-top: -15px;
}

/* up arrow div in filters */
.kuHideOpt{
    text-align: left;
    padding: 3px;
    padding-left: 35px;
    margin-bottom: 20px;
    margin-top: -15px;
}

.kuShowOpt img, .kuHideOpt img{
    width:auto !important;
}

/* down & up arrow link color in filters */
.kuShowOpt a,.kuHideOpt a{
    color:#4d7abf;
    text-decoration:none;
}

/* set list for filters */
.kuFilterNames ul{
    margin:0px;
    padding:0px;
    margin-top:10px;
    margin-left:0px !important;
}

/* style for each values in filter */
.kuFilterNames ul li{
    list-style:none;
    text-align:left;
    width:99%;
    display:inline-table;
    margin:0px;
    padding-left:0px;
    margin-left:0px !important;
}

.kuFilterNames ul li a{
    display: block;
    position: relative;
    overflow: hidden;
    margin: 0 5px;
    padding-left: 5px;
    padding-right: 5px;
    font-size: 13px;
    text-decoration: none;
    cursor: pointer;
    font-style:normal;
    color:#414042;
}

/* set background color on hover of filter */
.kuFilterNames ul li a:hover{
    cursor: pointer;
}

/* set background color for selected filter */
.kuFilterNames ul li.kuSelected a{
    color: #414042;
}

/* label for the filter */
.kuFilterNames ul li a span.kuFilterLabel{
    float:left;
    width:86%;
    margin:0px;
    padding:0px;
    font-weight:normal;
}

/* total nos of results available for filter  */
.kuFilterNames ul li a span.kuFilterTotal{
    float:right;
    width:13%;
    text-align:right;
}


.kuFilterLabel:before {
    content: '';
    border: 1px solid #c7c8ca;
    border-radius: 50%;
    margin-right: 8px;
    height: 12px;
    width: 12px;
    display: inline-block;
    color: #777;
    margin-top: 0px;
    position: relative;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    transition: background 200ms;
    -webkit-transition: background 200ms;
    -moz-transition: background 200ms;
}


.kuFilterNames ul li a:hover span.kuFilterLabel:before {
    background:#414042;
    border: 1px solid #414042;
}

.kuFilterNames ul li.kuSelected span.kuFilterLabel:before {
    background:#414042;
    border: 1px solid #414042;
}

.kuMulticheck .kuFilterLabel:before {
    content: '';
    border: 1px solid #c7c8ca;
    border-radius:0px;
    margin-right: 8px;
    height: 15px;
    width: 15px;
    color: #777;
    margin-bottom:-1px;
    position: relative;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    transition: background 200ms;
    -webkit-transition: background 200ms;
    -moz-transition: background 200ms;
    transition: border-color 200ms;
    -webkit-transition: border-color 200ms;
    -moz-transition: border-color 200ms;
}


.kuMulticheck .kuFilterNames ul li a:hover span.kuFilterLabel:before {
    background:#ffffff;
    border: 1px solid #414042;
}

.kuMulticheck .kuFilterNames ul li.kuSelected span.kuFilterLabel:before {
    background:#ffffff;
    border: 1px solid #414042;
}

.kuMulticheck .kuHover .kuFilterNames ul li a:hover span.kuFilterLabel:after{
    content: '';
    position: absolute;
    width: 9px;
    height: 5px;
    background: transparent;
    top: 3px;
    left: 8px;
    border: 2px solid #cccccc;
    border-top: none;
    border-right: none;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.kuMulticheck .kuFilterNames ul li.kuSelected span.kuFilterLabel:after {
    content: '';
    position: absolute;
    width: 9px;
    height: 5px;
    background: transparent;
    top: 3px;
    left: 8px;
    border: 2px solid #414042;
    border-top: none;
    border-right: none;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.kuMulticheck .kuFilterNames ul li.kuSelected:hover span.kuFilterLabel:after {
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border-color: #414042;
}

/* shows cancel button if filter is selected */
.kuFilterNames ul li a span.kuFilterCancel{
    float:right;
    width:13%;
    text-align:right;
}

/* klevu results box */
.kuResultList{
    float:right;
    width: 78%;
    margin-top:3px;
}

/* div for Sorting, pagination, change result view icons*/
.kuSortHeader{
    padding-top: 0px;
    margin-top: 0px;
}


.kuResultInfo{
    border-bottom: 1px solid #c7c8ca;
}


/* div to display total no of results */
.kuTotalResultsTab{
    float: left;
    width: 40%;
}

.kuTotResults{
    text-align:left;
    margin-bottom:10px;
    line-height:24px;
    font-weight:bold;
    font-size:14px;
    float:left;
    color: #414042;
}

.kuTotalResultsTab .kuTabs{
    line-height:30px;
}

.kuTotalResultsTab .kuTabs a{
    padding:8px;
    cursor:pointer;
    font-size: 12px;
    margin-right: 5px;
    transition: background 200ms;
    -webkit-transition: background 300ms;
    -moz-transition: background 200ms;
    color: #414042;
    font-family: Apercu-Bold, sans-serif;
}

.kuTotalResultsTab .kuTabs a:hover{
    text-decoration:none;
}

.kuTotalResultsTab a.kuTabSelected:hover{
    background:none;
}

.kuSortingOpt{
    padding-top: 12px;
    padding-bottom: 12px;
    width: 100%;
    margin-top: 3px;
}

/* div to display sorting dropdown */
.kuSortby{
    float:left;
    width: 26%;
}

/* label of sorting dropdown */
.kuSortby label{
    display:inline;
    color: #414042;
}

/* sorting dropdown */
.kuSortby select{
    display:inline;
    height:auto;
    min-height:25px;
    width:120px;
}

/* div to display icons to change the view of result (grid/view) */
.kuView{
    float: right;
    width:15%;
    text-align: right;
}

.kuView a {
    cursor:pointer;
}

.kuView span:before {
    font-family: keh2;
}

/* display GRID view icon */
.kuView .icon-gridview:before {
    content: "\e91c";
}

/* display LIST view icon */
.kuView .icon-listview:before {
    content: "\e91e";
}

/* set width and height of view icons box*/
.kuView a{
    display: inline-block;
    width: 22px;
    height: 22px;
    margin-left: 4px;
    text-align: center;
    vertical-align: middle;
    overflow: hidden;
    -webkit-transition:background-position .1s ease-in;
    -moz-transition:background-position .1s ease-in;
    -o-transition:background-position .1s ease-in;
    transition:background-position .1s ease-in;
}

/* changing background position on hover of GRID/LIST view icons */
.kuView a:hover{
    background-position:0 -25px;
    text-decoration: none;
}

/* change background position to set current view */
.kuView a.kuCurrent{
    background-position: 0 -25px;
    text-decoration: none;
}

/* dropdown to select no of results per page*/
.kuPerPage{
    float:left;
    width:25%;
    margin-left:10px;
    text-align: left;
}

.kuPerPage label{
    display:inline;
    color: #414042;
}

.kuPerPage select{
    width:auto !important;
    height:auto;
    min-height:25px;
}

/* div for pagination */
.kuPagination{
    width:33%;
    float: right;
    margin-left:10px;
    text-align:right;
    margin-bottom: 5px;
}

/* style to display page nos in line */
.kuPagination a {
    margin:0px;
    position: relative;
    display: inline-block;
    padding-left: 4px;
    padding-right: 5px;
    color: #414042;
    cursor: pointer;
    text-decoration:none;
    font-size: 13px;
}

/* style to show current page */
.kuPagination a.kuCurrent{
    background: #fff;
    -webkit-box-shadow:none;
    -moz-box-shadow:none;
    box-shadow:none;
    font-weight:bold;
}

.kuOtherContent .kuPagination{
    float:right;
}

.kuOtherContent .kuPerPage{
    margin-left:0px;
    text-align:left;
}

.kuPagination a[title="First"],
.kuPagination a[title="Previous"],
.kuPagination a[title="Next"],
.kuPagination a[title="Last"] {
    font-size: 0;
}

.kuPagination a[title="First"]:before {
    content: "\e918";
}

.kuPagination a[title="Previous"]:before {
    content: "\e911";
}

.kuPagination a[title="Next"]:before {
    content: "\e912";
}

.kuPagination a[title="Last"]:before {
    content: "\e919";
}

.kuPagination a[title="First"]:before,
.kuPagination a[title="Previous"]:before,
.kuPagination a[title="Next"]:before,
.kuPagination a[title="Last"]:before {
    font-size: 12px;
    font-family: keh2;
}

@media (min-width: 768px) {
    .kuPagination a[title="First"]:before,
    .kuPagination a[title="Previous"]:before,
    .kuPagination a[title="Next"]:before,
    .kuPagination a[title="Last"]:before {
        font-size: 12px;
    }
}

.kuClearLeft{
    clear:left;
    line-height:0px;
}

.klevu-clearboth-listview{
    clear:left;
}

.kuDiscountBadge{
    background: #414042;
    padding:7px 0px 0px 0px;
    color: #ffffff;
    width:47px;
    height:40px;
    border-radius:50%;
    font-weight: bold;
    position: absolute;
    text-align: center;
    -moz-transition: all 0.5s ease;
    -webkit-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

.kuDiscountBadge span{
    display:block;
}

/* klevu results div */
.kuResults{
    margin-top:10px;
}

.kuOtherContentView{
    margin-top:10px;
}

.kuOtherContentView ul li .kuNameDesc{
    margin-top:0px !important;
}

/* styles for list view results */
.kuListView{
    margin-top:10px;
}

.kuListView ul{
    margin:0px;
    padding:0px;
}

/* In LISTVIEW: list style for each result */
.kuListView ul li{
    position:relative;
    display:block;
    width: 100%;
    height: auto;
    padding: 10px;
    text-align:left;
    margin-bottom: 32px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    padding: 15px;
    border: 1px solid #eeeeee;
}

.kuListView ul li:hover{
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
    border-color: transparent;
    transition: box-shadow 400ms ease-out;
    -moz-transition: box-shadow 400ms ease-out;
    -webkit-transition: box-shadow 400ms ease-out;
    -o-transition: box-shadow 400ms ease-out;
}

/* In LISTVIEW:  for wrapping the image in fixed size div */
.kuListView .klevuImgWrap{
    float:left;
    overflow:hidden;
    width: 15% !important;
    height:200px !important;
    text-align:center;
}

/* In LISTVIEW: thumbnail of the product */
.kuListView img{
    max-width:100% !important;
    max-height: 200px !important;
    height:auto;
    width:auto;
    border:none;
    outline:none;
    display:inline-block !important;
    -webkit-transform: scaleY(1);
    -moz-transform: scaleY(1);
    -o-transform: scaleY(1);
    -ms-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
}

.kuListView ul li:hover img{
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -o-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
}

.kuListView ul li .kuDiscountBadge{
    top: 10px;
    left: 10px;
}

.kuListView .kuStarsSmall {
    height: 13px;
    display: inline-block;
    vertical-align: bottom;
    background: transparent url(https://js.klevu.com/klevu-js-v1/img-1-1/star-gray.png) top left repeat-x;
    width: 66px;
    margin-left:5px;
}

.kuListView .kuStarsSmall .kuRating {
    background: transparent url(https://js.klevu.com/klevu-js-v1/img-1-1/star-yellow.png) top left repeat-x;
    height: 13px;
}

/* In LISTVIEW: display product name and description */
.kuListView ul li .kuNameDesc{
    float:left;
    width: 52%;
    margin-left:5px;
    margin-top: 15px;
}

.kuListView ul li .kuName{
    padding:5px;
}

.kuListView ul li .kuDesc{
    line-height:20px;
    padding:5px;
    font-style:normal;
    color: #414042;
}

/* In LISTVIEW: set product name color and font size */
.kuListView ul li .kuName a{
    font-size: 14px;
    text-decoration:none;
    color:inherit;
    font-style:normal;
    color: #414042;
}

.kuListView ul li .kuName a:hover{
    text-decoration:underline;
}

/* In LISTVIEW: div to display saleprice and original price */
.kuListView ul li .kuPrice{
    float:left;
    width:15%;
    margin-top: 15px;
    margin-bottom:5px;
    padding:5px;
    text-align:center;
    color: #414042;
}

/* In LISTVIEW: div to display saleprice */
.kuListView ul li .kuSalePrice{
    font-weight:bold;
    font-size: 16px;
    margin-bottom:5px;
    font-family: inherit;
}

/* In LISTVIEW: div to display original price with line-through style */
.kuListView ul li .kuOrigPrice{
    font-size:13px;
    text-decoration:line-through;
}

/* styles for grid view results */
.kuGridView{
    margin-top:10px;
}

.kuGridView ul{
    margin:0px;
    padding:0px;
    margin-left:0px;
}

/* In GRIDVIEW: list style for each result */
.kuGridView ul li{
    display: inline-block;
    width: 23.5%;
    min-height: 365px;
    padding: 0;
    vertical-align: top;
    text-align:center;
    margin: 8px;
    padding-top:15px;
    margin-left:0px !important;
    margin-bottom: 20px;
    font-style:normal;
    position:relative;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    padding: 10px;
    border:1px solid #eeeeee;
    border-radius:3px;
}

.kuGridView ul li:hover{
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
    border-color: transparent;
    transition: box-shadow 400ms ease-out;
    -moz-transition: box-shadow 400ms ease-out;
    -webkit-transition: box-shadow 400ms ease-out;
    -o-transition: box-shadow 400ms ease-out;
}

/* In GRIDVIEW:  for wrapping the image in fixed size div */
.kuGridView .klevuImgWrap{
    float:none;
    overflow:hidden;
    width: 100% !important;
    height: 210px !important;
    text-align:center;
    margin:0 auto;
    position:relative;
}

/* In GRIDVIEW: thumbnail of the product */
.kuGridView img{
    display: block;
    max-width:100% !important;
    max-height: 200px !important;
    height:auto;
    width:auto;
    bottom: -100%;
    top: -100%;
    left: 0;
    right: 0;
    margin: auto !important;
    border:none;
    outline:none;
    display:inline-block !important;
    position: absolute;
    -webkit-transform: scaleY(1);
    -moz-transform: scaleY(1);
    -o-transform: scaleY(1);
    -ms-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    vertical-align: middle;
}

.kuGridView ul li:hover img{
    -webkit-transform: scale(1.1);
    -moz-transform: scale(1.1);
    -o-transform: scale(1.1);
    -ms-transform: scale(1.1);
    transform: scale(1.1);
}

.kuGridView ul li .kuDiscountBadge{
    top: 10px;
    right: 10px;
}

.kuGridView ul li:hover .kuDiscountBadge{
    width:57px;
    height:47px;
    padding-top:10px;
}

/* In GRIDVIEW: remove float value for name and description div */
.kuGridView .kuNameDesc{
    float:none;
    text-align: left;
    padding-left: 10px;
    margin-top: 10px;
}

/* In GRIDVIEW: discription is not displayed in grid layout*/
.kuGridView .kuDesc{
    display:none;
}

.kuGridView ul li .kuName{
    margin-bottom:5px;
}

.kuGridView .kuStarsSmall {
    height: 13px;
    display: inline-block;
    vertical-align: bottom;
    background: transparent url(https://js.klevu.com/klevu-js-v1/img-1-1/star-gray.png) top left repeat-x;
    width: 66px;
}

.kuGridView .kuStarsSmall .kuRating {
    background: transparent url(https://js.klevu.com/klevu-js-v1/img-1-1/star-yellow.png) top left repeat-x;
    height: 13px;
}


/* CSS for add to cart button */
.kuAddtocart{
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.kuAddtocart input[type="text"]{
    display: none;
    border:1px solid #ddd;
    outline:none;
    text-align:right;
}

.kuAddtocart a.kuAddtocartBtn{
    background: #414042;
    color: #fff;
    padding: 7px 15px;
    margin-bottom: 0;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    text-decoration:none;
    font-size: 13px;
    line-height: 19px;
    text-transform: uppercase;
}

/* add to cart in grid view */
.kuGridView ul li .kuAddtocart{
    width: 95%;
    margin-bottom: 15px;
    text-align: left;
    padding-left: 10px;
    position: absolute;
    bottom: 0;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s, opacity 0.5s linear;
    -webkit-transition: visibility 0s, opacity 0.5s linear;
    -moz-transition: visibility 0s, opacity 0.5s linear;
}

.kuGridView ul li input[type="text"]{
    width:25%;
}

.kuGridView ul li:hover .kuAddtocart{
    visibility: visible;
    opacity: 1;
}

/* add to cart in list view */
.kuListView ul li .kuAddtocart{
    width: 15%;
    float:left;
    margin-top: 30px;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0s, opacity 0.5s linear;
    -webkit-transition: visibility 0s, opacity 0.5s linear;
    -moz-transition: visibility 0s, opacity 0.5s linear;
}

.kuListView ul li input[type="text"]{
    width:5%;
    float:none;
}

.kuListView ul li:hover .kuAddtocart{
    visibility: visible;
    opacity: 1;
}

/* In GRIDVIEW: set product name color and font size */
.kuGridView ul li .kuName a{
    font-size: 14px;
    text-decoration:none;
    color: #414042;

}

.kuGridView ul li .kuName a:hover{
    text-decoration:underline;
}

/* In GRIDVIEW: div to display saleprice and original price */
.kuGridView ul li .kuPrice{
    float:none;
    margin-top: 12px;
    padding-left: 10px;
    text-align: left;
    color: #414042;
}

/* In GRIDVIEW: div to display saleprice */
.kuGridView ul li .kuSalePrice{
    font-size: 14px;
    font-weight:bold;
    font-family: inherit;
    text-align: left;
    display: inline;
    margin-right: 10px;
}

.priceGreyText{
    font-size:11px;
    color: #636363;
}

/* In GRIDVIEW: div to display original price with line-through style */
.kuGridView ul li .kuOrigPrice{
    font-size: 12px;
    text-decoration:line-through;
    text-align: left;
    display: inline;
}

.kuGridView ul li .kuVariants{
    text-align:left;
}

/* In GRIDVIEW: set color to highlight search keyowrd in name and description */
.kuGridView ul li strong{
    color:#2980B9;
}


/* pagination links at bottom of results */
.kuBottomPagi{
    padding-top: 8px;
    border-top: 1px solid #c7c8ca;
}

.kuBottomPagi .kuPerPage{
    margin-left:0px;
    text-align: left;
}

.kuBottomPagi .kuPagination{
    width: 50%;
    float: right;
    margin-top:5px;
}

.kuPagination a{
    font-style:normal;
}

.kuClearBoth{
    clear:both;
}

/* div to display No records found message */

/* No result found css*/

.kuNoRecordFound {
    text-align: center;
    margin-top: 5%;
    margin-bottom: 5%;
    color: #414042;
    font-size: 13px;
    background: #FFF;
    display:none;

}
.kuNoResults-lp {
    background: #FFF;
    width: 100%;
    margin: 30px auto;
    max-width: 850px;
}
.kuNoResults-lp ul {
    margin: 0px;

    padding: 0px;
}

.kuNoResults-lp-message {
    font-size: 18px;
    padding: 25px 5%;
    text-align: center;
}
.kuNoResults-lp-relatedProducts {
    width: 95%;
    margin: 30px auto;
}
.kuNoResults-lp-title {
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 40px;
    font-size: 18px;
    display: block;
}
.kuNoResults-lp-relatedProduct-list > li {
    display: inline-block;
    margin-right: 2%;
    width: 23%;
    min-height: 120px;
    margin-bottom: 25px;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align: top;
    padding: 0;
    text-align: center;
}
.kuNoResults-lp-relatedProduct-list > li a.kuRelatedProductbox-lp {
    position: relative;
    text-decoration: none;
    color: #000;
    outline: none;
    display: block;
    height: auto;
    min-height: 200px;
}
.kuNoResults-lp-relatedProduct-list > li:hover a {
    text-decoration: none;
}
.kuNoResults-lp-relatedProduct-list > li:hover {
    text-decoration: none;
}
.kuNoResults-lp-relatedProduct-list > li:hover .ku-Name {
    text-decoration: underline;
}
.kuNoResults-lp-relatedProduct-list > li:last-child {
    margin-right: 0px;
}
.kuNoResultsImgWrap-lp {
    float: none;
    height: 180px;
    margin: 0 auto;
    max-width: 180px;
    overflow: hidden;
    text-align: center;
    width: 100%;
}
.kuNoResultsImgWrap-lp img {
    max-width: 100%;
    max-height: 180px;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    -ms-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out;
    vertical-align: middle;
    width: auto;
    height: auto;
    border: none;
    outline: none;
}
.kuRelatedProductbox-lp .kuProduct-desc {
    font-size: 12px;
    padding: 5px 0 0;
    float: none;
}
.kuRelatedProductbox-lp .kuDiscount-badge {
    background: #000;
    padding: 3px 8px;
    color: #FFF;
    font-weight: bold;
    position: absolute;
    top: 0px;
    left: 0px;
    display: none;
}
.kuRelatedProductbox-lp .ku-Name {
    font-size: 12px;
    text-decoration: none;
    color: #000;
    line-height: 16px;
    min-height: 22px;
    margin-top: 5px;
    text-align: center;
}
.kuRelatedProductbox-lp .ku-Desc {
    display: none;
}
.kuRelatedProductbox-lp .kuStarsSmall {
    height: 13px;
    vertical-align: bottom;
    background: transparent url(https://js.klevu.com/klevu-js-v1/img-1-1/star-gray.png) top left repeat-x;
    width: 65px;
    display: none;
}
.kuRelatedProductbox-lp .kuPrice {
    margin-bottom: 5px;
    margin-top: 5px;
    float: none;
    padding: 0px;
    text-align: center;
}
.kuRelatedProductbox-lp .kuSalePrice {
    display: inline-block;
    font-weight: bold;
    font-size: 13px;
    color: #000;
}
.kuRelatedProductbox-lp .kuOrigPrice {
    display: inline-block;
    font-size: 12px;
    text-decoration: line-through;
    color: #555;
}
.kuRelatedProductbox-lp .kuVariants {
    font-size: 12px;
    text-align: left;
    color: #777;
    display: none;
}
.kuRelatedProductbox-lp .kuSalePrice span.klevuPriceGreyText{
    font-weight: normal;
    font-size: 13px !important;
    margin-bottom: 5px;
    color: #393939 !important;
    display:none;
}
.kuNoResults-lp .kuNoResults-lp-pSearch {
    text-align: center;
}
.kuNoResults-lp .kuNoResults-lp-pSearch strong { font-weight:600;}
.kuNoResults-lp .kuNoResults-lp-pSearch > a {
    font-size: 13px;
    text-transform: capitalize;
    text-decoration: none;
    color: #000;
    display: inline-block;
}
.kuNoResults-lp .kuNoResults-lp-pSearch > a:hover {
    text-decoration: underline;
}
/* ends No result found css */

/* div to display or query message */
.kuOrQueryMessage{
    text-align: center;
    margin-top: 10px;
    color: #414042;
    background: #ebebeb;
    font-size: 13px;
    padding: 7px 0px !important;
    margin: 10px 15px;
}

.kuOrQueryMessage span {
    font-weight: 600;
}


/* show variants */
.kuVariants{
    font-size: 10px;
    margin-top:2px;
    color: #414042;
}

/* height for loader div */
#loader{
    height:400px;
}

#loader img{
    margin-top:10%;
    display: inline-block;
    width:auto !important;
}

.kuOtherContent{
    display:none;
}

.disableKuFilter{
    opacity:0.3;
    pointer-events: none;
}


/* Klevu Price slider style */
.kuPriceRangeSlider{
    width: 80%;
    margin: 0 auto;
    margin-top: 30px;
    min-height: 50px;
}

.kuPS-target,
.kuPS-target * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -ms-touch-action: none;
    touch-action: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.kuPS-target {
    position: relative;
    direction: ltr;
}
.kuPS-base {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}
.kuPS-origin {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    bottom: 0;
}

.kuPS-handle {
    position: relative;
    z-index: 1;
}

.kuPS-stacking .kuPS-handle {
    z-index: 10;
}

.kuPS-state-tap .kuPS-origin {
    -webkit-transition: left 0.3s, top 0.3s;
    transition: left 0.3s, top 0.3s;
}

.kuPS-base,
.kuPS-handle {
    -webkit-transform: translate3d(0,0,0);
    transform: translate3d(0,0,0);
}

.kuPS-horizontal {
    height: 4px;
}

.kuPS-horizontal .kuPS-handle {
    width: 18px;
    height: 18px;
    left: -9px;
    top: -7px;
}

.kuPS-vertical {
    width: 18px;
}

.kuPS-vertical .kuPS-handle {
    width: 28px;
    height: 34px;
    left: -6px;
    top: -17px;
}

.kuPS-background {
    background: #c7c8ca;
}

.kuPS-connect {
    background: #414042;
    -webkit-transition: background 450ms;
    transition: background 450ms;
}

.kuPS-origin {
    border-radius: 2px;
}
.kuPS-target {
    border-radius: 4px;
}

.kuPS-draggable {
    cursor: w-resize;
}

.kuPS-vertical .kuPS-draggable {
    cursor: n-resize;
}

.kuPS-handle {
    border: 1px solid #414042;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
}

.kuPS-tooltip {
    display: block;
    position: absolute;
    text-align: center;
    font-size:13px;
    margin-left: -9px;
    background-color: #ffffff;
}

.kuPS-horizontal .kuPS-handle-lower .kuPS-tooltip {
    bottom: -24px;
    color: #414042;
}
.kuPS-horizontal .kuPS-handle-upper .kuPS-tooltip {
    bottom: -24px;
    color: #414042;
}

/* set opacity to filter */
.disableKlevuFilter{
    opacity:0.3;
    pointer-events: none;
}

/*-----------------------css for enabling filters in mobile  and filter tags--------------------------------------------*/
/* css for filter tags*/
.kuContainer #ku-search-filter-tags {
    display: block;
    color: #222222;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;
    text-align: left;
    padding-bottom: 0px;
    padding-left: 0px;
    position: relative;
}
.ku-search-filter-tag {
    background: #eee;
    border: 0px solid #f8f8f8;
    margin-right: 4px;
    border-radius: 0px;
    font-size: 11px;
    padding: 0px 5px;
    color: #222;
    margin-bottom: 4px;
    display: inline-block;
    line-height: 20px;
}
.ku-search-filter-remove, .ku-search-filter-remove-all {
    cursor: pointer;
    font-size: 12px;
    color: #222222;
    text-decoration: none;
}
.ku-search-filter-remove-all a {
    color: #222222;
    font-size: 12px;
    text-decoration: none;
    cursor: pointer;
}
.ku-search-filter-remove-all a:hover {
    color: #333;
}
/* ends css for filter tags*/
.kuFilterHead {
    cursor: pointer;
}
.kuExpand {
    position: relative !important;
    transition: all 0.3s ease;
}
.kuFilterHead.kuExpand::after {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: #000;
    border-image: none;
    border-style: solid;
    border-width: 0 1px 1px 0;
    content: "";
    display: inline-block;
    padding: 4px;
    position: absolute;
    right: 12px;
    top: 10px;
    transform: rotate(45deg);
    transition: all 0.3s ease 0s;
}
.kuCollapse {
    position: relative !important;
    transition: all 0.3s ease;
}
.kuFilterHead.kuCollapse::after {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: #000;
    border-image: none;
    border-style: solid;
    border-width: 0 1px 1px 0;
    content: "";
    display: inline-block;
    padding: 4px;
    position: absolute;
    right: 12px;
    top: 16px;
    transform: rotate(-135deg);
    transition: all 0.3s ease;
}
/* to remove arrow border when filtrs on top*/
.kuContainer.kuFiltersTop .kuFilterHead.kuCollapse::after {
    border-width: 1px 0px 0px 1px;
    position: initial;
}
.kuContainer.kuFiltersTop .kuFilterHead.kuExpand::after {
    border-width: 0 1px 1px 0;
    position: initial;
}
/*-----------------------ends css for enabling filters in mobile and filter tags--------------------------------------------*/

.reverse-validation-failed .validation-advice, .reverse-validation-failed ~ .validation-advice, .validation-failed .validation-advice, .validation-failed ~ .validation-advice {
    color: #c5532a
}

.validation-advice {
    padding-top: .33333rem;
    font-size: .77778rem;
    line-height: 1rem;
    font-family: Fira Sans, sans-serif;
    font-style: italic
}

.validation-advice + .validation-advice {
    display: none
}

.validation-advice.error-msg {
    color: #c5532a
}

.reverse-validation-failed ~ .validation-advice {
    display: none
}

.reverse-validation-failed ~ .validation-advice.validation-status {
    display: block
}

.input-with-button {
    -webkit-box-shadow: 0 1px 0 0 #666;
    box-shadow: 0 1px 0 0 #666
}

.buttons-set {
    padding-bottom: 1.33333rem
}

.buttons-set:after {
    display: block;
    clear: both;
    content: ""
}

@media (min-width: 35.5em) {
    .buttons-set {
        padding-bottom: 2rem
    }
}

.buttons-set .back-link {
    padding-top: .66667rem;
    padding-bottom: .66667rem
}

.buttons-set > * {
    float: left
}

.buttons-set > :not(:last-child) {
    margin-bottom: .66667rem
}

@media (min-width: 35.5em) {
    .buttons-set > :not(:last-child) {
        margin-right: 1rem;
        margin-bottom: 0
    }
}

.buttons-set-login .button-login {
    width: 100%
}

.form-alt input, .form-alt select {
    -webkit-box-shadow: 0 1px 0 0 #ccc;
    box-shadow: 0 1px 0 0 #ccc
}

.form-alt textarea {
    -webkit-box-shadow: 0 0 0 1px #ccc;
    box-shadow: 0 0 0 1px #ccc
}

.form-alt select {
    color: #ccc
}

.form-alt input::-webkit-input-placeholder {
    color: #fff;
    opacity: 1
}

.form-alt input:-ms-input-placeholder, .form-alt input::-ms-input-placeholder {
    color: #fff;
    opacity: 1
}

.form-alt input::placeholder {
    color: #fff;
    opacity: 1
}

.form-alt input[type=checkbox] ~ label:before, .form-alt input[type=radio] ~ label:before {
    color: #ccc
}

.form-alt .input-with-button {
    -webkit-box-shadow: 0 1px 0 0 #ccc;
    box-shadow: 0 1px 0 0 #ccc
}

.customer-dob {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.customer-dob .dob {
    position: relative;
    width: 2.8125rem;
    margin-right: 1.25rem
}

.col2-set {
    padding-bottom: 1rem
}

.no-flexbox .col2-set:after {
    display: block;
    clear: both;
    content: ""
}

@media (min-width: 38.75em) {
    .no-flexbox .col2-set > .col-1, .no-flexbox .col2-set > .col-2 {
        width: 48.35165%;
        margin-right: 2.58065%;
        float: left
    }
}

@media (min-width: 64.0625em) {
    .no-flexbox .col2-set > .col-1, .no-flexbox .col2-set > .col-2 {
        width: 48.70968%
    }
}

@media (min-width: 38.75em) {
    .no-flexbox .col2-set > .col-2 {
        margin-right: 0;
        float: right
    }
}

.flexbox .col2-set {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.flexbox .col2-set > .col-1, .flexbox .col2-set > .col-2 {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
}

@media (min-width: 38.75em) {
    .flexbox .col2-set > .col-1, .flexbox .col2-set > .col-2 {
        -webkit-flex-basis: 48.35165%;
        -ms-flex-preferred-size: 48.35165%;
        flex-basis: 48.35165%
    }
}

@media (min-width: 64.0625em) {
    .flexbox .col2-set > .col-1, .flexbox .col2-set > .col-2 {
        -webkit-flex-basis: 48.70968%;
        -ms-flex-preferred-size: 48.70968%;
        flex-basis: 48.70968%
    }
}

@media (max-width: 64em) {
    .flexbox .col2-set-persistent > .col-1, .flexbox .col2-set-persistent > .col-2 {
        -webkit-flex-basis: 48.35165%;
        -ms-flex-preferred-size: 48.35165%;
        flex-basis: 48.35165%
    }
}

.flexbox .col2-right-layout .main {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.flexbox .col2-right-layout .col-main {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-ordinal-group: 2;
    -webkit-order: 1;
    -ms-flex-order: 1;
    order: 1
}

@media (min-width: 64.0625em) {
    .flexbox .col2-right-layout .col-main {
        -webkit-flex-basis: 74.35484%;
        -ms-flex-preferred-size: 74.35484%;
        flex-basis: 74.35484%
    }
}

.flexbox .col2-right-layout .col-right {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
    -ms-flex-order: 2;
    order: 2
}

@media (min-width: 64.0625em) {
    .flexbox .col2-right-layout .col-right {
        -webkit-flex-basis: 23.06452%;
        -ms-flex-preferred-size: 23.06452%;
        flex-basis: 23.06452%
    }
}

.col2-right-layout .no-flexbox .main:after {
    display: block;
    clear: both;
    content: ""
}

.col2-right-layout .no-flexbox .col-main {
    width: 74.35484%;
    margin-right: 2.58065%;
    float: left
}

.col2-right-layout .no-flexbox .col-right {
    width: 23.06452%
}

.col2-right-layout .col-main:empty, .col2-right-layout .col-right:empty {
    display: none
}

.col3-set {
    padding-bottom: 1rem
}

.no-flexbox .col3-set:after {
    display: block;
    clear: both;
    content: ""
}

.no-flexbox .col3-set > .col-1, .no-flexbox .col3-set > .col-2, .no-flexbox .col3-set > .col-3 {
    width: 31.6129%;
    margin-right: 2.58065%;
    float: left
}

.no-flexbox .col3-set > .col-3 {
    margin-right: 0;
    float: right
}

.flexbox .col3-set {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between
}

@media (min-width: 48em) {
    .flexbox .col3-set > .col-1, .flexbox .col3-set > .col-2, .flexbox .col3-set > .col-3 {
        -webkit-flex-basis: 31.6129%;
        -ms-flex-preferred-size: 31.6129%;
        flex-basis: 31.6129%
    }
}

.flexbox .col2-left-layout .main {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.flexbox .col2-left-layout .col-main {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
}

@media (min-width: 64.0625em) {
    .flexbox .col2-left-layout .col-main {
        -webkit-flex-basis: 74.35484%;
        -ms-flex-preferred-size: 74.35484%;
        flex-basis: 74.35484%
    }
}

.flexbox .col2-left-layout .col-left {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
}

@media (min-width: 64.0625em) {
    .flexbox .col2-left-layout .col-left {
        -webkit-flex-basis: 23.06452%;
        -ms-flex-preferred-size: 23.06452%;
        flex-basis: 23.06452%
    }
}

.no-flexbox .col2-left-layout .main:after {
    display: block;
    clear: both;
    content: ""
}

.no-flexbox .col2-left-layout .col-main {
    width: 74.35484%;
    float: right
}

.no-flexbox .col2-left-layout .col-left {
    width: 23.06452%;
    margin-right: 2.58065%;
    float: left
}

.col2-left-layout .col-left:empty, .col2-left-layout .col-main:empty {
    display: none
}

.flexbox .col3-layout .main {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.flexbox .col3-layout .col-main {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
}

@media (min-width: 64.0625em) {
    .flexbox .col3-layout .col-main {
        -webkit-flex-basis: 48.70968%;
        -ms-flex-preferred-size: 48.70968%;
        flex-basis: 48.70968%
    }
}

.col3-layout .col-left:empty, .col3-layout .col-main:empty, .col3-layout .col-right:empty {
    display: none
}

.no-flexbox .col3-layout .main:after {
    display: block;
    clear: both;
    content: ""
}

.no-flexbox .col3-layout .col-main {
    width: 48.70968%;
    margin-right: 2.58065%;
    float: left
}

.no-flexbox .col3-layout .col-left {
    width: 23.06452%;
    margin-right: 2.58065%;
    float: left
}

.no-flexbox .col3-layout .col-right {
    width: 23.06452%;
    float: right
}

.col3-layout .col-left, .col3-layout .col-right {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%
}

@media (min-width: 64.0625em) {
    .col3-layout .col-left, .col3-layout .col-right {
        -webkit-flex-basis: 23.06452%;
        -ms-flex-preferred-size: 23.06452%;
        flex-basis: 23.06452%
    }
}

.kuContainer {
    font-size: inherit;
    padding: 0
}


@media (min-width: 64.0625em) {
    .kuContainer a {
        text-transform: uppercase;
        font-size: .88889rem;
        line-height: 1.33333rem
    }
}

.kuContainer select {
    padding-right: 12px;
    min-width: 60px;
}

.kuGridView ul {
    padding-left: 0;
    list-style: none
}

.kuGridView ul, .kuGridView ul li {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.kuGridView ul li {
    margin-bottom: .66667rem;
    position: relative;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 2rem .66667rem 1rem;
    border: 1px solid #ccc;
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    overflow: hidden;
    border-radius: 0;
    margin-top: 0;
    min-height: 0
}

@media (min-width: 32.5em) {
    .kuGridView ul li {
        padding-top: 1.66667rem;
        -webkit-flex-basis: 48%;
        -ms-flex-preferred-size: 48%;
        flex-basis: 48%;
        margin-right: 4%;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .kuGridView ul li .klevuImgWrap {
        margin-bottom: 1rem
    }
}

@media (max-width: 45em) and (min-width: 10em) {
    .kuGridView ul li:nth-child(2n) {
        margin-right: 0
    }
}

@media (min-width: 45.0625em) {
    .kuGridView ul li {
        -webkit-flex-basis: 31.6129%;
        -ms-flex-preferred-size: 31.6129%;
        flex-basis: 31.6129%;
        margin-right: 2.58065%
    }

    .kuGridView ul li:nth-child(3n+3) {
        margin-right: 0
    }
}

.kuGridView ul li:hover {
    -webkit-box-shadow: none;
    box-shadow: none;
    border: 1px solid #ccc
}

.kuGridView ul li:hover img {
    -webkit-transform: none;
    transform: none
}

.kuGridView ul li .klevuImgWrap {
    height: auto !important
}

.kuGridView ul li .kuNameDesc {
    margin-top: 0;
    padding-left: 0
}

.kuGridView ul li .kuName a {
    font-size: 16px;
    color: #333;
    letter-spacing: 0;
    line-height: 24px;
    text-transform: none;
    font-weight: 400
}

.kuGridView ul li .kuPrice {
    margin-top: 0;
    padding-left: 0;
    color: #333
}

.kuGridView ul li .kuPrice .kuSalePrice {
    font-family: Oswald, sans-serif;
    font-size: 18px;
    color: #333;
    letter-spacing: 0;
    line-height: 30px;
    font-weight: 400
}

@media (max-width: 32.4375em) {
    .kuGridView ul li {
        margin-right: 0
    }

    .kuGridView ul li:after {
        display: block;
        clear: both;
        content: ""
    }

    .kuGridView ul li .kuName {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .kuGridView ul li .klevuImgWrap {
        display: block;
        -webkit-flex-basis: 23%;
        -ms-flex-preferred-size: 23%;
        flex-basis: 23%;
        width: 23% !important
    }

    .kuGridView ul li .kuNameDesc {
        -webkit-flex-basis: 50%;
        -ms-flex-preferred-size: 50%;
        flex-basis: 50%
    }

    .kuGridView ul li .kuPrice {
        -webkit-flex-basis: 23%;
        -ms-flex-preferred-size: 23%;
        flex-basis: 23%
    }

    .kuGridView ul li .kuPrice .kuSalePrice {
        text-align: right;
        display: block;
        margin-right: 0
    }
}

.kuGridView img {
    margin-bottom: 18px;
    position: static;
    -webkit-transform: none;
    transform: none
}

.kuTotalResultsTab {
    width: auto
}

.kuTotalResultsTab .kuTabs a {
    font-weight: 500;
    color: #28343A;
    display: inline-block;
    position: relative;
    z-index: 0;
    padding: .66667rem 36px .66667rem 20px;
    text-decoration: none;
    cursor: pointer;
    text-transform: uppercase;
    font-size: 16px;
    letter-spacing: 0;
    line-height: 24px;
    margin: 0
}

@media (min-width: 1024px) {
    .kuTotalResultsTab .kuTabs a {
        line-height: 28px;
    }
}

.kuTotalResultsTab .kuTabs a:hover {
    border-bottom: 0
}

.kuTotalResultsTab .kuTabs a:after {
    display: inline-block;
    font-family: icomoon;
    font-size: 6px;
    vertical-align: middle;
    position: absolute;
    right: 0;
    top: 50%;
    padding-right: 20px;
    margin-top: -3px;
    line-height: .33333rem;
    content: "";
    -webkit-transition: -webkit-transform .3s ease .2s;
    transition: -webkit-transform .3s ease .2s;
    -o-transition: transform .3s ease .2s;
    transition: transform .3s ease .2s;
    transition: transform .3s ease .2s, -webkit-transform .3s ease .2s
}

.kuTotalResultsTab .kuTabs a.kuTabSelected {
    background-color: #fff;
    color: #F56438;
    z-index: 1;
}

.kuTotalResultsTab .kuTabs a.kuTabSelected:after {
    content: "\e914";
}

.kuTotalResultsTab .kuTabs a strong {
    float: right;
    position: relative;
    margin-left: 5px;
    font-weight: 500
}

.kuTotalResultsTab .kuTabs a strong:before {
    content: "("
}

.kuTotalResultsTab .kuTabs a strong:after {
    content: ")"
}

.kuResultInfo {
    border: 0;
    background-color: #f5f5f5
}

.kuResultInfo:after {
    display: block;
    clear: both;
    content: ""
}

.kuResultList {
    margin: 0
}

.kuSortby {
    width: auto !important;;
}

.kuSortby label {
    display: none
}

.kuSortby select {
    white-space: nowrap
}

.kuPerPage {
    margin-left: 1.125rem
}

.kuView {
    padding-top: .66667rem;
    padding-bottom: .66667rem
}

.kuPagination {
    margin: 0;
    width: auto;
    font-size: 0;
    border: 1px solid #28343A;
}

.kuPagination a {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    background-color: #fff;
    -webkit-transition: background 460ms cubic-bezier(.7,0,.3,1), border-color 460ms cubic-bezier(.7,0,.3,1);
    -o-transition: background 460ms cubic-bezier(.7,0,.3,1), border-color 460ms cubic-bezier(.7,0,.3,1);
    transition: background 460ms cubic-bezier(.7,0,.3,1), border-color 460ms cubic-bezier(.7,0,.3,1);
    float: left;
    visibility: visible;
    position: relative;
    z-index: 0;
}

.kuPagination a:nth-of-type(1) {
    border-left-width: 0;
}

.kuPagination a:nth-last-of-type(1) {
    border-right-width: 0;
}

.kuPagination .kuCurrent,
.kuPagination a {
    width: 42px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    font-family: Apercu-Bold;
}

.kuPagination .kuCurrent {
    border-left-color: #28343A;
    border-right-color: #28343A;
    position: relative;
    z-index: 1;
}

.kuPagination a {
    font-size: 14px;
}

@media (min-width: 768px) {
    .kuPagination a {
        font-size: 16px;
    }
}

@media (min-width: 1024px) {
    .kuPagination .kuCurrent,
    .kuPagination a {
        width: 50px;
        height: 50px;
        font-size: 18px;
        line-height: 50px;
    }
}

.kuPagination a:not(:last-child) {
    margin-right: -1px
}

.kuPagination a:hover {
    border-color: #28343a;
    z-index: 1;
    color: #28343a;
}

.kuPagination a.kuCurrent {
    border-color: #28343a;
    background-color: #feece7;
    color: #28343a;
}

.kuBottomPagi {
    padding-top: 3.33333rem;
    padding-bottom: 3.33333rem;
    border-top: 0
}

.kuBottomPagi .kuPagination {
    width: auto
}

.kuFilterHead {
    margin-bottom: .66667rem;
    font-weight: 600;
    padding: .66667rem 1.5rem;
    color: #333
}

.kuFilterHead, .kuFilterNames ul li a {
    font-family: Fira Sans, sans-serif;
    font-size: 14px;
    letter-spacing: .88px;
    line-height: 24px
}

.kuFilterNames ul li a {
    text-transform: none;
    color: #636363
}

.kuFilterLabel {
    text-transform: uppercase
}

.ku-search-filter-tag {
    padding: .33333rem .75rem;
    font-family: Fira Sans, sans-serif;
    font-size: 14px;
    color: #333
}

.ku-search-filter-remove {
    padding-left: .75rem;
    position: relative;
    font-size: 0;
    visibility: hidden
}

.ku-search-filter-remove:after {
    display: inline-block;
    font-family: icomoon;
    vertical-align: middle;
    position: absolute;
    visibility: visible;
    right: 0;
    top: 50%;
    margin-top: -1px;
    line-height: .33333rem;
    content: "";
    -webkit-transition: -webkit-transform .3s ease .2s;
    transition: -webkit-transform .3s ease .2s;
    -o-transition: transform .3s ease .2s;
    transition: transform .3s ease .2s;
    transition: transform .3s ease .2s, -webkit-transform .3s ease .2s
}

.kuMulticheck .kuFilterNames ul li.kuSelected span.kuFilterLabel:after, .kuMulticheck .kuHover .kuFilterNames ul li a:hover span.kuFilterLabel:after {
    top: 6px
}

@media only screen and (max-width: 1024px) {
    .kuFilters {
        display: block !important;
        float: none !important;
        width: 100% !important;
        margin-bottom: 20px
    }

    .kuResultList {
        float: none;
        width: 100%;
        margin-left: 0
    }
}

#container-search-shop > input {
    pointer-events: none
}
