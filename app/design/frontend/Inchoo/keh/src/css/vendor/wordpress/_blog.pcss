.page-layout-blog {
    & .pages {
        display: block;
        @mixin marginTrailer 1;

        @media (--medium) {
            @mixin marginTrailer 1, $breakpoint-768px;
        }

        @media (--large) {
            @mixin marginTrailer 1, $breakpoint-1024px;
        }
    }
}

.is-blog {
    & .page-footer {
        & .row--wrap-l {
            @media (--large) {
                @mixin paddingLeader 2, $breakpoint-1024px;
            }
        }
    }
}

.wordpress-homepage-view {
    & .page-title-wrapper,
    & .post-list-description {
        @mixin no-display;
    }
}

.wp-sidebar {
    @media (--large-mw) {
        @mixin marginTrailer 1;
    }
}

.blog-slick-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%,-50%,0);
    text-align: center;
    width: 100%;
    height: 100%;
    z-index: 2;

    @media (--medium) {
        width: 80%;
    }

    @media (--large) {
        width: 42.66667rem;
    }

    & h2 {
        & a {
            color: var(--theme__color__white);
            text-transform: uppercase;
        }
    }
}

.post-date-author--overlay {
    & span {
        text-decoration: underline;
        color: var(--theme__color__white);
        @mixin adjustFontSize 14px, 1;

        @media (--medium) {
            @mixin adjustFontSize 14px, 1, $breakpoint-768px;
        }

        @media (--large) {
            @mixin adjustFontSize 14px, 1, $breakpoint-1024px;
        }

        &.slash {
            text-decoration: none;
            font-size: 1rem;
        }
    }
}

.blog-slick {
    @mixin paddingTrailer 1;

    @media (--medium) {
        @mixin paddingTrailer 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin paddingTrailer 1, $breakpoint-1024px;
    }

    & > .blog-slide:nth-of-type(n + 2) {
        display: none;
    }

    & .slick-dots {
        position: absolute;
        bottom: calc((40 / 18) * 1rem);
        left: 50%;
        transform: translateX(-50%);

        & li {
            background-color: var(--theme__color__grayscale__5);

            &.slick-active {
                background-color: var(--theme__color__white);
            }
        }
    }

    & .slick-arrow {
        color: var(--theme__color__grayscale__5);
        background: transparent;
        border: none;

        @media (--medium-mw) {
            top: 94%
        }

        @media (--extra-large) {
            &.slick-prev {
                left: 16%;
            }

            &.slick-next {
                right: 16%;
            }
        }
    }
}

.blog-slide {
    height: 46rem;
    position: relative;
}

.blog-slide-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-home {
    & .post-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        & .item {
            flex-basis: 100%;

            @media (--medium) {
                flex-basis: 48.35165%;
                display: flex;
                flex-direction: column;
                justify-content: stretch;
            }

            &.featured {
                @media (--medium) {
                    flex-basis: 100%;
                }

                position: relative;
                @mixin ui-default-transition opacity;

                & .blog-img-wrapper {
                    & img {
                        width: 100%;
                        display: block;
                        height: auto;
                    }
                }

                & .post-title {
                    @media (--medium-mw) {
                        margin-bottom: 0;
                    }

                    & a {
                        display: block;
                        text-transform: uppercase;
                        color: var(--theme__color__white);
                        line-height: 1.2;
                    }
                }

                & .post-categories {
                    @media (--medium) {
                        @mixin marginTrailer 1, $breakpoint-768px;
                    }

                    @media (--large) {
                        @mixin marginTrailer 1, $breakpoint-1024px;
                    }
                }

                & .featured-details-wrapper {
                    position: relative;
                    @mixin marginTrailer 0.5;
                    @mixin  ui-default-transition opacity;

                    @media (--medium) {
                        @mixin marginTrailer 0.5, $breakpoint-768px;
                    }

                    @media (--large) {
                        @mixin marginTrailer 0.5, $breakpoint-1024px;
                    }

                    &:hover {
                        opacity: .9;
                    }
                }

                & .img-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,.5);
                }

                & .featured-details {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 90%;
                    text-align: center;
                    transform: translate3d(-50%,-50%,0);
                    background: transparent;

                    @media (--medium) {
                        top: 70%;
                        left: 0;
                        width: 85%;
                        text-align: left;
                        padding: 0 2.8125rem;
                        transform: translate3d(0,-70%,0);
                    }
                }

                & .post-categories-link {
                    font-weight: 500;
                    letter-spacing: calc((1 / 18) * 1rem);
                    color: var(--theme__color__white);
                    display: inline-block;
                    border: calc((1 / 18) * 1rem) solid var(--theme__color__white);
                    padding: calc((2 / 18) * 1rem) calc((16 / 18) * 1rem);
                    text-transform: uppercase;

                    @media (--medium-mw) {
                        margin-bottom: calc((6 / 18) * 1rem);
                    }
                }
            }
        }
    }
}

.post-list {
    & .item {
        margin-bottom: calc((18 / 18) * 1rem);
    }

    & .item:not(.featured) {
        & .post-list-item > a {
            overflow: hidden;
            display: block;
            @mixin marginTrailer 0.5;

            @media (--medium) {
                @mixin marginTrailer 0.5, $breakpoint-768px;
            }

            @media (--large) {
                @mixin marginTrailer 0.5, $breakpoint-1024px;
            }

            & img {
                display: block;
                height: auto;
                transition: opacity .2s ease-in, transform .2s ease-in;
                height: auto;

                &:hover {
                    opacity: 0.85;
                    transform: scale(1.08);
                }
            }
        }
    }
}

.post-bottom-wrapper {
    display: flex;
    justify-content: space-between;
    border-top: calc((1 / 18) * 1rem) solid var(--theme__color__grayscale__5);
    padding-top: calc((12 / 18) * 1rem);

    & .social-links ul,
    & .post-count ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    & .post-count li {
        & a span {
            font-size: calc((14 / 18) * 1rem);
            color: var(--theme__color__grayscale__2);
            transition: color .2s ease-in;

            &:hover {
                color: var(--theme__color__grayscale__4);
            }

            &.post-count-number {
                display: inline-block;
                margin-left: calc((5 / 18) * 1rem);
                font-size: calc((12 / 18) * 1rem)
            }
        }
    }

    & .social-links li {
        display: inline-block;
        padding-left: calc((24 / 18) * 1rem);

        @media (--medium) {
            padding-left: calc((6 / 18) * 1rem);
        }

        & a span {
            font-size: calc((24 / 18) * 1rem);
            color: var(--theme__color__grayscale__2);
            transition: color .2s ease-in;

            &:hover {
                color: var(--theme__color__grayscale__4);
            }
        }
    }
}

.post-bottom-wrapper--single-view {
    @mixin marginTrailer 1;

    @media (--medium) {
        @mixin marginTrailer 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 1.5, $breakpoint-1024px;
    }
}

.blog-author-img img {
    width: calc((150 / 18) * 1rem);
    height: calc((150 / 18) * 1rem);
    border-radius: 50%;

    @media (max-width: 63.9999rem) {
        width: calc((78 / 18) * 1rem);
        height: calc((78 / 18) * 1rem);
        margin-right: calc((15 / 18) * 1rem);
    }
}

.blog-author-outer-wrapper {
    margin-bottom: calc((18 / 18) * 1rem);
    padding-bottom: calc((18 / 18) * 1rem);
    border-bottom: calc((1 / 18) * 1rem) solid var(--theme__color__grayscale__5);

    @media (max-width: 63.9999rem) {
        display: flex;
        align-items: center;
    }
}

.blog-author-title {
    padding-bottom: 0;

    & span {
        font-family: var(--font__family__heading);
        font-weight: 400;
        font-size: calc((18 / 18) * 1rem);
        color: var(--theme__color__grayscale__3);
        line-height: calc((30 / 18) * 1rem);
        text-transform: uppercase;
    }

    @media (max-width: 63.9999rem) {
        margin-bottom: calc((12 / 18) * 1rem);
    }
}

.blog-post-categories {
    font-weight: 500;
    letter-spacing: calc((1 / 18) * 1rem);
    @mixin marginTrailer 0.5;
    display: flex;
    flex-wrap: wrap;

    @media (--medium) {
        @mixin marginTrailer 0.5, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 0.5, $breakpoint-1024px;
    }

    & a {
        border: calc((1 / 18) * 1rem) solid var(--theme__color__grayscale__3);
        letter-spacing: calc((1 / 18) * 1rem);
        color: var(--theme__color__grayscale__1);
        padding: calc((2 / 18) * 1rem) calc((15 / 18) * 1rem);
        text-transform: uppercase;
        margin-right: pxtorem(12px,14);
        @mixin marginTrailer 0.25;

        @media (--medium) {
            margin-right: pxtorem(12px,16);
            @mixin marginTrailer 0.25, $breakpoint-768px;
        }

        @media (--large) {
            margin-right: pxtorem(12px,18);
            @mixin marginTrailer 0.25, $breakpoint-1024px;
        }

        &:last-child {
            margin-right: 0;
        }
    }

    & img {
        display: block;
    }
}

.blog-post-tags {
    margin: calc((12 / 18) * 1rem) 0;

    & a {
        background: var(--theme__color__grayscale__6);
        text-transform: uppercase;
        font-weight: bold;
        font-size: calc((14 / 18) * 1rem);
        color: var(--theme__color__grayscale__1);
        letter-spacing: calc((0.88px / 18) * 1rem);
        line-height: calc((24 / 18) * 1rem);
        padding: calc((4 / 18) * 1rem) calc((17 / 18) * 1rem);

        @media (max-width: 20rem) {
            margin-bottom: calc((6 / 18) * 1rem);
        }
    }
}

.blog-post-skips {
    display: flex;
    justify-content: space-between;
    @mixin marginTrailer 1;

    @media (--medium) {
        @mixin marginTrailer 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 1.5, $breakpoint-1024px;
    }

    & a span {
        display: block;
        text-transform: uppercase;
        font-family: var(--font__family__heading);
        font-weight: 500;

        & i:before {
            font-size: calc((12 / 18) * 1rem);
            margin-right: calc((6 / 18) * 1rem);

            @media (min-width: 48rem) {
                font-size: calc((14 / 18) * 1rem);
            }
        }
    }

    & a:first-child {
        & span {
            text-align: left;
        }
    }

    & a:last-child {
        & span {
            text-align: right;
        }

        & span i:before {
            margin-left: calc((6 / 18) * 1rem);
        }
    }

    & a p {
        @media (--medium-mw) {
            display: none;
        }
    }
}

.post-entry-content {
    @mixin marginTrailer 0.5;

    @media (--medium) {
        @mixin marginTrailer 0.5, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 0.5, $breakpoint-1024px;
    }
}

.post-read-more {
    display: inline-block;
    font-family: var(--font__family__heading);
    text-transform: uppercase;
    position: relative;

    @media (--medium) {
        font-size: 1rem;
    }

    & i {
        margin-left: .66667rem;
    }
}

.featured-details {
    & .details {
        @mixin adjustFontSize 14px, 0.75;
        @mixin marginTrailer 0.5;

        @media (--medium) {
            @mixin adjustFontSize 14px, 0.75, $breakpoint-768px;
            @mixin marginTrailer 0.5, $breakpoint-768px;
        }

        @media (--large) {
            @mixin adjustFontSize 14px, 0.75, $breakpoint-1024px;
            @mixin marginTrailer 0.5, $breakpoint-1024px;
        }
    }

    & h4 {
        & a {
            color: var(--theme__color__grayscale__1);
        }
    }
}

.featured-img-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.44);
    z-index: 1;
}

.blog-trending-wrapper {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;

    @media (--medium) {
        flex-direction: row;
        justify-content: center;
    }
}

.blog-trending {
    @media (--medium) {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    & strong {
        line-height: 1.3;
    }
}

.blog-trending-item {
    position: relative;
    @mixin ui-default-transition opacity;

    &:hover {
        opacity: 0.9;
    }

    @media (--large) {
        flex: 1;
        max-width: 100%;
        margin-bottom: calc((24 / 18) * 1rem);
        margin-left: 0;
        margin-right: 0;
    }

    & img {
        display: block;
    }
}

.blog-trending-details {
    position: absolute;
    display: flex;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%,-50%,0);
    padding: calc((12 / 18 ) * 1rem);
    background: rgba(0,0,0,0.4);
    width: 93%;
    height: 93%
}

.trending-learn-more {
    color: var(--theme__color__white);
    text-align: center;
    margin: auto;

    & span {
        @mixin adjustFontSize 14px, 1;

        @media (--medium) {
            @mixin adjustFontSize 14px, 1, $breakpoint-768px;
        }

        @media (--large) {
            @mixin adjustFontSize 14px, 1, $breakpoint-1024px;
        }

        & i {
            margin: calc((6 / 18) * 1rem) 0 0 calc((6 / 18) * 1rem);
        }
    }
}

.blog-related-wrapper {
    display: flex;
    background-color: var(--theme__color__grayscale__6);
    padding: calc((24 / 18) * 1rem);

    @media (max-width: 35.999999rem) {
        flex-direction: column;
        align-items: center;
    }
}

.blog-related-item {
    flex: 0 1 33.3333%;

    & h5 {
        padding-bottom: 0;
    }

    & .blog-related-date {
        @media (min-width: 64rem) {
            font-size: calc((18 / 18) * 1rem);
        }
    }

    @media (max-width: 35.999999rem) {
        margin-bottom: calc((24 / 18) * 1rem);
    }

    @media (min-width: 36rem) {
        margin: 0 calc((12 / 18) * 1rem);

        &:first-child {
            margin-left: 0;
        }

        &:last-child {
            margin-right: 0;
        }
    }
}

.post-view {
    & img {
        height: auto;
        display: block;
    }
}

.featured-image--blog-view {
    position: relative;
    height: 0;
    padding-bottom: calc(480 / 640 * 100%);
    overflow: hidden;
    @mixin marginTrailer 1;

    @media (--medium) {
        @mixin marginTrailer 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 1, $breakpoint-1024px;
    }

    & img {
        width: 100%;
        display: block;
        height: auto;
    }
}

.block-blog-subscribe {
    position: relative;
    padding: 0 pxtorem(12px,14);
    @mixin rhythm 1, 1, 0, 0;
    height: calc((645 / 18) * 1rem);
    overflow: hidden;

    @media (max-width: 64rem) {
        margin: calc((48 / 18) * 1rem) calc((-20 / 18) * 1rem);
        height: calc((300 / 18) * 1rem);
        background: var(--theme__color__grayscale__6);
    }

    @media (max-width: 35.5rem) {
        margin: calc((48 / 18) * 1rem) calc((-10 / 18) * 1rem);
    }


    @media (--medium) {
        padding: 0 pxtorem(12px,16);
        @mixin rhythm 1, 1, 0, 0, $breakpoint-768px;
    }

    @media (--large) {
        padding: 0 pxtorem(12px,18);
        @mixin rhythm 1, 1, 0, 1, $breakpoint-1024px;
    }

    & .block-blog-title,
    & .block-content {
        position: relative;
        z-index: 1;
    }

    & .action.submit-blog-newsletter {
        max-width: none;
        width: 100%;
    }
}

.blog-newsletter-img {
    position: absolute;
    top: 0;
    left: 0;
    width: auto;
    max-width: none;

    @media (--large) {
        width: 330px;
        height: 645px;
    }
}
