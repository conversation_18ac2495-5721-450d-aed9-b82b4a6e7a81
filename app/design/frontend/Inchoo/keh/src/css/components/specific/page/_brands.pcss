.brands-letter-title {
    @mixin trailingBorder 1px, 0.5, solid;
    @mixin marginTrailer 1;
    border-color: var(--theme__color__grayscale__5);

    @media (--medium) {
        @mixin trailingBorder 1px, 0.5, solid, $breakpoint-768px;
        @mixin marginTrailer 1, $breakpoint-768px;
        border-color: var(--theme__color__grayscale__5);
    }

    @media (--large) {
        @mixin trailingBorder 1px, 0.5, solid, $breakpoint-1024px;
        @mixin marginTrailer 1, $breakpoint-1024px;
        border-color: var(--theme__color__grayscale__5);
    }
}

.brands-col-row {
    display: flex;
    flex-wrap: wrap;
}

.brands-col-item {
    @mixin marginTrailer 1;

    @media (--medium) {
        lost-column: 1/4 0 0;
        @mixin marginTrailer 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 1, $breakpoint-1024px;
    }
}