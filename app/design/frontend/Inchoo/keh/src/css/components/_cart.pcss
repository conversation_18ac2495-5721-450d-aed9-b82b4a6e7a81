.checkout-cart-index {
    & h1.page-title {
        @mixin adjustFontSize 28px, 1.5;
        color: var(--k2__color--primary--2);

        @media (--medium) {
            @mixin adjustFontSize 32px, 1.25, $breakpoint-768px;
        }

        @media (--large) {
            @mixin adjustFontSize 45px, 2, $breakpoint-1024px;
        }
    }

    & .cart.items.table .update-qty { display: none; }
}

.proceed-checkout-wrapper {
    @mixin marginTrailer 1;
}

.cart-container {
    @mixin flex column, flex-start, flex-start, wrap;
    @mixin marginTrailer 1;

    @media (--medium) {
        @mixin marginTrailer 1, $breakpoint-768px;
    }

    & .form-cart {
        lost-column: 1/1;
    }

    & .cart-summary {
        lost-column: 1/1;
    }

    & .checkout-methods-items {
        width: 100%;
        margin-top: auto;
    }
}

.opc-block-summary {
    & [data-role^="title"] {
        position: relative;
        display: inline-flex;
        align-items: center;
        @mixin section-title;
        @mixin adjustFontSize 14px, 1;
        @mixin trailingBorder 1px, 0, solid;
        @mixin marginTrailer 0.5;
        border-color: var(--theme__color__grayscale__5);
        outline: 0;

        @media (--medium) {
            @mixin adjustFontSize 14px, 1, $breakpoint-768px;
            @mixin trailingBorder 1px, 0, solid, $breakpoint-768px;
            @mixin marginTrailer 0.5, $breakpoint-768px;
            border-color: var(--theme__color__grayscale__5);
        }

        @media (--large) {
            @mixin adjustFontSize 14px, 1, $breakpoint-1024px;
            @mixin trailingBorder 1px, 0, solid, $breakpoint-1024px;
            @mixin marginTrailer 0.5, $breakpoint-1024px;
            border-color: var(--theme__color__grayscale__5);
        }

        &:after {
            font-family: "icomoon";
            margin: 0 8px;
            content: var(--icon__chevron-down);
            @mixin ui-default-transition transform;
            font-size: pxtorem(5px,14);
            font-weight: 400;

            @media (--medium) {
                font-size: pxtorem(5px,16);
            }

            @media (--large) {
                font-size: pxtorem(5px,18);
            }
        }

        &.active {
            color: var(--theme__color__grayscale__1);

            &::after {
                transform: rotate(-180deg);
            }
        }

        &[aria-selected^="true"] {
            color: var(--theme__color__grayscale__1);

            &::after {
                transform: rotate(-180deg);
            }
        }

        & strong {
            font-weight: 400;
        }
    }

    & [aria-hidden^="false"] {
        @mixin marginTrailer 1;

        @media (--medium) {
            @mixin marginTrailer 1, $breakpoint-768px;
        }

        @media (--large) {
            @mixin marginTrailer 1, $breakpoint-1024px;
        }
    }

    @media (--large) {
        @mixin interactive-sidebar var(--theme__color__primary__lighter), var(--theme__color__primary);
    }

    & .minicart-items-wrapper {
        & .product-item-wrapper {
            padding: 0.6rem !important;
        }
    }
}

.cart-summary {
    min-height: 436px;

    & .block {
        & .content {
            position: relative;
        }
    }

    @media (--medium) {
        min-height: 411px;
    }

    @media (--large) {
        min-height: 527px;
    }

    & .cart-totals {
        min-height: 144px;

        &:empty {
            background-color: red;
        }

        @media (--medium) {
            min-height: 162px;
        }
    }
}

.cart-totals {
    position: relative;
}

.cart.table-wrapper {
    @mixin marginTrailer 0;

    & .actions-toolbar {
        text-align: left;
        display: block;
    }

    & .action-edit {
        margin-right: 0.6rem;

        &::before {
            font-family: 'icomoon';
        }
    }

    & .action-delete {
        & span {
            display: inline-block;
        }

        & .icon {
            font-size: 1rem;
            color: var(--k2__color--primary--2);
        }

        &::before {
            display: none;
        }

        @media (--medium) {
            text-align: right;
        }
    }

    & .field {
        margin-bottom: 0;
    }

    & .cart.item {
        & .col.price,
        & .col.qty,
        & .col.subtotal,
        & .col.msrp {
            @mixin adjustFontSize 14px, 1;

            @media (--medium) {
                @mixin adjustFontSize 16px, 1, $breakpoint-768px;
            }

            @media (--large) {
                @mixin adjustFontSize 18px, 1, $breakpoint-1024px;
            }
        }

        & .col.qty {
            @media (--medium) {
                & .control.qty {
                    display: flex;
                    align-items: center;
                }
            }
        }

        & .col.price,
        & .col.subtotal {
            & .cart-price {
                & .original-price {
                    @mixin small;
                    text-decoration: line-through;
                    color: var(--k2__color__gray--3);
                }
            }
        }
    }
}

.cart.main.actions {
    text-align: left;
}

.primary--coupon {
    @mixin marginTrailer 0.5;

    @media (--medium) {
        @mixin marginTrailer 0.5, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 0.5, $breakpoint-1024px;
    }
}

.items.methods {
    & .item-options {
        & .label {
            color: var(--theme__color__grayscale__3);
        }
    }
}

.grand.totals {
    border-top: 0.1rem solid var(--theme__color__primary__light);

    & td,
    & th {
        @mixin rhythm 1, 1, 0, 0;

        @media (--medium) {
            @mixin rhythm 1, 1, 0, 0, $breakpoint-768px;
        }

        @media (--large) {
            @mixin rhythm 1, 1, 0, 0, $breakpoint-1024px;
        }
    }

    &.excl {
        display: none;
    }
}


.checkout-methods-items {
    & .item {
        text-align: center;

        &:not(:last-child) {
            @mixin marginTrailer 0.5;
        }
    }
}

.data.table.totals,
.table-totals {
    & caption {
        display: none;
    }

    & tbody {
        & th {
            text-align: left;
            padding-right: 1rem;
        }

        & td {
            text-align: right;
        }
    }
}

.cart.items.table {
    & .item-actions {
        background: transparent;
    }

    & thead {
        & tr {
            text-align: left;
            border-bottom: none;
        }
    }

    & caption {
        display: none;
    }

    & tbody {
        vertical-align: middle;
    }

    & .product-item-photo {
        display: block;
        flex: 0 0 160px;
    }

    & .product-item-details {
        text-align: left;
    }

    & .product-item-name {
        display: block;
        text-transform: capitalize;
        @mixin adjustFontSize 14px, 1;
        @mixin marginTrailer 0.5;
        word-break: break-word;

        @media (--medium) {
            @mixin adjustFontSize 14px, 1, $breakpoint-768px;
            @mixin marginTrailer 0.5, $breakpoint-768px;
        }

        @media (--large) {
            @mixin marginTrailer 0.5, $breakpoint-1024px;
            @mixin adjustFontSize 18px, 1, $breakpoint-1024px;
        }
    }

    & .product-sku {
        display: block;
    }

    & .qty-wrapper {
        display: flex;
    }
}

.cart-item-data-wrapper {
    & .product-image-container {
        display: block;
        margin: 0 auto;
    }

    & .product-image-wrapper {
        display: block;
        height: 0;
        overflow: hidden;
    }

    @media (--medium) {
        display: flex;
        align-items: flex-start;

        & .product-image-container {
            width: auto !important;
        }
    }
}

.checkout.methods {
    & .action {
        width: 100%;
        max-width: none;
    }
}

.cart.main.actions {
    display: flex;

    @media (--medium-mw) {
        flex-direction: column;
    }

    @media (--medium) {
        justify-content: flex-end;
        @mixin marginLeader 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginLeader 1, $breakpoint-1024px;
    }

    & > .action {
        @mixin marginTrailer 0.5;

        @media (--medium-mw) {
            max-width: none;
        }

        @media (--medium) {
            @mixin marginTrailer 0.5, $breakpoint-768px;

            &:not(:last-child) {
                margin-right: pxtorem(16, 16);
            }
        }

        @media (--large) {
            @mixin marginTrailer 0.5, $breakpoint-1024px;

            &:not(:last-child) {
                margin-right: pxtorem(16, 18);
            }
        }
    }
}

@media (--medium-mw) {
    .cart.items.table {
        & thead,
        & caption {
            display: none;
        }
    }

    .cart.table-wrapper {
        & .cart {
            & > .item {
                & .item-info {
                    position: relative;
                    width: 100%;
                    display: grid !important;
                    grid-template-columns: repeat(2, 1fr);
                    grid-template-areas: 'product-name product-name'
                    'product-price product-price'
                    'product-qty product-actions'
                    'product-subtotal product-subtotal';
                }
            }
        }

        & .col.item {
            grid-area: product-name;
        }

        & .col.price {
            grid-area: product-price;
        }

        & .col.qty {
            grid-area: product-qty;
        }

        & .col.actions {
            grid-area: product-actions;
            align-self: center;
        }

        & .col.subtotal {
            margin-top: pxtorem(7, 14);
            grid-area: product-subtotal;
        }

        & .item {
            & .col.item,
            & .col.price,
            & .col.qty,
            & .col.msrp {
                display: block;
            }

            & .col.subtotal {
                display: none;
            }

            & .col.price,
            & .col.qty,
            & .col.msrp {
                text-align: left;
                font-weight: 600;

                &::before {
                    content: attr(data-th) ":";
                    display: inline-block;
                    margin-right: pxtorem(12px,14);
                    font-weight: normal;
                }
            }

            & .col.item {
                overflow: hidden;
            }

            & .col.actions {

            }
        }

        & .action-delete {
            & span {
                display: inline-block;
            }

            & .icon {
                font-size: 1rem;
            }

            &::before {
                display: inline-block;
                content: attr(data-th) ":";
                font-size: 1rem;
                margin-right: pxtorem(8px,14);
            }
        }
    }

    .cart-summary {
        @mixin section-no-gutters var(--gutter__width);
        top: auto !important;
        display: flex;
        flex-direction: column;
    }
}

.cart-warranty-offers:not(:empty) {
    @media (--medium) and (--large-mw) {
        @mixin marginTrailer 0.5, $breakpoint-768px;
    }

    @media (--large) {
        @mixin marginTrailer 0.5, $breakpoint-1024px;
    }

    @media (--medium) and (--k3-tablet-mw) {
        width: 130px;
    }

    @media (--k3-desktop) {
        width: 183px;
    }
}

@media (--medium-mw) {
    .cart.table-wrapper {
        & .action-edit {
            @mixin marginTrailer 0.5;
        }
    }

    .cart.items.table {
        & .qty-wrapper {
            flex: 1 1 50%;
            margin-right: pxtorem(8, 14);
        }

        & .field.qty {
            & label {
                display: none;
            }
        }

        & .control.qty {
            display: flex;
            justify-content: left;
        }

        & .update-qty {
            flex: 1 1 50%;
            margin-left: pxtorem(7px,14);
        }

        & .col.item,
        & .col.price,
        & .col.subtotal {
            @mixin marginTrailer 0.5;
        }
    }
}

@media (--medium) {
    .cart-container {
        flex-direction: row;
        @mixin marginTrailer 1, $breakpoint-768px;

        & .cart-summary {
            display: flex;
            flex-wrap: wrap;
        }

        & .cart-section {
            lost-column: 6/12 2;
        }

        & .cart-totals {
            flex: 0 0 100%;
        }
    }

    .cart.items.table {
        & .item-info {
            & td:first-child {
                padding-left: 0;
            }
        }

        & .control.qty {
            & .qty {
                margin-right: 16px;
            }
        }

        & .qty {
            & .label {
                display: none;
            }
        }
    }

    .cart.main.actions {
        text-align: right;
        @mixin marginTrailer 1, $breakpoint-768px;
    }

    .cart.table-wrapper {
        & .actions-toolbar {
            margin-bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

@media (--large) {
    .cart-container {
        @mixin marginTrailer 1, $breakpoint-1024px;

        & .form-cart {
            lost-column: 9/12;
        }

        & .cart-summary {
            lost-column: 3/12 1 32px;
        }

        & .cart-section {
            display: block;
            width: 100%;
            lost-column: 1/1;
        }

        & .cart-section-coupon {
            order: 1;
        }

        & .cart-section-giftcard {
            order: 2;
        }

        & .cart-section-paypal,
        & .cart-section-shipping {
            order: 3;
        }

        & .cart-totals {
            order: 4;
        }

        & .checkout-methods-items {
            order: 5;
        }

        & .table-wrapper {
            overflow-x: unset;
        }
    }

    .cart.main.actions {
        @mixin marginTrailer 1, $breakpoint-1024px;
    }
}

@media (--large-mw) {
    .braintree-paypal-review {
        & .cart-summary {
            min-height: auto;
        }
    }

    .cart-section-paypal {
        margin-top: 1.4rem;
        max-width: none !important;
        width: 100% !important;
        flex: 1 1 100% !important;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(342px, 1fr));
        grid-gap: 0 24px;
    }
}


.sidebar__block__title {
    font-weight: normal;
    @mixin adjustFontSize 18px, 0.875;

    @media (--medium) {
        @mixin adjustFontSize 18px, 0.7, $breakpoint-768px;
    }

    @media (--large) {
        @mixin adjustFontSize 22px, 0.9, $breakpoint-1024px;
    }
}

.checkout-cart-index {
    & .table.totals {
        & th,
        & td {
            @mixin adjustFontSize 16px, 1;

            @media (--medium) {
                @mixin adjustFontSize 16px, 0.8, $breakpoint-768px;
            }

            @media (--large) {
                @mixin adjustFontSize 14px, 0.8, $breakpoint-1024px;
            }
        }

        & th {
            padding-right: pxtorem(8, 14);

            @media (--medium) {
                padding-right: pxtorem(8, 16);
            }

            @media (--large) {
                padding-right: pxtorem(8, 18);
            }
        }
    }
}

.cart-empty > p > a {
    color: var(--k2__color--primary--2)
}

.paypal-email {
    word-wrap: break-word;
}
