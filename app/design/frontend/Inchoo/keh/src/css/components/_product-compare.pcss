.compare.drawer {
    & .compare.no-display {
        @mixin no-display;
    }
}

.catalog-product_compare-index {
    & .print {
        float: right;
        clear: both;
        @mixin rhythm 1, 1, 0, 0;

        @media (--medium) {
            @mixin rhythm 1, 1, 0, 0, $breakpoint-768px;
        }

        @media (--large) {
            @mixin rhythm 1, 1, 0, 0, $breakpoint-1024px;
        }
    }

    & .data-table tr td:last-of-type,
    & .data-table tr th:last-of-type,
    & .data.table tr td:last-of-type,
    & .data.table tr th:last-of-type {
        text-align: right;
    }
}

.table.table-comparison:not(.additional-attributes) {
    table-layout: fixed;

    @media (--medium-mw) {
        & thead {
            display: table-header-group;
        }

        & tbody {
            display: table-row-group;
        }

        & tr {
            display: table-row;
        }

        & th {
            display: table-cell;
        }

        & td {
            display: table-cell;
        }
    }

    & thead {
        & th {
            background: transparent;
        }

        & tr {
            border: none;
        }
    }

    & tbody {
        & tr {
            border: none !important;

            &:nth-child(even) {
                background: transparent;
            }
        }
    }

    & caption {
        display: none;
    }

    & td {
        &:last-child {
            border-right: pxtorem(1px) solid var(--theme__color__grayscale__5);
        }
    }

    & .cell {
        width: 18em;
        padding: pxtorem(16, 14);
        @mixin rhythm 1, 1, 0, 0;
        text-align: left;

        &:first-child {
            @media (--medium-mw) {
                width: 6rem;
            }
        }

        @media (--medium) {
            padding: pxtorem(16, 16);
            @mixin rhythm 1, 1, 0, 0, $breakpoint-768px;
        }

        @media (--large) {
            padding: pxtorem(16, 18);
            @mixin rhythm 1, 1, 0, 0, $breakpoint-1024px;
        }

        &.label {
            border-right: pxtorem(1, 14) solid var(--theme__color__grayscale__5);

            @media (--medium) {
                border-width: pxtorem(1, 16);
            }

            @media (--large) {
                border-width: pxtorem(1, 18);
            }

            & .attribute.label,
            & > span {
                display: block;
                width: 100%;
                font-family: var(--font__family__heading);
                font-weight: 400;
                text-transform: uppercase;
                word-wrap: break-word;
                text-align: left;
                color: var(--theme__color__grayscale__1);

            }

            &.remove {
                & span {
                    display: none;
                }
            }
        }

        &.product {
            &.info,
            &.label {
                @mixin trailingBorder 1px, 1, solid;
                border-color: var(--theme__color__grayscale__5);

                @media (--medium) {
                    @mixin trailingBorder 1px, 1, solid, $breakpoint-768px;
                    border-color: var(--theme__color__grayscale__5);
                }

                @media (--large) {
                    @mixin trailingBorder 1px, 1, solid, $breakpoint-1024px;
                    border-color: var(--theme__color__grayscale__5);
                }
            }

            &.info {
                text-align: center;
            }
        }

        &.remove {
            text-align: right;
            padding-top: 0;
            padding-bottom: 0;
        }
    }

    & .action.delete {
        @media (--medium-mw) {
            @mixin adjustFontSize 18px, 1;
        }

        & span {
            display: none;
        }

        &:before {
            font-family: "icomoon";
            content: "\e934";
        }
    }

    & .product-image-container {
        display: block;
        margin: 0 auto;
    }

    & .action.tocart {
        margin: 0 auto;
        max-width: unset;
    }

    & .product-item-name {
        min-height: pxtorem(100, 14);

        @media (--medium) {
            min-height: pxtorem(100, 16);
        }

        @media (--large) {
            min-height: pxtorem(100, 18);
        }
    }

    & .product-item-photo {
        display: block;
        @mixin marginTrailer 1;

        @media (--medium) {
            @mixin marginTrailer 1, $breakpoint-768px;
        }

        @media (--large) {
            @mixin marginTrailer 1, $breakpoint-1024px;
        }

        & span {
            display: block;
        }
    }

    & .product-image-wrapper {
        height: 0;
    }

    & .product-item-name {
        & a {
            text-decoration: none;
        }
    }

    & .price-box {
        font-family: var(--font__family__heading);
        color: var(--theme__color__grayscale__1);
        font-weight: 400;
        text-align: left;

        & p {
            @mixin adjustFontSize 18px, 1;

            @media (--mobile-landscape-mw) {
                margin-bottom: 0;
            }

            @media (--medium) {
                @mixin adjustFontSize 18px, 1, $breakpoint-768px;
            }

            @media (--large) {
                @mixin adjustFontSize 18px, 1, $breakpoint-1024px;
            }
        }
    }

    & .price-box,
    & .actions-primary {
        @mixin marginTrailer 1;

        @media (--medium) {
            @mixin marginTrailer 1, $breakpoint-768px;
        }

        @media (--large) {
            @mixin marginTrailer 1, $breakpoint-1024px;
        }
    }

    & .actions-primary {
        display: flex;
        justify-content: center;
    }

    & .nornal-price,
    & .old-price {
        display: block;
    }

    & .product-reviews-summary {
        display: none;
    }
}

.block-search--compare {
    display: flex;
    flex-direction: column;
    background: var(--theme__color__grayscale__6);
    border: pxtorem(1, 14) solid var(--theme__color__grayscale__5);
    padding: var(--container__gutter__s);

    @media (--medium) {
        padding: var(--container__gutter__m);
        border-width: pxtorem(1, 16);
    }

    @media (--medium) {
        padding: var(--container__gutter__l);
        border-width: pxtorem(1, 18);
    }
}

.block-content--compare {
    & .form {
        @media (--medium) {
            display: flex;
            max-width: 70%;
            margin: 0 auto;
        }

        & .search {
            @media (--medium) {
                width: 70%;
            }
        }

        & .actions {
            @media (--medium) {
                width: 30%;
            }
        }

        & .search-products {
            @mixin button;
            @mixin button-aligned--with-input;
            @mixin button-primary;

            @media (--medium) {
                width: 100%;
                max-width: none;
                height: pxtorem(47, 16);
            }

            @media (--large) {
                width: 100%;
                max-width: none;
                height: pxtorem(47, 18);
            }
        }
    }
}

.search-autocomplete {
    width: 100% !important;
    height: 30.2rem;
    overflow-x: hidden;
    overflow-y: auto;
    background: var(--theme__color__white);
    box-shadow: 0 0 10px 1px rgba(0, 0, 0, .10);
    display: none;

    & li {
        @mixin flex row, center, flex-start, nowrap;
        padding: var(--container__gutter__s);
        @mixin rhythm 0.5, 0.5, 0, 0;
        border-bottom: pxtorem(1, 14) solid var(--theme__color__grayscale__5);
        cursor: pointer;

        &:last-child {
            border-bottom: none;
        }

        & .amount {
            margin-left: auto;
            font-weight: 700;
        }

        & img {
            display: block;
            max-width: pxtorem(100, 14);
            margin-right: pxtorem(12, 14);

            @media (--medium) {
                max-width: pxtorem(100, 16);
                margin-right: pxtorem(12, 16);
            }

            @media (--large) {
                max-width: pxtorem(100, 18);
                margin-right: pxtorem(12, 18);
            }
        }

        @media (--medium) {
            padding: var(--container__gutter__m);
            @mixin rhythm 0.5, 0.5, 0, 0, $breakpoint-768px;
            border-width: pxtorem(1, 16);
        }

        @media (--large) {
            padding: var(--container__gutter__l);
            @mixin rhythm 0.5, 0.5, 0, 0, $breakpoint-1024px;
            border-width: pxtorem(1, 18);
        }
    }
}

.data.table.table-comparison:not(.additional-attributes) {
    & tr {
        vertical-align: top;
    }

    & .cell.label.remove {
        height: 0;
    }

    & thead {
        @media (--medium-mw) {
            display: table-header-group !important;
        }
    }
}

#product-compare-container {
    @mixin paddingTrailer 1;

    @media (--medium) {
        @mixin paddingTrailer 1, $breakpoint-768px;
    }

    @media (--large) {
        @mixin paddingTrailer 1, $breakpoint-1024px;
    }
}
