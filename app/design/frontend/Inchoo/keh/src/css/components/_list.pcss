ul {
    margin-top: 0;
}

.links-decorated {
    & li {
        padding-right: 0;
        padding-left: 0;

        &::after {
            display: inline-block;
            position: relative;
            top: -1px;
            padding-right: pxtorem(10px, 14);
            padding-left: pxtorem(10px, 14);
            font-size: pxtorem(14px, 14);
            line-height: 1em;
            content: '\2022';

            @media (--medium) {
                padding-right: pxtorem(10px, 16);
                padding-left: pxtorem(10px, 16);
                font-size: pxtorem(16px, 16);
                top: 0;
            }

            @media (--large) {
                padding-right: pxtorem(10px, 18);
                padding-left: pxtorem(10px, 18);
                font-size: pxtorem(24px, 18);
                top: 3px;
            }
        }

        &:last-child {
            &::after {
                display: none;
            }
        }
    }
}

.tooltip-list {
    font-family: var(--font__family__heading);
    font-weight: 500;
    text-transform: uppercase;
    
    & a {
        display: block;
        color: var(--primary__color);
        padding: pxtorem(6px, 14) pxtorem(18px, 14);
        @mixin adjustFontSize 14px, 1;
        @mixin adjustLineHeight 0.75;
        @mixin ui-default-transition background-color;

        @media (--medium) {
            padding: pxtorem(6px, 16) pxtorem(24px, 16);
            @mixin adjustFontSize 16px, 1, $breakpoint-768px;
            @mixin adjustLineHeight 0.75, $breakpoint-768px;
        }

        @media (--large) {
            padding: pxtorem(6px, 18) pxtorem(28px, 16);
            @mixin adjustFontSize 16px, 1, $breakpoint-1024px;
            @mixin adjustLineHeight 0.75, $breakpoint-1024px;
        }

        &:hover {
            background-color: var(--theme__color__grayscale__6);
        }
    }
}
