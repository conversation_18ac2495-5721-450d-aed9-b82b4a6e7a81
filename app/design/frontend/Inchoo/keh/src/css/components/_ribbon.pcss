/*
 * Generic ribbon container. Has list variation (desktop only)
 * Example: The KEH difference banner list variation - KEH difference on product page (desktop only)
 */

.ribbon {
    @mixin rhythm 0.5, 0.5, 0, 1;
    background-color: var(--theme__color__grayscale__1);
    color: var(--theme__color__white);
}

.ribbon-list {
    display: none;

    @media (--large) {
        display: block;
    }

    & .container {

        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    & .title-wrapper {
        padding-right: 2em;
        flex-basis: pxtorem(320px, 18);
    }

    & .list-icons {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;


        & .ribbon-title {
            font-weight: 700;
            text-transform: uppercase;
        }
    }

    & li {
        display: flex;

        &:not(:last-child) {
            padding-right: 1em;
        }
    }

    & small {
        @mixin adjustFontSize 11px, 0.5, $breakpoint-1024px;
        display: block;
        margin-bottom: 0;
    }

    & .ribbon-title {
        padding: 0;
        color: var(--theme__color__white);
    }

    & .icon {
        @mixin adjustFontSize 42px, 2, $breakpoint-1024px;
        padding-right: pxtorem(18px, 18);
    }
}