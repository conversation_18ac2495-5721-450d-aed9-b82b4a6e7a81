<?xml version="1.0"?>
<!--
/**
* <AUTHOR> Team
* @copyright Copyright (c) 2022 Amasty (https://www.amasty.com)
* @package Improved Layered Navigation Base for Magento 2
*/-->
<!--
/**
* Copyright © 2015 Amasty. All rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <amshopby>
            <general>
                <ajax_enabled>0</ajax_enabled>
                <ajax_scroll_up>1</ajax_scroll_up>
                <enable_sticky_sidebar_desktop>0</enable_sticky_sidebar_desktop>
                <submit_filters_on_desktop>0</submit_filters_on_desktop>
                <submit_filters_on_mobile>0</submit_filters_on_mobile>
                <keep_single_choice_visible>1</keep_single_choice_visible>
                <unfolded_options_state>0</unfolded_options_state>
                <exclude_out_of_stock>0</exclude_out_of_stock>
            </general>
            <slider>
                <slider_style>-default</slider_style>
                <slider_color>#ff5502</slider_color>
            </slider>
            <heading>
                <add_title>after</add_title>
                <title_separator> - </title_separator>
                <add_description>after</add_description>
                <replace_image>1</replace_image>
                <replace_cms_block>1</replace_cms_block>
            </heading>
            <children_categories>
                <image_size>100</image_size>
                <items_per_slide>5</items_per_slide>
            </children_categories>
            <meta>
                <add_title>after</add_title>
                <title_separator> - </title_separator>
                <add_description>after</add_description>
                <description_separator>, </description_separator>
                <add_keywords>before</add_keywords>
            </meta>
            <stock_filter>
                <enabled>0</enabled>
                <placed_block>1</placed_block>
                <position>0</position>
                <label>Stock</label>
                <tooltip></tooltip>
                <is_expanded>0</is_expanded>
                <top_position>0</top_position>
                <side_position>0</side_position>
            </stock_filter>
            <rating_filter>
                <enabled>0</enabled>
                <placed_block>1</placed_block>
                <position>0</position>
                <label>Rating</label>
                <tooltip></tooltip>
                <is_expanded>0</is_expanded>
                <top_position>0</top_position>
                <side_position>0</side_position>
            </rating_filter>
            <am_is_new_filter>
                <enabled>0</enabled>
                <placed_block>1</placed_block>
                <position>0</position>
                <label>New</label>
                <tooltip></tooltip>
                <is_expanded>0</is_expanded>
                <top_position>0</top_position>
                <side_position>0</side_position>
            </am_is_new_filter>
            <am_on_sale_filter>
                <enabled>0</enabled>
                <placed_block>1</placed_block>
                <position>0</position>
                <label>On Sale</label>
                <tooltip></tooltip>
                <is_expanded>0</is_expanded>
                <top_position>0</top_position>
                <side_position>0</side_position>
            </am_on_sale_filter>
            <category_filter>
                <enabled>1</enabled>
                <category_tree_depth>1</category_tree_depth>
                <subcategories_view>1</subcategories_view>
                <subcategories_expand>1</subcategories_expand>
                <render_all_categories_tree>1</render_all_categories_tree>
                <render_categories_level>3</render_categories_level>
                <position>0</position>
                <top_position>0</top_position>
                <side_position>0</side_position>
            </category_filter>
            <tooltips>
                <enabled>1</enabled>
                <image>amasty/shopby/images/tooltip.png</image>
            </tooltips>
        </amshopby>
        <amshopby_root>
            <general>
                <enabled>1</enabled>
                <url>all-products</url>
            </general>
        </amshopby_root>
    </default>
</config>
