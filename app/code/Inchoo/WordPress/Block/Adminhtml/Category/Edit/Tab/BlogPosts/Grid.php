<?php

namespace Inchoo\WordPress\Block\Adminhtml\Category\Edit\Tab\BlogPosts;

class Grid extends \Magento\Backend\Block\Widget\Grid\Extended
{
    /**
     * @var \Magento\Framework\Registry
     */
    protected $registry;

    /**
     * @var \FishPig\WordPress\Model\ResourceModel\Post\CollectionFactory
     */
    protected $collectionFactory;

    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Helper\Data $backendHelper,
        \Magento\Framework\Registry $registry,
        \FishPig\WordPress\Model\ResourceModel\Post\CollectionFactory $collectionFactory,
        array $data = []
    ) {
        parent::__construct($context, $backendHelper, $data);
        $this->registry = $registry;
        $this->collectionFactory = $collectionFactory;
    }

    protected function _construct()
    {
        parent::_construct();
        $this->setId('blog_posts_grid');
        $this->setDefaultSort('ID');
        $this->setDefaultDir('desc');
        $this->setSaveParametersInSession(true);
        $this->setUseAjax(true);
        $this->setVarNameFilter('blog_post_filter');
        $this->setDefaultFilter(['selected_blog_posts' => 1]);
    }

    protected function _prepareCollection()
    {
        $collection = $this->collectionFactory->create()
            ->addFieldtoSelect(['post_title', 'post_author', 'post_date_gmt', 'post_content', 'post_type'])
            ->addPostTypeFilter('post')
            ->setOrderByPostDate()
            ->addIsViewableFilter();

        $this->setCollection($collection);

        return parent::_prepareCollection();
    }

    protected function _prepareColumns()
    {
        $this->addColumn(
            'selected_blog_posts',
            [
                'header_css_class' => 'a-center',
                'type' => 'checkbox',
                'name' => 'in_banners',
                'values' => $this->_getSelectedBlogPosts(),
                'align' => 'center',
                'index' => 'ID'
            ]
        );

        $this->addColumn(
            'ID',
            [
                'header' => __('ID'),
                'type' => 'number',
                'index' => 'ID',
                'header_css_class' => 'col-id',
                'column_css_class' => 'col-id'
            ]
        );

        $this->addColumn(
            'post_title',
            ['header' => __('Title'), 'type' => 'text', 'index' => 'post_title', 'escape' => true]
        );

        return parent::_prepareColumns();
    }

    protected function _addColumnFilterToCollection($column)
    {
        if ($column->getId() == 'selected_blog_posts') {
            $postsIds = $this->_getSelectedBlogPosts();
            if (empty($postsIds)) {
                $postsIds = 0;
            }
            if ($column->getFilter()->getValue()) {
                $this->getCollection()->addFieldToFilter('main_table.ID', ['in' => $postsIds]);
            } else {
                if ($postsIds) {
                    $this->getCollection()->addFieldToFilter('main_table.ID', ['nin' => $postsIds]);
                }
            }
        } else {
            parent::_addColumnFilterToCollection($column);
        }
        return $this;
    }

    protected function _getSelectedBlogPosts()
    {
        $posts = $this->getSelectedBlogPosts();
        if (!$posts) {
            $posts = $this->getBlogPosts();
        }

        return $posts;
    }

    public function getBlogPosts()
    {
        $category = $this->registry->registry('current_category');
        if ($category && $catPosts = $category->getBlogPosts()) {
            return $catPosts;
        }

        return [];
    }

    public function getRowUrl($row)
    {
        return '';
    }

    public function getGridUrl()
    {
        return $this->getUrl('adminhtml/wordpress_blogposts/grid', ['_current' => true]);
    }
}
