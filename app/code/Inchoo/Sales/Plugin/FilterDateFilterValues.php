<?php
declare(strict_types=1);

namespace Inchoo\Sales\Plugin;

use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;

readonly class FilterDateFilterValues
{
    public function __construct(
        private TimezoneInterface $timeZone
    ) {

    }

    /**
     * @param Collection $subject
     * @param array|string $field
     * @param array|string|null $condition
     * @return array
     */
    public function beforeAddFieldToFilter(Collection $subject, $field, $condition = null): array
    {
        if ($field === 'created_at' && is_array($condition)) {
            foreach ($condition as $key => $value) {
                if (!in_array($key, ['from', 'to', 'value'])) {
                    unset($condition[$key]);
                    continue;
                }
                $condition[$key] = $this->timeZone->convertConfigTimeToUtc($value);
            }
        }
        return [$field, $condition];
    }
}
