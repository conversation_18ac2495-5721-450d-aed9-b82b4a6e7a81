<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Inchoo\GiftCard\Api\GiftCardInterface" type="Inchoo\GiftCard\Model\GiftCard"/>
    <preference for="Inchoo\GiftCard\Api\Data\GiftCardResultInfoInterface" type="Inchoo\GiftCard\Model\Data\GiftCardResultInfo"/>
    <preference for="Inchoo\GiftCard\Api\Data\GiftCardSearchResultInterface" type="Inchoo\GiftCard\Model\Data\GiftCardSearchResult"/>

    <!--Plugins start-->
    <type name="Aheadworks\Giftcard\Api\GiftcardManagementInterface">
        <plugin name="inchoo_giftcard_enable_original_email" type="Inchoo\GiftCard\Plugin\Giftcard\Model\Service\GiftCardManagementPlugin" />
    </type>
    <type name="Aheadworks\Giftcard\Model\Product\Option\Render">
        <plugin name="keh_giftcard_options_render" type="Inchoo\GiftCard\Plugin\Giftcard\Model\Product\Option\RenderPlugin"/>
    </type>
    <type name="PayPal\Braintree\Gateway\Request\Level23ProcessingDataBuilder">
        <plugin name="AddAwGiftCardAccountAsLineItemForPayPal" type="Inchoo\GiftCard\Plugin\PayPal\Braintree\Level23Processing\AddGiftCardToInvoice" sortOrder="3"/>
    </type>

    <type name="Magento\Variable\Model\Config\Structure\AvailableVariables">
        <arguments>
            <argument name="configPaths" xsi:type="array">
                <item name="general/store_information" xsi:type="array">
                    <item name="general/store_information/phone_international" xsi:type="string">1</item>
                </item>
            </argument>
        </arguments>
    </type>

    <virtualType name="Inchoo\Giftcard\Block\Product\View\Gallery" type="Magento\Catalog\Block\Product\View\Gallery">
        <arguments>
            <argument name="galleryImagesConfig" xsi:type="array">
                <item name="small_image" xsi:type="array">
                    <item name="image_id" xsi:type="string">aw_gift_card_image_small</item>
                    <item name="data_object_key" xsi:type="string">small_image_url</item>
                    <item name="json_object_key" xsi:type="string">thumb</item>
                </item>
                <item name="medium_image" xsi:type="array">
                    <item name="image_id" xsi:type="string">aw_gift_card_image_medium</item>
                    <item name="data_object_key" xsi:type="string">medium_image_url</item>
                    <item name="json_object_key" xsi:type="string">img</item>
                </item>
                <item name="large_image" xsi:type="array">
                    <item name="image_id" xsi:type="string">aw_gift_card_image_large</item>
                    <item name="data_object_key" xsi:type="string">large_image_url</item>
                    <item name="json_object_key" xsi:type="string">full</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
