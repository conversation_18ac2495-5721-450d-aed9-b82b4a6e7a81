<?php

declare(strict_types=1);

namespace Inchoo\CompareProducts\Model\Autocomplete;

use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Search\Model\Autocomplete\DataProviderInterface;
use Magento\Search\Model\Autocomplete\ItemFactory;
use Magento\Search\Model\QueryFactory;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Helper\Image;

/**
 * Class DataProvider
 * @package Inchoo\CompareProducts\Model\Autocomplete
 */
class DataProvider implements DataProviderInterface
{
    /**
     * Autocomplete limit
     */
    public const CONFIG_AUTOCOMPLETE_LIMIT = 5;

    /**
     * Query factory
     *
     * @var QueryFactory
     */
    protected $queryFactory;

    /**
     * Autocomplete result item factory
     *
     * @var ItemFactory
     */
    protected $itemFactory;

    /**
     * Limit
     *
     * @var int
     */
    protected $limit = self::CONFIG_AUTOCOMPLETE_LIMIT;

    /**
     * @var CollectionFactory
     */
    protected $productCollectionFactory;

    private $catalogProductVisibility;

    /**
     * @var Image
     */
    protected $imageHelper;

    /**
     * DataProvider constructor.
     *
     * @param QueryFactory $queryFactory
     * @param ItemFactory $itemFactory
     * @param CollectionFactory $productCollectionFactory
     * @param Visibility $catalogProductVisibility
     */
    public function __construct(
        QueryFactory $queryFactory,
        ItemFactory $itemFactory,
        CollectionFactory $productCollectionFactory,
        Visibility $catalogProductVisibility,
        Image $imageHelper
    ) {
        $this->queryFactory = $queryFactory;
        $this->itemFactory = $itemFactory;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->catalogProductVisibility = $catalogProductVisibility;
        $this->imageHelper = $imageHelper;
    }

    /**
     * @return array|\Magento\Search\Model\Autocomplete\ItemInterface[]
     */
    public function getItems()
    {
        $query = $this->queryFactory->get()->getQueryText();
        $collection = $this->getProductCollection($query);
        $result = [];
        foreach ($collection as $item) {
            $resultItem = $this->itemFactory->create([
                'title' => $item->getName(),
                'num_results' => 1,
                'product_id' => $item->getId(),
                'image' => $this->imageHelper->init($item, 'product_base_image')->resize(90)->getUrl(),
            ]);
            if ($resultItem->getTitle() == $query) {
                array_unshift($result, $resultItem);
            } else {
                $result[] = $resultItem;
            }
        }
        return $result;
    }

    /**
     * Get product collection with name filter
     *
     * @param string $query
     * @return Collection
     */
    private function getProductCollection(string $query) : Collection
    {
        $productCollection = $this->productCollectionFactory->create();
        $productCollection->addAttributeToFilter('name', ['like' => '%'.$query.'%'])
            ->addAttributeToSelect('image')
            ->setVisibility($this->catalogProductVisibility->getVisibleInCatalogIds())
            ->setPageSize($this->limit);

        return $productCollection;
    }
}
