<?php

declare(strict_types=1);

namespace Inchoo\FedExHoldAtLocation\Plugin\Sales\Api;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderRepository
{
    protected $extensionFactory;

    /**
     * OrderRepository constructor.
     * @param OrderExtensionFactory $extensionFactory
     */
    public function __construct(
        OrderExtensionFactory $extensionFactory
    ) {
        $this->extensionFactory = $extensionFactory;
    }

    /**
     * @param OrderRepositoryInterface $subject
     * @param OrderInterface $order
     * @return OrderInterface
     */
    public function afterGet(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ): OrderInterface {
        $extensionAttributes = $order->getExtensionAttributes();
        $extensionAttributes = $extensionAttributes ?: $this->extensionFactory->create();

        $fedexLocationId = $order->getShippingAddress() ? $order->getShippingAddress()->getFedexLocationId() : null;

        $extensionAttributes->setIsHoldAtLocation((bool)$fedexLocationId);
        $extensionAttributes->setFedexLocationId($fedexLocationId);

        $order->setExtensionAttributes($extensionAttributes);

        return $order;
    }

    /**
     * @param OrderRepositoryInterface $subject
     * @param OrderSearchResultInterface $searchResult
     * @return OrderSearchResultInterface
     */
    public function afterGetList(
        OrderRepositoryInterface $subject,
        OrderSearchResultInterface $searchResult
    ): OrderSearchResultInterface {
        $orders = $searchResult->getItems();

        foreach ($orders as $order) {
            $extensionAttributes = $order->getExtensionAttributes();
            $extensionAttributes = $extensionAttributes ?: $this->extensionFactory->create();

            $fedexLocationId = $order->getShippingAddress() ? $order->getShippingAddress()->getFedexLocationId() : null;

            $extensionAttributes->setIsHoldAtLocation((bool)$fedexLocationId);
            $extensionAttributes->setFedexLocationId($fedexLocationId);

            $order->setExtensionAttributes($extensionAttributes);
        }

        return $searchResult;
    }
}
