<?php
declare(strict_types=1);

namespace Inchoo\Testimonial\Block\Adminhtml\System\Config;

use Magento\Config\Block\System\Config\Form\Field;
use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Framework\Exception\LocalizedException;

/**
 * Class Button
 *
 * @package Inchoo Testimonial
 */
class Button extends Field
{
    protected $_template = 'Inchoo_Testimonial::system/config/button.phtml';

    /**
     * @param AbstractElement $element
     * @return string
     */
    public function render(AbstractElement $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();
        return parent::render($element);
    }

    /**
     * @param AbstractElement $element
     * @return string
     */
    protected function _getElementHtml(AbstractElement $element)
    {
        return $this->_toHtml();
    }

    /**
     * @return string
     */
    public function getAjaxUrl(): string
    {
        return $this->getUrl('testimonial/testimonial/fetch');
    }

    /**
     * @return mixed
     */
    public function getButtonHtml()
    {
        try {
            $button = $this->getLayout()
                ->createBlock(\Magento\Backend\Block\Widget\Button::class)
                ->setData([
                    'id' => 'refresh',
                    'label' => __('Refresh'),
                ]);

            return $button->toHtml();
        } catch (LocalizedException) {
            return '';
        }
    }
}
