<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">transferstock_receiving_history.transferstock_receiving_history_data_source</item>
            <item name="deps" xsi:type="string">transferstock_receiving_history.transferstock_receiving_history_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">transferstock_receiving_history_columns</item>

    </argument>
    <dataSource name="transferstock_delivery_history_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magestore\TransferStock\Ui\DataProvider\InventoryTransfer\Form\ReceivingHistory</argument>
            <argument name="name" xsi:type="string">transferstock_receiving_history_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">receive_id</argument>
            <argument name="requestFieldName" xsi:type="string">receive_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">receive_id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>

    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
                <item name="sticky" xsi:type="boolean">false</item>
            </item>
        </argument>
        <paging name="listing_paging"/>
    </container>

    <columns name="transferstock_receiving_history_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">transferstock_receiving_history.transferstock_receiving_history.listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
            </item>
        </argument>

        <column name="receive_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">ID</item>
                </item>
            </argument>
        </column>
        <column name="created_on">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="dateFormat" xsi:type="string">MM/dd/yyyy</item>
                    <item name="label" xsi:type="string" translate="true">Created On</item>
                </item>
            </argument>
        </column>

        <column name="created_by">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="label" xsi:type="string" translate="true">Created By</item>
                </item>
            </argument>
        </column>
        <column name="total_qty">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Received Qty</item>
                </item>
            </argument>
        </column>

        <actionsColumn name="actions" class="Magestore\TransferStock\Ui\Component\Listing\Columns\InventoryTransfer\Columns\ReceiveActions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="draggable" xsi:type="boolean">false</item>
                    <item name="indexField" xsi:type="string">receive_id</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">Action</item>
                    <item name="filterable" xsi:type="boolean">false</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                </item>
            </argument>
        </actionsColumn>

    </columns>
</listing>
