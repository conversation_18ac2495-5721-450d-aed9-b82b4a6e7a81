<?php
/**
 * Copyright © Magestore, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace Magestore\ReportSuccess\Model;

use Magento\Framework\Exception\LocalizedException;
use Magento\Reports\Model\Flag as MagentoFlag;

/**
 * Used to create Report Flag Model
 *
 * Class Flag
 */
class Flag extends MagentoFlag
{

    public const REPORT_MAGESTORE_STOCK_VALUE_FLAG_CODE = 'magestore_stock_value_aggregated';
    public const REPORT_MAGESTORE_STOCK_BY_SOURCE_FLAG_CODE = 'magestore_stock_by_source_aggregated';
    public const REPORT_MAGESTORE_STOCK_DETAILS_FLAG_CODE = 'magestore_stock_details_aggregated';

    /**
     * GetMagestoreStockValueStatisticFlagCode
     *
     * @param Statistics\StatisticInterface $magestoreStockValueStatistic
     * @return string
     */
    public function getMagestoreStockValueStatisticFlagCode(Statistics\StatisticInterface $magestoreStockValueStatistic)
    {
        return $magestoreStockValueStatistic->getStatisticId()."_aggregated";
    }

    /**
     * GetMagestoreStockValueStatisticUpdatedAt
     *
     * @param Statistics\StatisticInterface $magestoreStockValueStatistic
     * @return string
     */
    public function getMagestoreStockValueUpdatedAt(Statistics\StatisticInterface $magestoreStockValueStatistic)
    {
        $reportCode = $this->getMagestoreStockValueStatisticFlagCode($magestoreStockValueStatistic);
        $flag = $this->setReportFlagCode($reportCode)->loadSelf();
        return $flag->hasData()? $flag->getLastUpdate() : '';
    }

    /**
     * GetMagestoreStockBySourceStatisticFlagCode
     *
     * @param Statistics\StatisticInterface $magestoreStockBySourceStatistic
     * @return string
     */
    public function getMagestoreStockBySourceStatisticFlagCode(
        Statistics\StockBySourceStatisticInterface $magestoreStockBySourceStatistic
    ) {
        return $magestoreStockBySourceStatistic->getStatisticId()."_aggregated";
    }

    /**
     * GetMagestoreStockBySourceUpdatedAt
     *
     * @param Statistics\StockBySourceStatisticInterface $magestoreStockBySourceStatistic
     * @return string
     * @throws LocalizedException
     */
    public function getMagestoreStockBySourceUpdatedAt(
        Statistics\StockBySourceStatisticInterface $magestoreStockBySourceStatistic
    ) {
        $reportCode = $this->getMagestoreStockBySourceStatisticFlagCode($magestoreStockBySourceStatistic);
        $flag = $this->setReportFlagCode($reportCode)->loadSelf();

        return $flag->hasData()? $flag->getLastUpdate() : '';
    }

    /**
     * GetMagestoreReportStatisticFlagCode
     *
     * @param Statistics\StatisticInterface $magestoreReportStatistic
     * @return string
     */
    public function getMagestoreReportStatisticFlagCode(Statistics\StatisticInterface $magestoreReportStatistic)
    {
        return $magestoreReportStatistic->getStatisticId()."_aggregated";
    }

    /**
     * GetMagestoreReportUpdatedAt
     *
     * @param Statistics\StatisticInterface $magestoreReportStatistic
     * @return string
     */
    public function getMagestoreReportUpdatedAt(Statistics\StatisticInterface $magestoreReportStatistic)
    {
        $reportCode = $this->getMagestoreReportStatisticFlagCode($magestoreReportStatistic);
        $flag = $this->setReportFlagCode($reportCode)->loadSelf();
        return $flag->hasData()? $flag->getLastUpdate() : '';
    }
}
