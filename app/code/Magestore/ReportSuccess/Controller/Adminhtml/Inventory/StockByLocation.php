<?php
/**
 * Copyright © 2018 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\ReportSuccess\Controller\Adminhtml\Inventory;

use IntlDateFormatter;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\View\Result\LayoutFactory;
use Magestore\ReportSuccess\Model\Statistics\StockBySourceStatisticInterface;
use Magestore\ReportSuccess\Controller\Adminhtml\Inventory;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Backend\Model\View\Result\ForwardFactory;
use Magestore\ReportSuccess\Model\FlagFactory;

/**
 * Class StockValue
 *
 * Controller inventory stock by location
 */
class StockByLocation extends Inventory implements HttpGetActionInterface
{
    /**
     * @var FlagFactory
     */
    protected $flagFactory;
    /**
     * @var TimezoneInterface
     */
    protected $timezone;
    /**
     * @var StockBySourceStatisticInterface
     */
    protected $statistic;

    /**
     * Construct
     *
     * @param Context $context
     * @param PageFactory $resultPageFactory
     * @param JsonFactory $resultJsonFactory
     * @param LayoutFactory $resultLayoutFactory
     * @param ForwardFactory $resultForwardFactory
     * @param FlagFactory $flagFactory
     * @param TimezoneInterface $timezone
     * @param StockBySourceStatisticInterface $statistic
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        JsonFactory $resultJsonFactory,
        LayoutFactory $resultLayoutFactory,
        ForwardFactory $resultForwardFactory,
        FlagFactory $flagFactory,
        TimezoneInterface $timezone,
        StockBySourceStatisticInterface $statistic
    ) {
        parent::__construct(
            $context,
            $resultPageFactory,
            $resultJsonFactory,
            $resultLayoutFactory,
            $resultForwardFactory
        );
        $this->flagFactory = $flagFactory;
        $this->timezone = $timezone;
        $this->statistic = $statistic;
    }

    /**
     * Determine if authorized to perform group actions.
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Magestore_ReportSuccess::stock_value');
    }

    /**
     * Warehouse grid
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        $this->showLastExecutionTime();

        $resultPage = $this->_initAction();
        $resultPage->getConfig()->getTitle()->prepend(__('Stock by Source Report'));
        return $resultPage;
    }

    /**
     * Init layout, menu and breadcrumb
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    protected function _initAction()
    {
        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Magestore_ReportSuccess::stock_by_location');
        $resultPage->getConfig()->getTitle()->prepend(__('Stock by Source Report'));
        $resultPage->addBreadcrumb(__('Stock by Source Report'), __('Stock by Source Report'));
        return $resultPage;
    }

    /**
     * Show Last Execution Time
     *
     * @return $this
     */
    public function showLastExecutionTime()
    {
        $statistic = $this->statistic;
        $flag = $this->flagFactory->create();
        $updatedAt = $flag->getMagestoreStockBySourceUpdatedAt($statistic);

        if ($updatedAt) {
            $updatedAt = $this->timezone->formatDate(
                $updatedAt,
                IntlDateFormatter::MEDIUM,
                true
            );
        } else {
            $updatedAt = 'undefined';
        }

        $refreshStatsLink = $this->getUrl('reports/report_statistics');
        $directRefreshLink = $this->getUrl('reports/report_statistics/refreshRecent');

        $this->messageManager->addNotice(
            __(
                'Last updated: %1. To refresh last day\'s <a href="%2">statistics</a>, '.
                'click <a href="#2" data-post="%3">here</a>.',
                $updatedAt,
                $refreshStatsLink,
                str_replace(
                    '"',
                    '&quot;',
                    json_encode(['action' => $directRefreshLink, 'data' => ['code' => $statistic->getStatisticId()]])
                )
            )
        );
        return $this;
    }
}
