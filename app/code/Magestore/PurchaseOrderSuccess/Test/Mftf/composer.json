{"name": "magento/functional-test-module-webpos-login", "description": "N/A", "config": {"sort-packages": true}, "require": {"php": "~7.0.13|~7.1.0", "magento/functional-test-module-store": "100.0.0-dev", "magento/functional-test-module-eav": "100.0.0-dev", "magento/functional-test-module-cms": "100.0.0-dev", "magento/functional-test-module-indexer": "100.0.0-dev", "magento/functional-test-module-customer": "100.0.0-dev", "magento/functional-test-module-theme": "100.0.0-dev", "magento/functional-test-module-checkout": "100.0.0-dev", "magento/functional-test-module-backend": "100.0.0-dev", "magento/functional-test-module-widget": "100.0.0-dev", "magento/functional-test-module-wishlist": "100.0.0-dev", "magento/functional-test-module-tax": "100.0.0-dev", "magento/functional-test-module-msrp": "100.0.0-dev", "magento/functional-test-module-catalog-inventory": "100.0.0-dev", "magento/functional-test-module-directory": "100.0.0-dev", "magento/functional-test-module-catalog-rule": "100.0.0-dev", "magento/functional-test-module-product-alert": "100.0.0-dev", "magento/functional-test-module-url-rewrite": "100.0.0-dev", "magento/functional-test-module-catalog-url-rewrite": "100.0.0-dev", "magento/functional-test-module-page-cache": "100.0.0-dev", "magento/functional-test-module-quote": "100.0.0-dev", "magento/functional-test-module-config": "100.0.0-dev", "magento/functional-test-module-media-storage": "100.0.0-dev", "magento/magento2-functional-testing-framework": "2.2.0", "magento/functional-test-module-ui": "100.0.0-dev"}, "suggest": {"magento/functional-test-module-cookie": "100.0.0-dev", "magento/functional-test-module-sales": "100.0.0-dev", "magento/functional-test-module-catalog-sample-data": "100.0.0-dev"}, "type": "magento2-test", "license": ["OSL-3.0", "AFL-3.0"]}