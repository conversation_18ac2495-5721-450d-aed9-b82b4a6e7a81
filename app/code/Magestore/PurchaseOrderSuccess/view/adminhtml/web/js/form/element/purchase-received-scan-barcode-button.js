/*
 * Copyright © 2024 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'jquery',
    'Magento_Ui/js/form/components/button'
], function ($, Component) {
    'use strict';

    return Component.extend({
        defaults: {
            elementTmpl: 'Magestore_PurchaseOrderSuccess/form/element/scan-barcode-button'
        },

        handleOnclick: function () {
            if ($('#purchase-received-barcode-input-wrapper').length) {
                $('#purchase-received-barcode-input-wrapper').toggle();
            } else {
                $('#purchase-received-barcode-input-wrapper').toggle();
            }
            $('#purchase-received-barcode-input-wrapper').focus();
        }
    });
});
