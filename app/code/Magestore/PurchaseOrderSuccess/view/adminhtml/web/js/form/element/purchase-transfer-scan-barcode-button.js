/*
 * Copyright © 2024 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'jquery',
    'Magento_Ui/js/form/components/button'
], function ($, Component) {
    'use strict';

    return Component.extend({
        defaults: {
            elementTmpl: 'Magestore_PurchaseOrderSuccess/form/element/scan-barcode-button'
        },

        handleOnclick: function () {
            if ($('#purchase-transfer-barcode-input-wrapper').length) {
                $('#purchase-transfer-barcode-input-wrapper').toggle();
            } else {
                $('#purchase-transfer-barcode-input-wrapper').toggle();
            }
            $('#purchase-transfer-barcode-input-wrapper').focus();
        }
    });
});
