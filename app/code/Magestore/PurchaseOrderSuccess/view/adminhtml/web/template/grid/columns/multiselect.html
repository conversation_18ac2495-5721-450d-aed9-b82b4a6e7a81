<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<th class="data-grid-multicheck-cell">
    <div class="action-multicheck-wrap" css="_disabled: !totalRecords()" collapsible="">
        <input class="admin__control-checkbox" type="checkbox"
               data-bind="
                checked: isPageSelected(true),
                attr: {id: ++ko.uid},
                event: { change: toggleSelectAll },
                css: { '_indeterminate': indetermine },
                enable: totalRecords">
        <label attr="for: ko.uid" style="margin-left: 7px"></label>
        <ul class="action-menu" each="actions" closeCollapsible="">
            <li data-bind="click: $parent[value].bind($parent),
                           visible: $parent.isActionRelevant(value)">
                <span class="action-menu-item" text="label"></span>
            </li>
        </ul>
    </div>
</th>