<?xml version="1.0"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="editor"/>
    <update handle="styles"/>
    <head>
        <css src="Magestore_PurchaseOrderSuccess::css/purchaseorder.css"/>
    </head>
    <body>
        <referenceContainer name="page.main.actions">
            <block class="Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\Timeline" name="variation-steps-wizard">
                <arguments>
                    <argument name="config" xsi:type="array">
                        <item name="form" xsi:type="string">os_return_order_form.os_return_order_form</item>
                    </argument>
                </arguments>
                <block class="Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\StatusNew" name="new">
                    <arguments>
                        <argument name="config" xsi:type="array">
                            <item name="form" xsi:type="string">os_return_order_form.os_return_order_form</item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\StatusPending" name="pending">
                    <arguments>
                        <argument name="config" xsi:type="array">
                            <item name="form" xsi:type="string">os_return_order_form.os_return_order_form</item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\StatusProcessing" name="processing">
                    <arguments>
                        <argument name="config" xsi:type="array">
                            <item name="form" xsi:type="string">os_return_order_form.os_return_order_form</item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\StatusCompleted" name="completed">
                    <arguments>
                        <argument name="config" xsi:type="array">
                            <item name="form" xsi:type="string">os_return_order_form.os_return_order_form</item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\StatusCanceled" name="canceled">
                    <arguments>
                        <argument name="config" xsi:type="array">
                            <item name="form" xsi:type="string">os_return_order_form.os_return_order_form</item>
                        </argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>
        <referenceContainer name="content">
            <uiComponent name="os_return_order_form"/>
        </referenceContainer>
    </body>
</page>
