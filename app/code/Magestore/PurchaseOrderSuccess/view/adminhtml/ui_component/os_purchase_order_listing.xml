<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_purchase_order_listing.os_purchase_order_listing_data_source</item>
            <item name="deps" xsi:type="string">os_purchase_order_listing.os_purchase_order_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">purchase_order_template_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Create Purchase Order</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/new</item>
            </item>
        </item>
    </argument>
    <dataSource name="os_purchase_order_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">os_purchase_order_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">purchase_order_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">purchase_order_id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <exportButton class="Magento\Ui\Component\ExportButton">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magestore_PurchaseOrderSuccess/js/grid/export</item>
                    <item name="displayArea" xsi:type="string">dataGridActions</item>
                    <item name="options" xsi:type="array">
                        <item name="csv" xsi:type="array">
                            <item name="value" xsi:type="string">csv</item>
                            <item name="label" xsi:type="string" translate="true">CSV</item>
                            <item name="url" xsi:type="string">mui/export/gridToCsv/is_export/true</item>
                        </item>
                        <item name="xml" xsi:type="array">
                            <item name="value" xsi:type="string">xml</item>
                            <item name="label" xsi:type="string" translate="true">Excel XML</item>
                            <item name="url" xsi:type="string">mui/export/gridToXml/is_export/true</item>
                        </item>
                    </item>
                </item>
            </argument>
        </exportButton>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="purchase_order_template_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">os_purchase_order_listing.os_purchase_order_listing.listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
            </item>
        </argument>

        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">purchase_order_id</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <!--
        <column name="purchase_order_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="sorting" xsi:type="string">asc</item>
                    <item name="label" xsi:type="string" translate="true">ID</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                </item>
            </argument>
        </column>
        -->
        <column name="purchase_code">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="label" xsi:type="string" translate="true">PO Number</item>
                    <item name="sortOrder" xsi:type="number">25</item>
                </item>
            </argument>
        </column>
        <column name="purchased_at" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\Date" sortOrder="30">
            <settings>
                <timezone>false</timezone>
                <skipTimeZoneConversion>true</skipTimeZoneConversion>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Purchase On</label>
            </settings>
        </column>
        <column name="expected_at" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\Date">
            <settings>
                <timezone>false</timezone>
                <skipTimeZoneConversion>true</skipTimeZoneConversion>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Expect Delivery On</label>
            </settings>
        </column>
        <column name="supplier_id">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magestore\PurchaseOrderSuccess\Model\PurchaseOrder\Option\Supplier</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="label" xsi:type="string" translate="true">Supplier</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
            </argument>
        </column>
        <column name="total_qty_orderred">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Requested Qty</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
            </argument>
        </column>
        <column name="total_qty_received">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Received Qty</item>
                    <item name="sortOrder" xsi:type="number">51</item>
                </item>
            </argument>
        </column>
        <column name="total_qty_billed">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Billed Qty</item>
                    <item name="sortOrder" xsi:type="number">52</item>
                </item>
            </argument>
        </column>
        <column name="total_billed" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Total Paid</item>
                    <item name="sortOrder" xsi:type="number">55</item>
                </item>
            </argument>
        </column>        
        <column name="grand_total_incl_tax" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Grand Total (Incl. Tax)</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
            </argument>
        </column>
        <column name="status">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magestore\PurchaseOrderSuccess\Model\PurchaseOrder\Option\Status</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\PurchaseOrder\Actions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="draggable" xsi:type="boolean">false</item>
                    <item name="indexField" xsi:type="string">purchase_order_id</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">Action</item>
                    <item name="filterable" xsi:type="boolean">false</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>