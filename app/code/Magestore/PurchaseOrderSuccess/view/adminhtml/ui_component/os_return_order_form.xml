<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_return_order_form.os_return_order_form_data_source
            </item>
            <item name="deps" xsi:type="string">os_return_order_form.os_return_order_form_data_source</item>
            <item name="namespace" xsi:type="string">os_return_order_form</item>
        </item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="array">
                <item name="name" xsi:type="string">back</item>
                <item name="label" xsi:type="string" translate="true">Back</item>
                <item name="class" xsi:type="string">back</item>
                <item name="url" xsi:type="string">*/*/</item>
            </item>
            <item name="complete" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\Complete</item>
            <item name="transfer" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\Transfer</item>
            <item name="start" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\Save</item>
            <item name="confirm" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\Confirm</item>
            <item name="sendRequest" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\SendRequest</item>
            <item name="print" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\PrintReturnOrder</item>
            <item name="cancel" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\Cancel</item>
            <item name="delete" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Button\Delete</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="childrenFromMeta" xsi:type="boolean">true</item>
    </argument>
    <dataSource name="os_return_order_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magestore\PurchaseOrderSuccess\Ui\DataProvider\ReturnOrder\Form\ReturnOrder</argument>
            <argument name="name" xsi:type="string">os_return_order_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">return_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="*/returnOrder/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
</form>
