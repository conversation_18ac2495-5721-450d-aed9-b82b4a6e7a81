<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_purchase_order_form.os_purchase_order_form_data_source
            </item>
            <item name="deps" xsi:type="string">os_purchase_order_form.os_purchase_order_form_data_source</item>
            <item name="namespace" xsi:type="string">os_purchase_order_form</item>
        </item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Back</item>
            <item name="complete" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Complete</item>
            <item name="receive" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Receive</item>
            <item name="transfer" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Transfer</item>
            <item name="fake_save" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\SaveConfirm</item>
            <item name="start" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Save</item>
            <item name="convert" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\Quotation\Edit\Button\Convert</item>
            <item name="confirm" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Confirm</item>
            <item name="sendRequest" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\SendRequest</item>
            <item name="print" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\PrintPurchaseOrder</item>
            <item name="cancel" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Cancel</item>
            <item name="delete" xsi:type="string">Magestore\PurchaseOrderSuccess\Block\Adminhtml\PurchaseOrder\Edit\Button\Delete</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="childrenFromMeta" xsi:type="boolean">true</item>
    </argument>
    <dataSource name="os_purchase_order_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magestore\PurchaseOrderSuccess\Ui\DataProvider\PurchaseOrder\Form\PurchaseOrder</argument>
            <argument name="name" xsi:type="string">os_purchase_order_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">purchase_order_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="*/purchaseOrder/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
</form>
