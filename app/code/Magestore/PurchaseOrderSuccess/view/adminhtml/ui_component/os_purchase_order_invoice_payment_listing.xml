<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_purchase_order_invoice_payment_listing.os_purchase_order_invoice_payment_listing_data_source</item>
            <item name="deps" xsi:type="string">os_purchase_order_invoice_payment_listing.os_purchase_order_invoice_payment_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">purchase_order_invoice_payment_template_columns</item>
    </argument>
    <dataSource name="os_purchase_order_invoice_payment_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">os_purchase_order_invoice_payment_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">purchase_order_invoice_id</argument>
            <argument name="requestFieldName" xsi:type="string">invoice_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">purchase_order_invoice_payment_id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <exportButton name="export_button">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magestore_PurchaseOrderSuccess/js/grid/export</item>
                </item>
            </argument>
        </exportButton>
        <filters name="listing_filters"/>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="purchase_order_invoice_payment_template_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">os_purchase_order_invoice_payment_listing.os_purchase_order_invoice_payment_listing.listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
            </item>
        </argument>

        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">purchase_order_invoice_payment_id</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="payment_at" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Payment Date</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                    <item name="dateFormat" xsi:type="string">MM/dd/yyyy</item>
                </item>
            </argument>
        </column>
        <column name="payment_amount" class="Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Payment Amount</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                </item>
            </argument>
        </column>
        <column name="payment_method">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magestore\PurchaseOrderSuccess\Model\PurchaseOrder\Option\PaymentMethod</item>
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/select</item>
                    <item name="label" xsi:type="string" translate="true">Payment Method</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
            </argument>
        </column>
        <column name="description">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Description</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>