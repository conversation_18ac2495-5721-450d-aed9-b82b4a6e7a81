<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_return_order_import_product_form.os_return_order_import_product_form_data_source
            </item>
            <item name="deps" xsi:type="string">os_return_order_import_product_form.os_return_order_import_product_form_data_source</item>
            <item name="namespace" xsi:type="string">os_return_order_import_product_form</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <dataSource name="os_return_order_import_product_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magestore\PurchaseOrderSuccess\Ui\DataProvider\ReturnOrder\Form\Modifier\ReturnSumary\ImportProductDataProvider</argument>
            <argument name="name" xsi:type="string">os_return_order_import_product_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">return_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
                <item name="submit_url" xsi:type="url" path="purchaseordersuccess/returnOrder_product/import"/>
            </item>
        </argument>
    </dataSource>
    <fieldset name="import_product" >
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string" translate="true"></item>
                <item name="collapsible" xsi:type="boolean">false</item>
            </item>
        </argument>
        <container name="messages">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string"/>
                    <item name="component" xsi:type="string">Magento_Catalog/js/components/messages</item>
                </item>
            </argument>
        </container>
        <container name="import">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
            <htmlContent name="html_content">
                <argument name="block" xsi:type="object">Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Fieldset\ReturnSumary\ImportProduct</argument>
            </htmlContent>
        </container>
    </fieldset>
</form>
