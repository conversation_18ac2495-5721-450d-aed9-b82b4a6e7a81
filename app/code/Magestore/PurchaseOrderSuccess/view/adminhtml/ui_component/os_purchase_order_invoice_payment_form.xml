<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_purchase_order_invoice_payment_form.os_purchase_order_invoice_payment_form_data_source
            </item>
            <item name="deps" xsi:type="string">os_purchase_order_invoice_payment_form.os_purchase_order_invoice_payment_form_data_source</item>
            <item name="namespace" xsi:type="string">os_purchase_order_invoice_payment_form</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="childrenFromMeta" xsi:type="boolean">true</item>
    </argument>
    <dataSource name="os_purchase_order_invoice_payment_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magestore\PurchaseOrderSuccess\Ui\DataProvider\PurchaseOrder\Invoice\Payment\Form\Payment</argument>
            <argument name="name" xsi:type="string">os_purchase_order_invoice_payment_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">purchase_order_invoice_id</argument>
            <argument name="requestFieldName" xsi:type="string">invoice_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="purchaseordersuccess/purchaseOrder_invoice/payment"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
    <!--<fieldset name="general">-->
        <!--<argument name="data" xsi:type="array">-->
            <!--<item name="config" xsi:type="array">-->
                <!--<item name="label" xsi:type="string"/>-->
                <!--<item name="collapsible" xsi:type="boolean">false</item>-->
            <!--</item>-->
        <!--</argument>-->
        <!--<field name="purchase_order_invoice_id">-->
            <!--<argument name="data" xsi:type="array">-->
                <!--<item name="config" xsi:type="array">-->
                    <!--<item name="dataType" xsi:type="string">text</item>-->
                    <!--<item name="formElement" xsi:type="string">hidden</item>-->
                    <!--<item name="label" xsi:type="string">Invoice Id</item>-->
                    <!--<item name="sortOrder" xsi:type="number">10</item>-->
                <!--</item>-->
            <!--</argument>-->
        <!--</field>-->
        <!--<field name="payment_at">-->
            <!--<argument name="data" xsi:type="array">-->
                <!--<item name="config" xsi:type="array">-->
                    <!--<item name="dataType" xsi:type="string">date</item>-->
                    <!--<item name="formElement" xsi:type="string">date</item>-->
                    <!--<item name="label" xsi:type="string">Payment Date</item>-->
                    <!--<item name="sortOrder" xsi:type="number">20</item>-->
                    <!--<item name="validation" xsi:type="array">-->
                        <!--<item name="required-entry" xsi:type="boolean">true</item>-->
                    <!--</item>-->
                    <!--<item name="dataScope" xsi:type="string">payment_at</item>-->
                <!--</item>-->
            <!--</argument>-->
        <!--</field>-->
        <!--<field name="payment_method">-->
            <!--<argument name="data" xsi:type="array">-->
                <!--<item name="options" xsi:type="object">Magestore\PurchaseOrderSuccess\Model\PurchaseOrder\Option\PaymentMethod</item>-->
                <!--<item name="config" xsi:type="array">-->
                    <!--<item name="dataType" xsi:type="string">text</item>-->
                    <!--<item name="formElement" xsi:type="string">select</item>-->
                    <!--<item name="label" xsi:type="string">Payment Method</item>-->
                    <!--<item name="sortOrder" xsi:type="number">30</item>-->
                    <!--<item name="validation" xsi:type="array">-->
                        <!--<item name="required-entry" xsi:type="boolean">true</item>-->
                    <!--</item>-->
                    <!--<item name="dataScope" xsi:type="string">payment_method</item>-->
                <!--</item>-->
            <!--</argument>-->
        <!--</field>-->
        <!--<field name="payment_amount">-->
            <!--<argument name="data" xsi:type="array">-->
                <!--<item name="config" xsi:type="array">-->
                    <!--<item name="dataType" xsi:type="string">text</item>-->
                    <!--<item name="formElement" xsi:type="string">input</item>-->
                    <!--<item name="label" xsi:type="string">Payment Amount</item>-->
                    <!--<item name="sortOrder" xsi:type="number">40</item>-->
                    <!--<item name="validation" xsi:type="array">-->
                        <!--<item name="required-entry" xsi:type="boolean">true</item>-->
                        <!--<item name="validate-number" xsi:type="boolean">true</item>-->
                        <!--<item name="validate-greater-than-zero" xsi:type="boolean">true</item>-->
                    <!--</item>-->
                    <!--<item name="dataScope" xsi:type="string">payment_amount</item>-->
                <!--</item>-->
            <!--</argument>-->
        <!--</field>-->
        <!--<field name="description">-->
            <!--<argument name="data" xsi:type="array">-->
                <!--<item name="config" xsi:type="array">-->
                    <!--<item name="dataType" xsi:type="string">text</item>-->
                    <!--<item name="formElement" xsi:type="string">textarea</item>-->
                    <!--<item name="label" xsi:type="string">Description</item>-->
                    <!--<item name="sortOrder" xsi:type="number">50</item>-->
                    <!--<item name="dataScope" xsi:type="string">description</item>-->
                <!--</item>-->
            <!--</argument>-->
        <!--</field>-->
    <!--</fieldset>-->
</form>
