<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

?>

<div id="purchase_email_items_block" class="purchase_email"
     data-modal-info="<?php /* @escapeNotVerified */ echo $block->getWidgetOptions();?>"
     style="width: 100%">
    <h5><?php echo __('Items Returned')?></h5>
    <div class="admin__page-section-item order-items" style="width: 100%">
        <table class="data-table admin__table-secondary order-subtotal-table"
               cellspacing="0" cellpadding="0" border="0" style="margin:0; padding:0; width: 100%">
            <thead>
            <tr style="font-size:11px;">
                <th align="left" bgcolor="#EAEAEA"
                    style="padding:3px 9px; font-size:11px;"><?php echo __('Product') ?></th>
                <th align="left" bgcolor="#EAEAEA" style="padding:3px 9px; font-size:11px;"><?php echo __('SKU') ?></th>
                <th align="center" bgcolor="#EAEAEA"
                    style="padding:3px 9px; font-size:11px;"><?php echo __('Supplier SKU') ?></th>
                <th align="center" bgcolor="#EAEAEA" style="padding:3px 9px; font-size:11px;"><?php echo __('Qty') ?></th>
            </tr>
            </thead>
            <?php $items = $block->getReturnOrderItems(); $i = 0;
            foreach ($items as $id => $item): ?>
                <tbody<?php echo $i % 2 ? ' bgcolor="#F6F6F6"' : '' ?>>
                <tr>
                    <td align="left" valign="top" style="font-size:11px; padding:3px 9px; border-bottom:1px dotted #CCCCCC;">
                        <strong style="font-size:11px;"><?php echo $block->escapeHtml($item->getProductName()) ?></strong>
                    </td>
                    <td align="left" valign="top" style="font-size:11px; padding:3px 9px; border-bottom:1px dotted #CCCCCC;">
                        <?php echo $block->escapeHtml($item->getProductSku()) ?>
                    </td>
                    <td align="center" valign="top" style="font-size:11px; padding:3px 9px; border-bottom:1px dotted #CCCCCC;">
                        <?php echo $item->getProductSupplierSku() ?>
                    </td>
                    <td align="center" valign="top" style="font-size:11px; padding:3px 9px; border-bottom:1px dotted #CCCCCC;">
                        <?php echo $item->getQtyReturned() * 1?>
                    </td>
                </tr>
                </tbody>
                <?php $i++; ?>
            <?php endforeach; ?>
        </table>
    </div>
</div>