<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\PurchaseOrderSuccess\Ui\DataProvider\PurchaseOrder\Form\Modifier\PurchaseSumary;

use Magento\Bundle\Model\Product\Type as BundleType;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\GroupedProduct\Model\Product\Type\Grouped;
use Magento\Downloadable\Model\Product\Type as DownloadableType;
use Magestore\SupplierSuccess\Api\Data\SupplierProductInterface;
use Magestore\PurchaseOrderSuccess\Api\Data\PurchaseOrderItemInterface;

/**
 * Class AllSupplierProductDataProvider
 *
 * Data provider for all supplier products list of PO
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class AllSupplierProductDataProvider extends \Magento\Ui\DataProvider\AbstractDataProvider
{
    /**
     * @var \Magestore\SupplierSuccess\Model\ResourceModel\Supplier\Product\Collection
     */
    protected $collection;

    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $request;

    /**
     * @var \Magestore\SupplierSuccess\Service\Supplier\ProductService
     */
    protected $supplierProductService;

    /**
     * @var \Magestore\PurchaseOrderSuccess\Service\PurchaseOrder\Item\ItemService
     */
    protected $purchaseItemService;

    /**
     * @var \Magestore\PurchaseOrderSuccess\Service\Config\ProductConfig
     */
    protected $productConfig;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory
     */
    protected $productCollectionFactory;

    public $filterMap = [
        'product_id' => 'entity_id',
        'product_sku' => 'sku',
        'product_name' => 'name'
    ];

    /**
     * Constructor
     *
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magestore\SupplierSuccess\Service\Supplier\ProductService $supplierProductService
     * @param \Magestore\PurchaseOrderSuccess\Service\PurchaseOrder\Item\ItemService $purchaseItemService
     * @param \Magestore\PurchaseOrderSuccess\Service\Config\ProductConfig $productConfig
     * @param \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory
     * @param array $meta
     * @param array $data
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        \Magento\Framework\App\RequestInterface $request,
        \Magestore\SupplierSuccess\Service\Supplier\ProductService $supplierProductService,
        \Magestore\PurchaseOrderSuccess\Service\PurchaseOrder\Item\ItemService $purchaseItemService,
        \Magestore\PurchaseOrderSuccess\Service\Config\ProductConfig $productConfig,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->request = $request;
        $this->supplierProductService = $supplierProductService;
        $this->purchaseItemService = $purchaseItemService;
        $this->productConfig = $productConfig;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->collection = $this->getAllSupplierProductCollection();
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (!$this->getCollection()->isLoaded()) {
            $this->getCollection()->load();
        }
        if ($this->checkProductSource()) {
            $data = $this->getCollection()->toArray();
        } else {
            $collection = $this->getCollection();
            $items = $collection->toArray();
            foreach ($items as &$item) {
                // initialize primary field name
                $item['supplier_product_id'] = $item['product_id'];
                $item['product_name'] = $item['name'];
            }
            $data = [
                'totalRecords' => $collection->getSize(),
                'items' => array_values($items)
            ];
        }
        return $data;
    }

    /**
     * Get All Supplier Product Collection
     *
     * @return \Magestore\SupplierSuccess\Model\ResourceModel\Supplier\Product\Collection $collection
     */
    public function getAllSupplierProductCollection()
    {
        $supplierId = $this->request->getParam('supplier_id', null);
        $purchaseId = $this->request->getParam('purchase_id', null);
        $excludeTypes = [
            BundleType::TYPE_CODE,
            Configurable::TYPE_CODE,
            Grouped::TYPE_CODE,
            DownloadableType::TYPE_DOWNLOADABLE,
            'customsale'
        ];
        if ($this->checkProductSource()) {
            $collection = $this->supplierProductService->getProductsBySupplierId($supplierId);
            $collection->getSelect()->joinInner(
                ['catalog_product_entity' => $collection->getTable('catalog_product_entity')],
                $collection->getConnection()->quoteInto(
                    "main_table.product_id = catalog_product_entity.entity_id AND
                    catalog_product_entity.type_id NOT IN (?)",
                    $excludeTypes
                ),
                []
            );
        } else {
            $collection = $this->productCollectionFactory->create();
            $collection->addAttributeToSelect(['name']);
            $collection->getSelect()
                ->columns(['product_id' => 'entity_id'])
                ->columns(['product_sku' => 'sku']);
            $collection->addFieldToFilter('type_id', ['nin' => $excludeTypes]);
        }
        if ($purchaseId) {
            $productIds = $this->purchaseItemService->getProductsByPurchaseOrderId($purchaseId)
                ->getColumnValues(PurchaseOrderItemInterface::PRODUCT_ID);
            if (!empty($productIds)) {
                if ($this->checkProductSource()) {
                    $collection->addFieldToFilter(SupplierProductInterface::PRODUCT_ID, ['nin' => $productIds]);
                } else {
                    $collection->addFieldToFilter('entity_id', ['nin' => $productIds]);
                }
            }
        }
        return $collection;
    }

    /**
     * Set Query limit
     *
     * @param int $offset
     * @param int $size
     * @return void
     */
    public function setLimit($offset, $size)
    {
        $this->getCollection()->setPageSize($size);
        $this->getCollection()->setCurPage($offset);
    }

    /**
     * Add Filter
     *
     * @param \Magento\Framework\Api\Filter $filter
     * @return void
     */
    public function addFilter(\Magento\Framework\Api\Filter $filter)
    {
        if (isset($this->filterMap[$filter->getField()]) && !$this->checkProductSource()) {
            $filter->setField($this->filterMap[$filter->getField()]);
        }
        parent::addFilter($filter);
    }

    /**
     * Add Order
     *
     * @param string $field
     * @param string $direction
     * @return void
     */
    public function addOrder($field, $direction)
    {
        if (isset($this->filterMap[$field]) && !$this->checkProductSource()) {
            $this->getCollection()->addOrder($this->filterMap[$field], $direction);
        } else {
            $this->getCollection()->addOrder($field, $direction);
        }
    }

    /**
     * Check Product Source
     *
     * @return bool
     */
    public function checkProductSource()
    {
        return (boolean)($this->productConfig->getProductSource() ==
            \Magestore\PurchaseOrderSuccess\Model\System\Config\ProductSource::TYPE_SUPPLIER);
    }
}
