<?php
/**
 * Copyright © Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\PurchaseOrderSuccess\Ui\Component\Listing\Columns\PurchaseOrder;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Ui\Component\Listing\Columns;
use Magento\Ui\Component\Listing\Columns\Column;
use Magestore\PurchaseOrderSuccess\Model\System\Config\ProductSource;
use Magestore\PurchaseOrderSuccess\Service\Config\ProductConfig;

/**
 * PurchaseOrder - AbstractColumns
 */
class AbstractColumns extends Columns
{

    /**
     * @var ProductConfig
     */
    protected $productConfig;

    protected $listColumnsSupplier = ['cost', 'product_supplier_sku'];

    /**
     * @param ContextInterface $context
     * @param ProductConfig $productConfig
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        ProductConfig $productConfig,
        array $components = [],
        array $data = []
    ) {
        parent::__construct($context, $components, $data);
        $this->productConfig = $productConfig;
    }

    /**
     * @inheritDoc
     */
    public function prepare()
    {
        parent::prepare();

        $this->_prepareColumns();
    }

    /**
     * Prepare Columns
     *
     * @return void
     */
    protected function _prepareColumns()
    {
        foreach ($this->components as $id => $column) {
            if ($column instanceof Column) {
                if (!$this->checkProductSource() && in_array($id, $this->listColumnsSupplier)) {
                    unset($this->components[$id]);
                }
            }
        }
    }

    /**
     * Check Product Source
     *
     * @return bool
     */
    public function checkProductSource()
    {
        return (boolean)($this->productConfig->getProductSource() == ProductSource::TYPE_SUPPLIER);
    }
}
