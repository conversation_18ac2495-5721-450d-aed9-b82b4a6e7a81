<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_fulfilsuccess_recent_picked_listing.pickrequest_listing_data_source</item>
            <item name="deps" xsi:type="string">os_fulfilsuccess_recent_picked_listing.recent_picked_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">pickrequest_listing_template_columns</item>
    </argument>
    <dataSource name="recent_picked_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">recent_picked_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">pick_request_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">pick_request_id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>

    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
                <item name="sticky" xsi:type="boolean">false</item>
            </item>
        </argument>
        <paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magestore_FulfilSuccess/js/pickrequest/grid/massactions/popup</item>
                </item>
            </argument>
            <action name="print-order-picked-item">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">print-order-item</item>
                        <item name="label" xsi:type="string" translate="true">Print Picked Items</item>
                        <item name="popup" xsi:type="boolean">true</item>
                        <item name="url" xsi:type="url" path="fulfilsuccess/pickRequest/massPrintOrderItems"/>
                    </item>
                </argument>
            </action>
        </massaction>
    </container>

    <columns name="pickrequest_listing_template_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">os_fulfilsuccess_recent_picked_listing.os_fulfilsuccess_recent_picked_listing.pickrequest_listing_template_columns.ids</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
                <item name="editorConfig" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">os_fulfilsuccess_recent_picked_listing.os_fulfilsuccess_recent_picked_listing.pickrequest_listing_template_columns.ids</item>
                    <item name="enabled" xsi:type="boolean">true</item>
                    <item name="indexField" xsi:type="string">pick_request_id</item>
                </item>
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">os_fulfilsuccess_recent_picked_listing.os_fulfilsuccess_recent_picked_listing.pickrequest_listing_template_columns.actions</item>
                        <item name="target" xsi:type="string">applyAction</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">edit</item>
                            <item name="1" xsi:type="string">${ $.$data.rowIndex }</item>
                        </item>
                    </item>
                    <item name="controlVisibility" xsi:type="boolean">true</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">os_fulfilsuccess_recent_picked_listing.os_fulfilsuccess_recent_picked_listing.listing_top.bookmarks</item>
                        <item name="root" xsi:type="string">columns.${ $.index }</item>
                        <item name="namespace" xsi:type="string">current.${ $.storageConfig.root}</item>
                    </item>
                </item>
            </item>
        </argument>

        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">pick_request_id</item>
                    <item name="sortOrder" xsi:type="number">0</item>
                    <item name="preserveSelectionsOnFilter" xsi:type="boolean">true</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="pick_request_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Pick #</item>
                    <item name="sortOrder" xsi:type="number">1</item>
                </item>
            </argument>
        </column>
        <column name="pack_request_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Pack #</item>
                    <item name="sortOrder" xsi:type="number">5</item>
                </item>
            </argument>
        </column>        
        <column name="age">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magestore_FulfilSuccess/js/grid/columns/age</item>
                    <item name="filter" xsi:type="string">false</item>
                    <item name="sorting" xsi:type="string">DESC</item>
                    <item name="label" xsi:type="string" translate="true">Age</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </column>
        <column name="increment_id" class="Magestore\FulfilSuccess\Ui\Component\Listing\Columns\PickRequest\Renderer\OrderIncrementId">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">order_id</item>
                    <item name="filter" xsi:type="string">text</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/actions</item>
                    <item name="draggable" xsi:type="boolean">true</item>
                    <item name="label" xsi:type="string" translate="true">Order #</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                    <item name="fieldClass" xsi:type="array">
                        <item name="data-grid-actions-cell" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="batch_id" class="Magestore\FulfilSuccess\Ui\Component\Listing\Columns\PickRequest\Renderer\BatchNumber">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magestore\FulfilSuccess\Model\PickRequest\Batch\Options</item>
                <item name="config" xsi:type="array">
                    <item name="indexField" xsi:type="string">batch_id</item>
                    <item name="filter" xsi:type="string">select</item>
                    <item name="component" xsi:type="string">Magestore_FulfilSuccess/js/pickrequest/grid/filter-action</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="draggable" xsi:type="boolean">true</item>
                    <item name="label" xsi:type="string" translate="true">Batch #</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                    <item name="fieldClass" xsi:type="array">
                        <item name="data-grid-actions-cell" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </column>
        <column name="purchased_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Purchased Date</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                </item>
            </argument>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Picked Date</item>
                    <item name="sortOrder" xsi:type="number">45</item>
                    <item name="sorting" xsi:type="string">DESC</item>
                </item>
            </argument>
        </column>
        <column name="shipping_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Ship-to Name</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                </item>
            </argument>
        </column>
        <column name="customer_email">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Email</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                </item>
            </argument>
        </column>
        <column name="base_grand_total" class="Magento\Catalog\Ui\Component\Listing\Columns\Price">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Grand Total (Base)</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                </item>
            </argument>
        </column>
        <column name="total_items">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">Total Items</item>
                    <item name="sortOrder" xsi:type="number">80</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="show_order" class="Magestore\FulfilSuccess\Ui\Component\Listing\Columns\PickRequest\Detail">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magestore_FulfilSuccess/js/pickrequest/grid/columns/detail</item>
                    <item name="indexField" xsi:type="string">pick_request_id</item>
                    <item name="sortable" xsi:type="boolean">false</item>
                    <item name="label" xsi:type="string" translate="true">View</item>
                    <item name="sortOrder" xsi:type="number">100</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>