<?xml version="1.0"?>
<!--
  ~ Magestore
  ~
  ~ NOTICE OF LICENSE
  ~
  ~ This source file is subject to the Magestore.com license that is
  ~ available through the world-wide-web at this URL:
  ~ http://www.magestore.com/license-agreement.html
  ~
  ~ DISCLAIMER
  ~
  ~ Do not edit or add to this file if you wish to upgrade this extension to newer
  ~ version in the future.
  ~
  ~ @category    Magestore
  ~ @package     Magestore_Customercredit
  ~ @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
  ~ @license     http://www.magestore.com/license-agreement.html
  ~
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="adminhtml_customer_save_after">
        <observer name="credit_save_after" instance="Magestore\Customercredit\Observer\CustomerSaveAfter"/>
    </event>
    <event name="sales_order_load_after">
        <observer name="customercredit_sales_order_load_after" instance="Magestore\Customercredit\Observer\SalesOrderLoadAfter"/>
    </event>
    <event name="catalog_product_save_before">
        <observer name="customercredit_catalog_product_save_before" instance="Magestore\Customercredit\Observer\Adminhtml\ProductSaveBefore"/>
    </event>
    <event name="controller_action_catalog_product_save_entity_after">
        <observer name="customercredit_catalog_product_save_after" instance="Magestore\Customercredit\Observer\Adminhtml\ProductSaveAfter"/>
    </event>
</config>
 