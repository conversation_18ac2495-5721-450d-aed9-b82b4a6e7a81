<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Customercredit
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

/**
 * @var $block Magestore\Customercredit\Block\Sharecredit\Validatecustomer
 */
?>
<?php if ($block->getVerifyEnable()): ?>
    <?php
    $email = $this->getRequest()->getParam('customercredit_email_input');
    $amount_credit = $this->getRequest()->getParam('customercredit_value_input');
    $is_check_email = $this->getRequest()->getParam('check_send_email');
    $description = $this->getRequest()->getParam('customer-credit-share-message');
    $code_id = $this->getRequest()->getParam('id');
    ?>
    <div class="box-account box-info box-customercredit-share">
        <div class="title-credit">
            <h2 class="title-credit-label"><?php echo __('Send Credit to friends') ?></h2>
        </div>
        <div class="customercredit_show_verify_code" id="customercredit_show_verify_code">
            <form action="<?php echo $block->getFormActionUrl(); ?>" method="post" id="verify-code-form">
                <p style="margin-bottom: 5px;">
                    <strong>
                        <?php echo __('Enter verification code from your email:') ?>
                    </strong>
                </p>

                <div class="input-box" style="margin-bottom: 15px;float: left;">
                    <input type="text" class="input-text required-entry form-control" id="customer_credit_code" name="customercreditcode" value="<?php echo $block->getVerifyCode(); ?>"/>
                    <input type="hidden" value="<?php echo $email; ?>" id="customercredit_value_emailhide" name="email_hide"/>
                    <input type="hidden" value="<?php echo $amount_credit; ?>" id="customercredit_value_amounthide" name="amount_hide"/>
                    <input type="hidden" value="<?php echo $description; ?>" id="customercredit_value_messagehide" name="message_hide"/>
                    <?php if (isset($code_id)): ?>
                        <input type="hidden" value="<?php echo $code_id; ?>" id="customercredit_credir_code_id" name="credit_code_id_hide"/>
                    <?php endif; ?>
                </div>
                <button class="submit-code button" title="<?php echo __('Submit Code') ?>" type="submit">
                    <span><span><?php echo __('Submit Code') ?></span></span>
                </button>
            </form>
        </div>
    </div>
    <script type="text/javascript">
        var dataForm = new VarienForm('verify-code-form', true);
    </script>
    <div class="box-account box-info box-customercredit-history" style="float: left;margin-top: 10px">
        <div class="title-credit">
            <h2 class="title-credit-label"><?php echo __('Credit Code List') ?></h2>
        </div>
        <?php echo $this->getChildHtml() ?>
    </div>
    <?php

endif ?>