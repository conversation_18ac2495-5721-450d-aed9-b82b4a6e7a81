<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Customercredit
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\Customercredit\Plugin\Adminhtml\Block\Customer;

use Magento\Framework\Registry;
use Magento\Framework\View\Element\UiComponent\Context;

/**
 * Customer - BackButton
 */
class BackButton
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var Registry
     */
    protected $registry;

    /**
     * BackButton constructor
     *
     * @param Context $context
     * @param Registry $registry
     */
    public function __construct(
        Context $context,
        Registry $registry
    ) {
        $this->context = $context;
        $this->registry = $registry;
    }

    /**
     * After Get Button Data
     *
     * @param \Magento\Customer\Block\Adminhtml\Edit\BackButton $button
     * @param array $result
     * @return array|mixed
     */
    public function afterGetButtonData(\Magento\Customer\Block\Adminhtml\Edit\BackButton $button, $result)
    {
        $type = $this->context->getRequestParam('type');
        if ($type == 'customercredit') {
            return [
                'label' => __('Back'),
                'on_click' => sprintf("location.href = '%s';", $button->getUrl('customercreditadmin/customercredit/')),
                'class' => 'back',
                'sort_order' => 10
            ];
        }

        return $result;
    }
}
