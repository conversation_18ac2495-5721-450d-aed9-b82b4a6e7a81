<!--
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_RewardPoints
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
/**
 * @category    Magestore
 * @package     Magestore_RewardPoints
 * <AUTHOR> Developer
 */
-->
<?php
/** @var \Magestore\Rewardpoints\Block\Checkout\Cart\Point $block */
/** @var Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
$data = \Laminas\Json\Json::encode($block->knockoutData());
$script = "var rewardpointConfig = $data";
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', ['type' => 'text/javascript'], $script, false)?>
