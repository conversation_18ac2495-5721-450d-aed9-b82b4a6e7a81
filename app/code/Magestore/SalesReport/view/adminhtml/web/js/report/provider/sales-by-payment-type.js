/**
 * Copyright © Magestore, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'Magestore_SalesReport/js/report/data-provider',
    'jquery',
    'Magestore_SalesReport/js/lib/color-scheme.min',
    'mage/translate',
    'Magestore_SalesReport/js/report/chart'
], function (GridProvider, $, ColorScheme, __) {
    'use strict';


    let currentMetric = 'number_of_selling_transactions';

    window.reportData = null;
    window.reportChart = null;
    window.createChart = function (data, metric) {
        window.reportData = data;
        if (window.reportData.items && window.reportData.items.length) {
            const reportBy = 'payment_method';
            const baseCurrencySymbol = window.reportFilters.base_currency;
            let labels = [];
            let value = [];
            let reportType = 'bar';
            let scheme = new ColorScheme();
            scheme.from_hex('f1d4b3')
                .scheme('analogic')
                .variation('hard')
                .web_safe(false);
            let colors = scheme.colors().map((color) => {
                return "#" + color;
            });
            let metricLabels = {
                'total_paid_amount_value': __("Total Paid"),
                'total_paid_percent_value': __("% Total Paid"),
                'number_of_selling_transactions': __("Number of Selling Transactions"),
                'avg_transaction_value': __("Average Transaction Value"),
                'number_of_refund_transactions': __("Number of Refunding Transactions"),
                'total_subtotal_amount_value': __('Subtotal'),
                'total_grand_total_amount_value': __('Grand Total'),
                'total_tax_amount_value': __('Tax'),
                'total_discount_amount_value': __('Discount'),
                'total_refunded_amount_value': __('Refunded'),
                'total_shipping_amount_value': __('Shipping (excl. Tax)'),
            };
            let scaleOptions = {};
            let generateScalesOptions = (digit, withSymbol = false) => {
                return {
                    x: {
                        title: {
                            display: true,
                            text: __('Payment Type'),
                            font: {
                                size: 20,
                                weight: 'bold',
                                lineHeight: 1.2
                            },
                            padding: {top: 20, left: 0, right: 0, bottom: 0}
                        }
                    },
                    y: {
                        ticks: {
                            format: {maximumFractionDigits: digit, minimumFractionDigits: digit}
                        },
                        title: {
                            display: true,
                            text:  withSymbol ? `${metricLabels[metric]} (${baseCurrencySymbol})` : `${metricLabels[metric]}`,
                            font: {
                                size: 20,
                                weight: 'bold',
                                lineHeight: 1.2
                            },
                        }
                    }
                }
            };
            switch (metric) {
                case 'total_paid_percent_value':
                    reportType = 'pie';
                    break;
                case 'number_of_selling_transactions':
                case 'number_of_refund_transactions':
                    reportType = 'bar';
                    scaleOptions = generateScalesOptions(0);
                    break;
                default:
                    reportType = 'bar';
                    scaleOptions = generateScalesOptions(2, true);
            }
            window.reportData.items.forEach(item => {
                labels.push(item[reportBy]);
                value.push(item[metric]);
            })

            let chartData = {
                labels: labels,
                datasets: [{
                    label: window.metricOptions[metric],
                    data: value,
                    backgroundColor: reportType === 'pie' ? colors : '#f1d4b3',
                    borderColor: '#eb5202',
                    borderWidth: 1
                }]
            };
            if (!window.reportChart) {
                window.reportChart = $.mage.reportChart({data: chartData, type: reportType, scaleOptions: scaleOptions}, '#reportChart');
            } else {
                window.reportChart.refreshChartData(chartData, reportType, scaleOptions);
            }
        }
    }
    window.changeChartMetric = function(value) {
        currentMetric = value;
        window.createChart(window.reportData, value);
    }

    return GridProvider.extend({
        /**
         * Handles successful data reload.
         *
         * @param {Object} data - Retrieved data object.
         */
        onReload: function (data) {
            this.firstLoad = false;
            this.set('lastError', false);
            this.setData(data)
                .trigger('reloaded');

            if (this.triggerDataReload) {
                this.triggerDataReload = false;
                this.reload();
            }

            let cloneData = {...data};
            cloneData.items.pop();
            if (cloneData.items && cloneData.items.length) {
                $("#report-chart").show();
                window.createChart(
                    cloneData,
                    currentMetric
                );
            } else {
                $("#report-chart").hide();
            }
        },
    });
});
