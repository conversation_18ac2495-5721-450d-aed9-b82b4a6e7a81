<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2024 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         class="ReportSalesByProductFeatureListing">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                sales_report_reports_salesbyproductfeature_item_attribute_set_listing.sales_report_reports_salesbyproductfeature_item_listing_data_source
            </item>
        </item>
    </argument>
    <settings>
        <spinner>sales_report_reports_salesbyproductfeature_item_attribute_set_listing_columns</spinner>
        <deps>
            <dep>
                sales_report_reports_salesbyproductfeature_item_attribute_set_listing.sales_report_reports_salesbyproductfeature_item_listing_data_source
            </dep>
        </deps>
    </settings>
    <dataSource name="sales_report_reports_salesbyproductfeature_item_listing_data_source"
                component="Magestore_SalesReport/js/report/provider/sales-by-product-feature">
        <settings>
            <updateUrl path="mui/index/render"/>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
                <param name="cacheRequests" xsi:type="boolean">false</param>
            </storageConfig>
        </settings>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider"
                      name="sales_report_reports_salesbyproductfeature_item_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <columnsControls name="columns_controls"/>
        <paging name="listing_paging"/>
        <bookmark name="bookmarks"/>
        <exportButton name="export_button" class="Magestore\SalesReport\Ui\Component\Listing\ExportButton">
            <settings>
                <additionalParams>
                    <param xsi:type="string" name="reportFilters">${ $.provider }:params.reportFilters</param>
                </additionalParams>
                <options>
                    <option name="csv" xsi:type="array">
                        <item name="url" xsi:type="string">sales_report/reports_export/reportToCsv</item>
                    </option>
                </options>
            </settings>
        </exportButton>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                </item>
            </argument>
        </filters>
    </listingToolbar>
    <columns name="sales_report_reports_salesbyproductfeature_item_attribute_set_listing_columns">
        <selectionsColumn name="ids">
            <settings>
                <indexField>id</indexField>
                <preserveSelectionsOnFilter>true</preserveSelectionsOnFilter>
                <visible>false</visible>
            </settings>
        </selectionsColumn>
        <column name="attribute_set_name">
            <settings>
                <label translate="true">Attribute Set</label>
                <filter>text</filter>
            </settings>
        </column>
        <column name="total_revenue_amount_incl_tax" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Revenue (incl. Tax)</label>
            </settings>
        </column>
        <column name="total_revenue_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Revenue (excl. Tax)</label>
                <filter>textRange</filter>
            </settings>
        </column>
        <column name="total_cost_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">COGS</label>
            </settings>
        </column>
        <column name="total_profit_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Profit</label>
            </settings>
        </column>
        <column name="margin" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Percent">
            <settings>
                <label translate="true">Margin (%)</label>
            </settings>
        </column>
        <column name="total_refunded_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Refunded</label>
                <filter>textRange</filter>
            </settings>
        </column>
        <column name="total_revenue_percent" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Percent">
            <settings>
                <label translate="true">% Total Revenue</label>
                <sorting>desc</sorting>
            </settings>
        </column>
        <column name="total_qty_sold" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Column">
            <settings>
                <label translate="true">Qty Sold</label>
            </settings>
        </column>
        <column name="avg_selling_price" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Average Selling Price</label>
                <filter>textRange</filter>
            </settings>
        </column>
        <column name="total_qty_refunded" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Column">
            <settings>
                <label translate="true">Qty Refunded</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="total_invoiced_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Invoiced</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="total_qty_invoiced" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Column">
            <settings>
                <label translate="true">Qty Invoiced</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="total_discount_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Discount</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="total_tax_amount" class="Magestore\SalesReport\Ui\Component\Listing\Column\Aggregated\Price">
            <settings>
                <label translate="true">Tax</label>
                <visible>false</visible>
            </settings>
        </column>
        <column name="top_selling_products" class="Magestore\SalesReport\Ui\Component\Listing\Column\TopSellingProducts">
            <settings>
                <label translate="true">Top Selling Product</label>
                <visible>false</visible>
                <bodyTmpl>ui/grid/cells/html</bodyTmpl>
            </settings>
        </column>
    </columns>
</listing>
