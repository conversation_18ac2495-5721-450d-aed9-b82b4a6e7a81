<?php
/*
 * Copyright © 2024 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\SalesReport\Model\ResourceModel\Report;

use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Select;
use Magestore\Payment\Model\Payment\Method\MultiPayment;
use Magestore\SalesReport\Api\Statistics\SalesReportStatisticsInterface;

/**
 * Class for Sales by payment type
 */
class SalesByPaymentType extends AbstractReport implements SalesReportStatisticsInterface
{
    /**
     * Model initialization
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('magestore_sales_by_payment_type_aggregated', 'id');
    }

    /**
     * @inheritDoc
     */
    public function aggregate($from = null, $to = null)
    {
        parent::aggregate($from, $to);
        $this->_setFlagData(\Magestore\SalesReport\Model\Flag::REPORT_SALES_BY_PAYMENT_TYPE_FLAG_CODE);
        return $this;
    }

    /**
     * Aggregate by field
     *
     * @param string $aggregationField
     * @param string|int|\DateTime|array|null $from
     * @param string|int|\DateTime|array|null $to
     * @throws \Exception
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function aggregateByField($aggregationField, $from, $to)
    {
        $this->aggField = $aggregationField;
        $connection = $this->getConnection();
        $connection->beginTransaction();
        try {
            if ($from !== null || $to !== null) {
                $subSelect = $this->_getTableDateRangeSelect(
                    $this->getTable('sales_order'),
                    $aggregationField,
                    $aggregationField,
                    $from,
                    $to
                );
            } else {
                $subSelect = null;
            }
            $this->timezoneOffsetQuery = $this->getStoreTZOffsetQuery(
                ['sales_order' => $this->getTable('sales_order')],
                'sales_order.' . $aggregationField,
                $from,
                $to
            );
            $magentoPayment = $this->getMagentoPayments($connection, $subSelect);
            $connection->query($magentoPayment->insertFromSelect(
                $this->getMainTable(),
                array_keys($this->getColumns())
            ));
            $posPayments = $this->getPosPayments($connection, $subSelect);
            $connection->query($posPayments->insertFromSelect(
                $this->getMainTable(),
                array_keys($this->getWebPosColumns())
            ));
            $connection->commit();
        } catch (\Exception $e) {
            $connection->rollBack();
            throw $e;
        }
    }

    /**
     * Get magento payments
     *
     * @param AdapterInterface $connection
     * @param Select $subSelect
     * @return Select
     */
    public function getMagentoPayments($connection, $subSelect)
    {
        $select = $connection->select();
        $columns = $this->getColumns();
        $select->from(
            ['sales_order' => $this->getTable('sales_order')],
            $columns
        )->where(
            'sales_order.state NOT IN (?)',
            [\Magento\Sales\Model\Order::STATE_CANCELED]
        );
        $select->join(
            ['sales_order_payment' => $this->getPayments()],
            'sales_order_payment.order_id = sales_order.entity_id',
            []
        );
        // join invoice transaction
        $select->join(
            ['sales_invoice_transaction' => $this->getInvoiceTransaction()],
            'sales_order.entity_id = sales_invoice_transaction.order_id',
            []
        );
        // join refund transaction
        $select->joinLeft(
            ['sales_creditmemo_transaction' => $this->getRefundTransaction()],
            'sales_order.entity_id = sales_creditmemo_transaction.order_id',
            []
        );
        if ($subSelect !== null) {
            $select->having($this->_makeConditionFromDateRangeSelect($subSelect, 'period'));
        }
        $groupArr = $this->getGroupByArray();
        $select->group($groupArr);
        return $select;
    }

    /**
     * Get pos payments
     *
     * @param AdapterInterface $connection
     * @param Select $subSelect
     * @return Select
     */
    public function getPosPayments($connection, $subSelect)
    {
        $select = $connection->select();
        $columns = $this->getWebPosColumns();
        $select->from(
            ['sales_order' => $this->getTable('sales_order')],
            $columns
        )->where(
            'sales_order.state NOT IN (?)',
            [\Magento\Sales\Model\Order::STATE_CANCELED]
        );
        $select->join(
            ['webpos_order_payment' => $this->getWebPosPayment()],
            'webpos_order_payment.order_id = sales_order.entity_id',
            []
        );
        if ($subSelect !== null) {
            $select->having($this->_makeConditionFromDateRangeSelect($subSelect, 'period'));
        }
        $groupArr = $this->getWebposGroupByArray();
        $select->group($groupArr);
        return $select;
    }

    /**
     * @inheritDoc
     */
    public function getColumns(): array
    {
        $columns = parent::getColumns();
        $method = 'sales_order_payment.payment_method';
        $baseAmountPaid = 'SUM(sales_order_payment.total_paid_amount)';
        // invoice transaction
        $sellingTransactions = 'SUM(sales_invoice_transaction.number_transaction)';
        //refund transaction
        $refundTransactions = 'SUM(IFNULL(sales_creditmemo_transaction.number_transaction, 0))';
        $baseRefund = 'SUM(sales_order_payment.total_refunded_amount)';

        return [
            'date_used' => $columns['date_used'],
            'sales_channel' => $columns['sales_channel'],
            'period' => $columns['period'],
            'pos_location_id' => $columns['pos_location_id'],
            'store_id' => $columns['store_id'],
            'payment_method' => $method,
            'total_paid_amount' => $baseAmountPaid,
            'total_refunded_amount' => $baseRefund,
            'number_of_selling_transactions' => $sellingTransactions,
            'number_of_refund_transactions' => $refundTransactions,
            'total_grand_total_amount' => $columns['total_grand_total_amount'],
            'total_shipping_amount' => $columns['total_shipping_amount'],
            'total_subtotal_amount' => $columns['total_subtotal_amount'],
            'total_discount_amount' => $columns['total_discount_amount'],
            'total_tax_amount' => $columns['total_tax_amount'],
        ];
    }

    /**
     * Get webpos columns
     */
    public function getWebPosColumns(): array
    {
        $columns = parent::getColumns();
        $method = 'webpos_order_payment.payment_method';
        $baseAmountPaid = 'SUM(webpos_order_payment.total_paid_amount)';
        $refundedAmount = 'SUM(webpos_order_payment.total_refunded_amount)';
        // invoice transaction
        $sellingTransactions = 'SUM(webpos_order_payment.number_of_selling_transactions)';
        $refundedTransactions = 'SUM(webpos_order_payment.number_of_refunded_transactions)';
        return [
            'date_used' => $columns['date_used'],
            'sales_channel' => $columns['sales_channel'],
            'period' => $columns['period'],
            'pos_location_id' => $columns['pos_location_id'],
            'store_id' => $columns['store_id'],
            'payment_method' => $method,
            'number_of_selling_transactions' => $sellingTransactions,
            'number_of_refund_transactions' => $refundedTransactions,
            'total_paid_amount' => $baseAmountPaid,
            'total_refunded_amount' => $refundedAmount,
            'total_grand_total_amount' => $columns['total_grand_total_amount'],
            'total_shipping_amount' => $columns['total_shipping_amount'],
            'total_subtotal_amount' => $columns['total_subtotal_amount'],
            'total_discount_amount' => $columns['total_discount_amount'],
            'total_tax_amount' => $columns['total_tax_amount']
        ];
    }

    /**
     * @inheritDoc
     */
    public function getGroupByArray()
    {
        $group = parent::getGroupByArray();
        $moreColumns = [
            'payment_method' => 'sales_order_payment.payment_method'
        ];
        return array_merge($moreColumns, $group);
    }

    /**
     * Get Webpos group by array
     */
    public function getWebposGroupByArray()
    {
        $group = parent::getGroupByArray();
        $moreColumns = [
            'payment_method' => 'webpos_order_payment.payment_method'
        ];
        return array_merge($moreColumns, $group);
    }

    /**
     * Join payments
     *
     * @return Select
     */
    public function getPayments()
    {
        $notMultiPaymentPosFilter = $this->getConnection()->prepareSqlCondition(
            'sales_order_payment.method',
            ['neq' => MultiPayment::PAYMENT_CODE]
        );
        $paymentSelect = clone $this->getConnection()->select();
        $paymentSelect->reset();
        $paymentSelect->from(['sales_order_payment' => $this->getTable('sales_order_payment')]);
        $paymentSelect->reset(Select::COLUMNS);
        $paymentSelect->columns(
            [
                'payment_method' => 'sales_order_payment.method',
                'order_id' => 'sales_order_payment.parent_id',
                'total_paid_amount' => 'SUM(sales_order_payment.base_amount_paid)',
                'total_refunded_amount' => 'SUM(sales_order_payment.base_amount_refunded)',
            ]
        );
        $paymentSelect->where($notMultiPaymentPosFilter);
        $paymentSelect->group(['order_id', 'payment_method']);
        return $paymentSelect;
    }

    /**
     * Get webpos payments
     *
     * @return Select
     */
    public function getWebPosPayment()
    {
        $paymentSelect = clone $this->getConnection()->select();
        $paymentSelect->reset();
        $paymentSelect->from(['webpos_order_payment' => $this->getTable('webpos_order_payment')]);
        $paymentSelect->reset(Select::COLUMNS);
        $connection = $this->getConnection();
        $baseAmountExpr = sprintf(
            "SUM(%s)",
            $connection->getCaseSql('type', [0 => 'webpos_order_payment.base_amount_paid'], 0)
        );
        $baseRefundExpr = sprintf(
            "SUM(%s)",
            $connection->getCaseSql('type', [1 => 'webpos_order_payment.base_amount_paid'], 0)
        );
        $numberSellingTransactions = sprintf(
            "COUNT(%s)",
            $connection->getCaseSql('type', [0 => 'webpos_order_payment.base_amount_paid'])
        );
        $numberRefundedTransactions = sprintf(
            "COUNT(%s)",
            $connection->getCaseSql('type', [1 => 'webpos_order_payment.base_amount_paid'])
        );
        $paymentSelect->columns(
            [
                'payment_method' => 'webpos_order_payment.method',
                'order_id' => 'webpos_order_payment.order_id',
                'total_paid_amount' => new \Zend_Db_Expr($baseAmountExpr),
                'total_refunded_amount' => new \Zend_Db_Expr($baseRefundExpr),
                'number_of_selling_transactions' => $numberSellingTransactions,
                'number_of_refunded_transactions' => $numberRefundedTransactions,
            ]
        );
        $paymentSelect->group(['order_id', 'payment_method']);
        return $paymentSelect;
    }

    /**
     * Number invoice transactions
     *
     * @return Select
     */
    public function getInvoiceTransaction()
    {
        $invoiceTransactions = clone $this->getConnection()->select();
        $invoiceTransactions->reset();
        $invoiceTransactions->from(['sales_invoice' => $this->getTable('sales_invoice')]);
        $invoiceTransactions->reset(Select::COLUMNS);
        $invoiceTransactions->columns(
            [
                'order_id' => 'sales_invoice.order_id',
                'number_transaction' => 'COUNT(1)'
            ]
        );
        $invoiceTransactions->group('order_id');
        return $invoiceTransactions;
    }

    /**
     * Get refund transaction
     *
     * @return Select
     */
    public function getRefundTransaction()
    {
        $refundTransactions = clone $this->getConnection()->select();
        $refundTransactions->reset();
        $refundTransactions->from(['sales_creditmemo' => $this->getTable('sales_creditmemo')]);
        $refundTransactions->reset(Select::COLUMNS);
        $refundTransactions->columns(
            [
                'order_id' => 'sales_creditmemo.order_id',
                'number_transaction' => 'COUNT(1)'
            ]
        );
        $refundTransactions->group('order_id');
        return $refundTransactions;
    }
}
