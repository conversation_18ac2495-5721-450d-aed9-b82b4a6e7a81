<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">os_branchrequest_shortfall.os_branchrequest_shortfall_data_source</item>
            <item name="deps" xsi:type="string">os_branchrequest_shortfall.os_branchrequest_shortfall_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">os_branchrequest_shortfall_columns</item>

    </argument>
    <dataSource name="os_branchrequest_delivery_history_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
            <argument name="name" xsi:type="string">os_branchrequest_shortfall_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">entity_id</item>
                    </item>
                </item>
            </argument>
        </argument>
    </dataSource>

    <container name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
                <item name="sticky" xsi:type="boolean">false</item>
            </item>
        </argument>
        <paging name="listing_paging"/>
        <exportButton name="export_button">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="selectProvider" xsi:type="string">os_branchrequest_shortfall.os_branchrequest_shortfall.os_branchrequest_shortfall_columns.ids</item>
                    <item name="component" xsi:type="string">Magestore_BranchRequest/js/grid/export</item>
                </item>
            </argument>
        </exportButton>
    </container>

    <columns name="os_branchrequest_shortfall_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">os_branchrequest_shortfall.os_branchrequest_shortfall.listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
            </item>
        </argument>
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">entity_id</item>
                    <item name="visible" xsi:type="boolean">false</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="product_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">ID</item>
                </item>
            </argument>
        </column>
        <column name="image" class="Magestore\BranchRequest\Ui\Component\Listing\Columns\InventoryTransfer\Columns\Image">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/thumbnail</item>
                    <item name="label" xsi:type="string" translate="true">Thumbnail</item>
                </item>
            </argument>
        </column>
        <column name="product_sku">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">SKU</item>
                </item>
            </argument>
        </column>
        <column name="product_name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Name</item>
                </item>
            </argument>
        </column>
       <!-- <column name="type_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Type</item>
                </item>
            </argument>
        </column>-->
        <column name="racket">
            <settings>
                <filter>text</filter>
                <label translate="true">Racket #</label>
                <sortable>false</sortable>
            </settings>
        </column>
        <column name="qty_requested">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Requested</item>
                </item>
            </argument>
        </column>
        <column name="qty_delivered" class="Magestore\BranchRequest\Ui\Component\Listing\Columns\Qty">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Delivered</item>
                </item>
            </argument>
        </column>
        <column name="qty_received">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Received</item>
                </item>
            </argument>
        </column>
        <column name="qty_not_received">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="label" xsi:type="string" translate="true">Qty Not Received</item>
                </item>
            </argument>
        </column>
    </columns>
</listing>
