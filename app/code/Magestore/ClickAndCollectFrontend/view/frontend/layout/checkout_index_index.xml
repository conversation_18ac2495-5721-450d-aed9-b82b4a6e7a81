<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="root">
            <block class="Magestore\ClickAndCollectFrontend\Block\Checkout\TimePreparePickup"
                   name="clickandcollectaddmoreinputhidden"
                   before="-"
                   template="Magestore_ClickAndCollectFrontend::checkout/clickandcollect_add_more_input_hidden.phtml"
            />
        </referenceContainer>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="click-and-collect" xsi:type="array">
                                            <item name="component" xsi:type="string">
                                                Magestore_ClickAndCollectFrontend/js/view/store-pickup
                                            </item>
                                            <item name="sortOrder" xsi:type="string">0</item>
                                            <item name="deps" xsi:type="array">
                                                <item name="0" xsi:type="string">
                                                    checkout.steps.shipping-step.shippingAddress
                                                </item>
                                            </item>
                                            <item name="children" xsi:type="array">
                                                <item name="store-selector" xsi:type="array">
                                                    <item name="component" xsi:type="string">
                                                        Magestore_ClickAndCollectFrontend/js/view/store-selector
                                                    </item>
                                                    <item name="displayArea" xsi:type="string">store-selector</item>
                                                    <item name="children" xsi:type="array">
                                                        <item name="customer-email" xsi:type="array">
                                                            <item name="component" xsi:type="string">
                                                                Magestore_ClickAndCollectFrontend/js/view/form/element/email
                                                            </item>
                                                            <item name="displayArea" xsi:type="string">customer-email
                                                            </item>
                                                            <item name="tooltip" xsi:type="array">
                                                                <item name="description" xsi:type="string"
                                                                      translate="true">We'll send your order
                                                                    confirmation here.
                                                                </item>
                                                            </item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="before-login-form" xsi:type="array">
                                                                    <item name="component" xsi:type="string">
                                                                        uiComponent
                                                                    </item>
                                                                    <item name="displayArea" xsi:type="string">
                                                                        before-login-form
                                                                    </item>
                                                                    <item name="children" xsi:type="array">
                                                                        <!-- before login form fields -->
                                                                    </item>
                                                                </item>
                                                                <item name="additional-login-form-fields"
                                                                      xsi:type="array">
                                                                    <item name="component" xsi:type="string">
                                                                        uiComponent
                                                                    </item>
                                                                    <item name="displayArea" xsi:type="string">
                                                                        additional-login-form-fields
                                                                    </item>
                                                                    <item name="children" xsi:type="array">
                                                                        <!-- additional login form fields -->
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                    <item name="config" xsi:type="array">
                                                        <item name="popUpList" xsi:type="array">
                                                            <item name="element" xsi:type="string">
                                                                #click-and-collect-store-selector-popup
                                                            </item>
                                                            <item name="options" xsi:type="array">
                                                                <item name="type" xsi:type="string">popup</item>
                                                                <item name="responsive" xsi:type="boolean">true</item>
                                                                <item name="innerScroll" xsi:type="boolean">true</item>
                                                                <item name="title" xsi:type="string" translate="true">
                                                                    Select Store
                                                                </item>
                                                                <item name="trigger" xsi:type="string">
                                                                    click-and-collect-store-selector-popup
                                                                </item>
                                                                <item name="buttons" xsi:type="array"/>
                                                                <item name="modalClass" xsi:type="string">
                                                                    store-selector-popup
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>
