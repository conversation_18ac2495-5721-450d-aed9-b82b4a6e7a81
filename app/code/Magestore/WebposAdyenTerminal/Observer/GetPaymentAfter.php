<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Observer;

use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;

/**
 * Class GetPaymentAfter
 *
 * Get Payment Data
 */
class GetPaymentAfter implements ObserverInterface
{
    /**
     * @var \Magestore\WebposAdyenTerminal\Helper\Data
     */
    public $helper;

    /**
     * @var \Magestore\Webpos\Model\Payment\PaymentFactory
     */
    public $paymentFactory;

    /**
     * GetPaymentAfter constructor.
     *
     * @param \Magestore\WebposAdyenTerminal\Helper\Data $helper
     * @param \Magestore\Webpos\Model\Payment\PaymentFactory $paymentFactory
     */
    public function __construct(
        \Magestore\WebposAdyenTerminal\Helper\Data $helper,
        \Magestore\Webpos\Model\Payment\PaymentFactory $paymentFactory
    ) {
        $this->helper = $helper;
        $this->paymentFactory = $paymentFactory;
    }

    /**
     * Insert Payment Pay pal here
     *
     * @param EventObserver $observer
     */
    public function execute(EventObserver $observer)
    {
        if ($this->helper->isEnableAdyen()) {
            $payments = $observer->getData('payments');
            $paymentList = $payments->getList();
            $paymentList[] = $this->getPaymentData();
            $payments->setList($paymentList);
        }
    }

    /**
     * Get Payment Data
     *
     * @return array
     */
    public function getPaymentData()
    {
        $config = $this->helper->getPaymentConfig(
            [
                'title',
                'sort_order',
                'poiid',
                'server_type',
            ]
        );
        $sortOrder = $config['sort_order'];
        $sortOrder = $sortOrder ? (int)$sortOrder : 0;

        $paymentModel = $this->paymentFactory->create();
        $paymentModel->setCode(\Magestore\WebposAdyenTerminal\Helper\Data::CODE);
        $paymentModel->setTitle($config['title']);
        $paymentModel->setInformation('');
        $paymentModel->setType('0');
        $paymentModel->setIsDefault($this->helper->isDefault());
        /*BEAUTYXL-86: Hide Reference No when checkout with Adyen terminal*/
        $paymentModel->setIsReferenceNumber(0);
        $paymentModel->setIsPayLater(0);
        $paymentModel->setMultiable(1);
        $paymentModel->setSortOrder($sortOrder);

        $paymentModel->setPoiid($config['poiid']);
        $paymentModel->setServerType($config['server_type']);

        return $paymentModel->getData();
    }
}
