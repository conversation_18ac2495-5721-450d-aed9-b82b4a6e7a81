<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON>
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Model;

use Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magestore\Webpos\Api\Sales\OrderRepositoryInterface;
use Magestore\Webpos\Api\Data\Checkout\Order\CommentInterface;

/**
 * Class TerminalCloudService
 *
 * Connect To Terminal Cloud
 */
class TerminalCloudService implements TerminalCloudServiceInterface
{
    /**
     * @var \Magestore\WebposAdyenTerminal\Helper\Data
     */
    public $webposAdyenHelper;

    /**
     * @var Json
     */
    public $serializer;

    /**
     * @var OrderRepositoryInterface
     */
    public $orderRepository;

    /**
     * @var CommentInterface
     */
    public $commentInterface;

    /**
     * TerminalCloudService constructor.
     *
     * @param \Magestore\WebposAdyenTerminal\Helper\Data $webposAdyenHelper
     * @param Json $serializer
     * @param OrderRepositoryInterface $orderRepository
     * @param CommentInterface $commentInterface
     */
    public function __construct(
        \Magestore\WebposAdyenTerminal\Helper\Data $webposAdyenHelper,
        Json $serializer,
        OrderRepositoryInterface $orderRepository,
        CommentInterface $commentInterface
    ) {
        $this->webposAdyenHelper = $webposAdyenHelper;
        $this->serializer = $serializer;
        $this->orderRepository = $orderRepository;
        $this->commentInterface = $commentInterface;
    }

    /**
     * Check Adyen Terminal Enable
     *
     * @return bool
     */
    public function isEnable()
    {
        return $this->webposAdyenHelper->isEnableAdyen();
    }

    /**
     * Check Adyen Terminal Configuration
     *
     * @return string
     */
    public function getConfigurationError()
    {
        $message = '';
        $hasSDK = (class_exists(\Adyen\Service\PosPayment::class)) ? true : false;
        if (!$hasSDK) {
            $message = __('Adyen APIs Library not found, please go to the configuration
            to get the instruction to install the Adyen APIs Library');
        } else {
            if ($this->isEnable()) {
                $configs = $this->webposAdyenHelper->getPaymentConfig(
                    [
                        'merchant_account',
                        'api_key'
                    ]
                );
                if (empty($configs['merchant_account']) || empty($configs['api_key'])) {
                    $message = __('Merchant account and api key are required');
                }
            } else {
                $message = __('Adyen Terminal is disabled');
            }
        }
        return $message;
    }

    /**
     * Get default POIID
     *
     * @return string
     */
    public function getPOIID()
    {
        return $this->webposAdyenHelper->getPaymentConfig(["poiid"])["poiid"];
    }

    /**
     * Initialize the adyen service
     *
     * @return \Adyen\Client
     */
    public function initializeAdyenService()
    {
        $config = $this->webposAdyenHelper->getPaymentConfig(
            [
                'poiid',
                'live_prefix',
                'merchant_account',
                'api_key',
                'server_type',
            ]
        );

        // initialize client
        $apiKey = $config["api_key"];

        $client = new \Adyen\Client(); /** @phpstan-ignore-line */
        $client->setApplicationName("Webpos Adyen Terminal");
        $client->setXApiKey($apiKey);
        $client->setAdyenPaymentSource('webposadyenterminal-magento2', "3.0");
        if ($config["server_type"]) {
            $client->setEnvironment(\Adyen\Environment::LIVE, $config['live_prefix']); /** @phpstan-ignore-line */
        } else {
            $client->setEnvironment(\Adyen\Environment::TEST); /** @phpstan-ignore-line */
        }

        // initialize service
        $service = new \Adyen\Service\PosPayment($client); /** @phpstan-ignore-line */
        return $service;
    }

    /**
     * Get Connected Terminals
     *
     * @param string $poiid
     * @return string
     */
    public function getConnectedTerminal($poiid)
    {
        $response = [
            'status' => 0
        ];
        if ($message = $this->getConfigurationError()) {
            $response['message'] = __($message);
            return json_encode($response);
        }
        $config = $this->webposAdyenHelper->getPaymentConfig(
            [
                'poiid',
                'live_prefix',
                'merchant_account',
                'api_key',
                'server_type',
            ]
        );

        $service = $this->initializeAdyenService();

        $requestParams = [
            "merchantAccount" => $config["merchant_account"],
            // "uniqueTerminalId" => $poiid,
        ];

        try {
            $responseData = $service->getConnectedTerminals($requestParams);
        } catch (\Adyen\AdyenException $e) { /** @phpstan-ignore-line */
            $response['message'] = $e->getMessage();
            return json_encode($response);
        }
        if (count($responseData['uniqueTerminalIds']) && in_array($poiid, $responseData['uniqueTerminalIds'])) {
            $response['message'] = __('The Adyen Terminal "%1" has been connected successfully!', $poiid);
            $response['status'] = 1;
        } else {
            $response['message'] = __('The Adyen Terminal "%1" has not been connected!', $poiid);
        }
        return json_encode($response);
    }

    /**
     * Trigger sync call sendSaleRequest on terminal
     *
     * @param mixed $payload
     * @return false|string
     * @throws \Adyen\AdyenException
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function sendSaleRequest($payload)
    {
        if ($message = $this->getConfigurationError()) {
            $response = [];
            $response['message'] = __($message);
            return json_encode($response);
        }
        if (empty($payload['poiid'])) {
            throw new \Adyen\AdyenException("Terminal ID is empty in initiate request"); /** @phpstan-ignore-line */
        }
        if (empty($payload['amount'])) {
            throw new \Adyen\AdyenException("Amount is empty in initiate request"); /** @phpstan-ignore-line */
        }
        if (empty($payload['paymentMethod']) || empty($payload['paymentMethod']['increment_id'])) {
            throw new \Adyen\AdyenException("Increment Id is empty in initiate request"); /** @phpstan-ignore-line */
        }
        if (empty($payload['paymentMethod']) || empty($payload['paymentMethod']['currencyCode'])) {
            throw new \Adyen\AdyenException("Currency Code is empty in initiate request"); /** @phpstan-ignore-line */
        }
        $serviceID = date("dHis");
        $timeStamper = date("Y-m-d") . "T" . date("H:i:s+00:00");
        $request = [
            'SaleToPOIRequest' => [
                'MessageHeader' => [
                    'ProtocolVersion' => '3.0',
                    'MessageClass' => 'Service',
                    'MessageCategory' => 'Payment',
                    'MessageType' => 'Request',
                    'ServiceID' => $serviceID,
                    'SaleID' => 'WebposAdyenTerminal',
                    'POIID' => $payload['poiid']
                ],
                "PaymentRequest" => [
                    "SaleData" => [
                        "SaleTransactionID" => [
                            "TransactionID" => (string)$payload['paymentMethod']['increment_id'],
                            "TimeStamp" => $timeStamper
                        ]
                    ],
                    "PaymentTransaction" => [
                        "AmountsReq" => [
                            "Currency" => $payload['paymentMethod']['currencyCode'],
                            "RequestedAmount" => doubleval($payload['amount'])
                        ]
                    ]
                ]
            ]
        ];

        return $this->callTerminalCloud($request);
    }

    /**
     * Trigger sync call sendRefundRequest on terminal
     *
     * @param mixed $payload
     * @return false|string
     * @throws \Adyen\AdyenException
     */
    public function sendRefundRequest($payload)
    {
        if ($message = $this->getConfigurationError()) {
            $response = [];
            $response['message'] = __($message);
            return json_encode($response);
        }
        $transactionArray = explode('.', $payload['paymentMethod']['pspReference']);
        if (empty($payload['poiid'])) {
            throw new \Adyen\AdyenException("Terminal ID is empty in initiate request"); /** @phpstan-ignore-line */
        }
        if (empty($payload['amount'])) {
            throw new \Adyen\AdyenException("Amount is empty in initiate request"); /** @phpstan-ignore-line */
        }
        $serviceID = date("dHis");
        $timeStamper = date("Y-m-d") . "T" . date("H:i:s+00:00");
        $request = [
            'SaleToPOIRequest' => [
                'MessageHeader' => [
                    'ProtocolVersion' => '3.0',
                    'MessageClass' => 'Service',
                    'MessageCategory' => 'Reversal',
                    'MessageType' => 'Request',
                    'SaleID' => 'WebposAdyenTerminal',
                    'ServiceID' => $serviceID,
                    'POIID' => $payload['poiid']
                ],
                "ReversalRequest" => [
                    "OriginalPOITransaction" => [
                        "POITransactionID" => [
                            "TransactionID" => '.' . $transactionArray[0],
                            "TimeStamp" => $timeStamper
                        ]
                    ],
                    "ReversalReason" => "MerchantCancel",
                    "ReversedAmount" => $payload['amount'],
                    "SaleData" => [
                        "SaleToAcquirerData" => "currency=EUR",
                        "SaleTransactionID" => [
                            "TimeStamp" => $timeStamper,
                            "TransactionID" => $transactionArray[1]
                        ]
                    ]
                ]
            ]
        ];
        return $this->callTerminalCloud(
            $request,
            $payload['paymentMethod']['increment_id'],
            $payload['paymentMethod']['title']
        );
    }

    /**
     * Call Terminal Cloud
     *
     * @param array $request
     * @param string $incrementId
     * @param string $title
     * @return false|string
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function callTerminalCloud($request, $incrementId = null, $title = null)
    {
        $response = [];
        $response['message'] = __("Some things went wrong!");
        try {
            $service = $this->initializeAdyenService();
            $data = $service->runTenderSync($request);
            if (isset($data['SaleToPOIRequest']) && isset($data['SaleToPOIRequest']['EventNotification'])
                && isset($data['SaleToPOIRequest']['EventNotification']['EventToNotify'])) {
                $eventNotification = $data['SaleToPOIRequest']['EventNotification'];
                if ($eventNotification['EventToNotify'] == 'Reject') {
                    if (isset($eventNotification['EventDetails']) && $eventNotification['EventDetails']) {
                        $response['message'] = str_replace(['message=', '+'], ['', ' '], $eventNotification['EventDetails']);// @codingStandardsIgnoreLine
                    }
                }
            }
            if (isset($data['SaleToPOIResponse']) && isset($data['SaleToPOIResponse']['ReversalResponse'])) {
                $reversalResponse = $data['SaleToPOIResponse']['ReversalResponse'];
                if ($reversalResponse['Response']['Result'] == 'Success') {
                    $response['status'] = 1;
                    $response['message'] = __('Success');
                    $transaction = $reversalResponse['POIData']['POITransactionID']['TransactionID'];
                    $transactionArray = explode('.', $transaction);
                    if (isset($transactionArray[1]) && $transactionArray[1]) {
                        $response['transactionNumber'] = $transactionArray[1];
                    } else {
                        $response['transactionNumber'] = $transaction;
                    }
                    $comment = $this->commentInterface
                        ->setComment('[Refund] Ref: ' . $response['transactionNumber']. ' by ' . $title);
                    $this->orderRepository->commentOrder($incrementId, $comment);
                } else {
                    $error = ($this->serializer->unserialize(base64_decode($reversalResponse['Response']['AdditionalResponse']))); // @codingStandardsIgnoreLine
                    if (isset($error["message"]) && $error["message"]) {
                        $response['message'] = $error["message"];
                    }
                }
            }
            $response = $this->checkSendSaleRequest($response, $data);
        } catch (\Adyen\AdyenException $e) { /** @phpstan-ignore-line */
            $response['message'] = $e->getMessage();
        } catch (\Exception $e) {
            $response['message'] = $e->getMessage();
        }
        return json_encode($response);
    }

    /**
     * CheckSendSaleRequest
     *
     * @param array $response
     * @param array $data
     * @return array
     */
    public function checkSendSaleRequest($response, $data)
    {
        if (isset($data['SaleToPOIResponse']) && isset($data['SaleToPOIResponse']['PaymentResponse'])) {
            $paymentResponse = $data['SaleToPOIResponse']['PaymentResponse'];
            if ($paymentResponse['Response']['Result'] == 'Success') {
                $response['status'] = 1;
                $response['message'] = __('Success');
                $transaction = $paymentResponse['POIData']['POITransactionID']['TransactionID'];
                $transactionArray = explode('.', $transaction);
                $merReference = $paymentResponse['SaleData']['SaleTransactionID']['TransactionID'];
                if (isset($transactionArray[1]) && $transactionArray[1]) {
                    $response['transactionNumber'] = $transactionArray[1];
                } else {
                    $response['transactionNumber'] = $transaction;
                }
                $response['merReference'] = $merReference;
            } else {
                $error = ($this->serializer->unserialize(base64_decode($paymentResponse['Response']['AdditionalResponse']))); // @codingStandardsIgnoreLine
                if (isset($error["message"]) && $error["message"]) {
                    $response['message'] = $error["message"];
                }
            }
        }
        return $response;
    }
}
