<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagement\Model\Command;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Get categories array from 'breadcrumb_category' of product.
 */
readonly class GetCategoriesFromProductBreadcrumbs
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository
    ) { }

    /**
     * @param ProductInterface|null $product
     * @return string[]
     */
    public function execute(?ProductInterface $product): array
    {
        // breadcrumb_category id populated with the last category id.
        $categoryId = $product->getBreadcrumbCategory();

        if (!$categoryId) {
            return ['Shop'];
        }

        $result = [];

        if ($category = $this->getCategoryFromRepo((int)$categoryId)) {
            foreach ($this->getParentCategories($category) as $parentCategory) {
                if (!in_array($parentCategory->getId(), [1,2])) {
                    $result[] = $parentCategory->getName();
                }
            }
        }

        return $result;
    }

    /**
     * @param int $categoryId
     * @return CategoryInterface|null
     */
    private function getCategoryFromRepo(int $categoryId): ?CategoryInterface
    {
        try {
            return $this->categoryRepository->get($categoryId);
        } catch (NoSuchEntityException) {
            return null;
        }
    }

    /**
     * @param CategoryInterface $category
     * @return array
     */
    private function getParentCategories(CategoryInterface $category): array
    {
        try {
            return $category->getParentCategories();
        } catch (\Exception) {
            return [];
        }
    }
}
