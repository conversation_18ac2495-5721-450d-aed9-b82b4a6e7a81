<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagement\Api\Data;

/**
 * Interface for Engagement Order
 */
interface EngagementOrderInterface
{
    public const string PURCHASE_ID = 'purchase_id';
    public const string PURCHASE_STATUS = 'purchase_status';
    public const string CREATED_AT = 'created_at';
    public const string PURCHASE_SOURCE_TYPE = 'purchase_source_type';
    public const string PURCHASE_SOURCE_NAME = 'purchase_source_name';
    public const string PRODUCT_LIST = 'product_list';
    public const string PRODUCT_IDS = 'product_ids';
    public const string TOTAL_PRICE = 'total_price';
    public const string TOTAL_PRICE_WITHOUT_TAX = 'total_price_without_tax';
    public const string TOTAL_PRICE_LOCAL_CURRENCY = 'total_price_local_currency';
    public const string LOCAL_CURRENCY = 'local_currency';
    public const string TOTAL_QUANTITY = 'total_quantity';
    public const string PAYMENT_TYPE = 'payment_type';
    public const string SHIPPING_TYPE = 'shipping_type';
    public const string SHIPPING_COMPANY = 'shipping_company';
    public const string SHIPPING_COST = 'shipping_cost';
    public const string SHIPPING_COUNTRY = 'shipping_country';
    public const string SHIPPING_CITY = 'shipping_city';
    public const string TAX_PERCENTAGE = 'tax_percentage';
    public const string TAX_VALUE = 'tax_value';
    public const string VOUCHER_CODE = 'voucher_code';
    public const string GIFT_CARD_CODE = 'gift_card_code';
    public const string GIFT_CARD_VALUE = 'gift_card_value';
    public const string VARIANT_LIST = 'variant_list';
    public const string VARIANT_IDS = 'variant_ids';
    public const string LANGUAGE = 'language';
    public const string LOCATION = 'location';
    public const string DOMAIN = 'domain';

    public function setPurchaseId(string $purchaseId): EngagementOrderInterface;
    public function getPurchaseId(): string;

    public function setCreatedAt(string $createdAt): EngagementOrderInterface;
    public function getCreatedAt(): string;

    public function setPurchaseStatus(string $purchaseStatus): EngagementOrderInterface;
    public function getPurchaseStatus(): string;

    public function setPurchaseSourceType(string $purchaseSourceType): EngagementOrderInterface;
    public function getPurchaseSourceType(): string;

    public function setPurchaseSourceName(string $purchaseSourceName): EngagementOrderInterface;
    public function getPurchaseSourceName(): string;

    public function setProductList(array $productList): EngagementOrderInterface;
    public function getProductList(): array;

    public function setProductIds(array $productIds): EngagementOrderInterface;
    public function getProductIds(): array;

    public function setTotalPrice(float $totalPrice): EngagementOrderInterface;
    public function getTotalPrice(): float;

    public function setTotalPriceWithoutTax(float $totalPriceWithoutTax): EngagementOrderInterface;
    public function getTotalPriceWithoutTax(): float;

    public function setTotalPriceLocalCurrency(float $totalPriceLocalCurrency): EngagementOrderInterface;
    public function getTotalPriceLocalCurrency(): float;

    public function setLocalCurrency(string $localCurrency): EngagementOrderInterface;
    public function getLocalCurrency(): string;

    public function setTotalQuantity(int $totalQuantity): EngagementOrderInterface;
    public function getTotalQuantity(): int;

    public function setPaymentType(string $paymentType): EngagementOrderInterface;
    public function getPaymentType(): string;

    public function setShippingType(string $shippingType): EngagementOrderInterface;
    public function getShippingType(): string;

    public function setShippingCompany(string $shippingCompany): EngagementOrderInterface;
    public function getShippingCompany(): string;

    public function setShippingCost(float $shippingCost): EngagementOrderInterface;
    public function getShippingCost(): float;

    public function setShippingCountry(string $shippingCountry): EngagementOrderInterface;
    public function getShippingCountry(): string;

    public function setShippingCity(string $shippingCity): EngagementOrderInterface;
    public function getShippingCity(): string;

    public function setTaxPercentage(float $taxPercentage): EngagementOrderInterface;
    public function getTaxPercentage(): float;

    public function setTaxValue(float $taxValue): EngagementOrderInterface;
    public function getTaxValue(): float;

    public function setVoucherCode(string $voucherCode): EngagementOrderInterface;
    public function getVoucherCode(): string;
    public function setGiftCardCode(string $giftCardCode): EngagementOrderInterface;
    public function getGiftCardCode(): string;

    public function setGiftCardValue(string $giftCardValue): EngagementOrderInterface;
    public function getGiftCardValue(): string;

    public function setVariantList(array $variantList): EngagementOrderInterface;
    public function getVariantList(): array;

    public function setVariantIds(array $variantIds): EngagementOrderInterface;
    public function getVariantIds(): array;

    public function setLanguage(string $language): EngagementOrderInterface;
    public function getLanguage(): string;

    public function setLocation(string $location): EngagementOrderInterface;
    public function getLocation(): string;

    public function setDomain(string $domain): EngagementOrderInterface;
    public function getDomain(): string;
}
