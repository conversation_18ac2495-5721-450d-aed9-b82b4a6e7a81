<?php

declare(strict_types=1);

namespace Keh\ShareSale\ViewModel;

use Magento\Checkout\Model\Session\Proxy as CheckoutSessionProxy;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Sales\Model\Order;
use Psr\Log\LoggerInterface;
use Keh\ShareSale\Model\Command\GetCustomerOrdersCount;
use Keh\ShareSale\Model\ConfigProvider;

readonly class ShareSale implements ArgumentInterface
{
    public function __construct(
        private GetCustomerOrdersCount $getCustomerOrdersCount,
        private LoggerInterface $logger,
        private CheckoutSessionProxy $checkoutSession,
        private ConfigProvider $configProvider
    ) { }

    /**
     * @return string
     */
    public function getMerchantId(): string
    {
        return $this->configProvider->getMerchantId();
    }

    /**
     * Get order from checkout session
     *
     * @return Order|null
     */
    public function getOrder(): ?Order
    {
        $order = $this->checkoutSession->getLastRealOrder();
        if (!$order || !$order->getEntityId()) {
            $this->logger->error('keh:' . __METHOD__);
            $this->logger->error(
                "Could not load order from checkout session, ShareSale is not sent"
            );
            return null;
        }
        return $order;
    }

    /**
     * @return string
     */
    public function getImageUrl(): string
    {
        return $this->configProvider->getSaleEndpoint();
    }

    /**
     * @param Order $order
     * @return string
     */
    public function getAffiliateTotal(Order $order): string
    {
        return (string)($order->getSubtotal() + $order->getDiscountAmount());
    }

    /**
     * @param Order $order
     * @return array
     */
    public function getOrderItemsData(Order $order): array
    {
        $skuList = [];
        $quantityList = [];
        $priceList = [];
        foreach ($order->getAllVisibleItems() as $item) {
            $skuList[] = $item->getSku();
            $quantityList[] = ceil((int)$item->getQtyOrdered());
            $priceList[] = (
                $item->getProduct()->getFinalPrice() - ($item->getDiscountAmount() / $item->getQtyOrdered())
            );
        }
        return [
            'sku_list' => implode(',', $skuList),
            'quantity_list' => implode(',', $quantityList),
            'price_list' => implode(',', $priceList)
        ];
    }

    /**
     * @param int $customerId
     * @return int
     */
    public function isNewCustomer(int $customerId): int
    {
        $ordersCount = $this->getCustomerOrdersCount->execute($customerId);
        return $ordersCount > 1 ? 0 : 1;
    }
}
