<?php
declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Checkout\Block\Onepage\Success;
use Keh\ShareSale\ViewModel\ShareSale;

/**
 * @var Success $block
 * @var Escaper $escaper
 * @var ShareSale $viewModel
 */
$viewModel = $block->getData('view_model');
$order = $viewModel->getOrder();
?>
<?php if ($order): ?>
    <?php $orderItemsData = $viewModel->getOrderItemsData($order)?>
    <img id='_SHRSL_img_1' src='<?= $viewModel->getImageUrl() ?>?tracking=<?= $order->getIncrementId() ?>&amp;amount=<?= $viewModel->getAffiliateTotal($order) ?>&amp;transtype=sale&amp;merchantID=<?= $viewModel->getMerchantId() ?>&amp;couponcode=<?= $order->getCouponCode() ?>&amp;skulist=<?= $orderItemsData['sku_list'] ?>&amp;quantitylist=<?= $orderItemsData['quantity_list'] ?>&amp;pricelist=<?= $orderItemsData['price_list'] ?>&amp;newcustomer=<?= $viewModel->isNewCustomer((int)$order->getCustomerId()) ?>&amp;currency=<?= $order->getOrderCurrencyCode() ?>&amp;v=2.1' width='1' height='1'>
<?php endif; ?>
