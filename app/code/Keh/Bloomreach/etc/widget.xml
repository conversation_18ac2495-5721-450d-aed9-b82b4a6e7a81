<?xml version="1.0" encoding="UTF-8"?>

<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget class="Keh\Bloomreach\Block\Widget\Products" id="bloomreach_products_widget">
        <label>Bloomreach New Widget</label>
        <description>Bloom reach products widget</description>
        <parameters>
            <parameter name="widget_id" xsi:type="text" required="true" visible="true" sort_order="10">
                <label>BloomReach Widget Id</label>
            </parameter>
            <parameter name="widget_type" xsi:type="select" visible="true" required="true" sort_order="15">
                <label translate="true">Widget type</label>
                <options>
                    <option name="select" value="">
                        <label>Choose widget type</label>
                    </option>
                    <option name="item" value="item">
                        <label>Item-based(FBT, FVT, Similar, Experience-driven)</label>
                    </option>
                    <option name="category" value="category">
                        <label>Category-based(Category or Landing pages)</label>
                    </option>
                    <option name="keyword" value="keyword">
                        <label>Keyword-based(Search or Landing pages)</label>
                    </option>
                    <option name="personalized" value="personalized">
                        <label>Personalization-based(Past Purchases, Recently Viewed Products)</label>
                    </option>
                    <option name="global" value="global">
                        <label>Global recommendation(Trending, Bestseller)</label>
                    </option>
                </options>
            </parameter>
            <parameter name="alternative_widget_id" xsi:type="select" required="false" visible="true" sort_order="20" source_model="Keh\Bloomreach\Model\Widget\Source\BloomreachWidgets">
                <label translate="true">Alternative widget id (for personalized based)</label>
                <description translate="true">We can use it for guest as alternative of customer based widgets</description>
                <depends>
                    <parameter name="widget_type" value="personalized"/>
                </depends>
            </parameter>
            <parameter name="default_category_id" xsi:type="text" required="false" visible="true" sort_order="25">
                <label>Default category id</label>
                <description translate="true">We can use it to display this widget where we don`t have a category ID, like the home page.</description>
                <depends>
                    <parameter name="widget_type" value="category"/>
                </depends>
            </parameter>
            <parameter name="default_query" xsi:type="text" required="false" visible="true" sort_order="30">
                <label>Default query</label>
                <description translate="true">We can use it to display this widget where we don`t have a query, like the home page.</description>
                <depends>
                    <parameter name="widget_type" value="keyword"/>
                </depends>
            </parameter>
            <parameter name="show_title" xsi:type="select" required="true" visible="true" sort_order="35">
                <label>Show Title</label>
                <options>
                    <option name="yes" value="1" selected="true">
                        <label>Yes</label>
                    </option>
                    <option name="no" value="0">
                        <label>No</label>
                    </option>
                </options>
            </parameter>
            <parameter name="show_view_more_link" xsi:type="select" required="true" visible="true" sort_order="45">
                <label>Show View More Link</label>
                <options>
                    <option name="no" value="0" selected="true">
                        <label>No</label>
                    </option>
                    <option name="yes" value="1">
                        <label>Yes</label>
                    </option>
                </options>
            </parameter>
            <parameter name="view_more_link_url" xsi:type="text" required="true" visible="true" sort_order="50">
                <label>View More Link URL</label>
                <depends>
                    <parameter name="show_view_more_link" value="1"/>
                </depends>
            </parameter>
            <parameter name="view_more_link_position" xsi:type="select" required="true" visible="true" sort_order="55">
                <label>View More Link Position</label>
                <depends>
                    <parameter name="show_view_more_link" value="1"/>
                </depends>
                <options>
                    <option name="select" value="">
                        <label></label>
                    </option>
                    <option name="top" value="top">
                        <label>Top</label>
                    </option>
                    <option name="bottom" value="bottom">
                        <label>Bottom</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
</widgets>
