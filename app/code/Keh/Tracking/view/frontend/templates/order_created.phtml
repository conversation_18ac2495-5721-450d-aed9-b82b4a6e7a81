<?php
declare(strict_types=1);

use Keh\Tracking\ViewModel\OrderDataProvider;
use Magento\Checkout\Block\Onepage\Success;
use Magento\Framework\Escaper;

/** @var Success $block */
/** @var Escaper $escaper */

/** @var OrderDataProvider $orderDataProvider */
$orderDataProvider = $block->getData('order_data_provider');

$orderData = $orderDataProvider->getOrderMarketingDataArray();
?>
<?php if (!empty($orderData)): ?>
    <script type="text/x-magento-init">
        {
            "*": {
                "Magento_Ui/js/core/app": {
                    "components": {
                        "orderCreated": {
                            "component": "Keh_Tracking/js/order-created",
                            "orderData": <?php echo $orderDataProvider->serialize($orderData);?>
                        }
                    }
                }
            }
        }
    </script>
<?php endif;?>
