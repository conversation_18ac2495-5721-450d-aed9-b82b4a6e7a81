<?php
declare(strict_types=1);

namespace Keh\Tracking\ViewModel;

use Magento\Checkout\Model\Session\Proxy as CheckoutSessionProxy;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Keh\Tracking\Model\DataProvider\OrderMarketingDataArray;

/**
 * Order data provider for onepage success page
 */
class OrderDataProvider implements ArgumentInterface
{
    public function __construct(
        private readonly Json $jsonSerializer,
        private readonly OrderMarketingDataArray $orderMarketingDataArray,
        private readonly CheckoutSessionProxy $checkoutSession
    ) { }

    /**
     * Get array of order data from checkout session
     *
     * @return array
     */
    public function getOrderMarketingDataArray(): array
    {
        $order = $this->checkoutSession->getLastRealOrder();
        return $this->getOrderMarketingDataArrayByOrder($order);
    }

    /**
     * @param OrderInterface|null $order
     * @return array
     */
    public function getOrderMarketingDataArrayByOrder(?OrderInterface $order): array
    {
        if (!$order || !$order->getEntityId()) {
            return [];
        }
        return $this->orderMarketingDataArray->getData($order);
    }

    public function serialize(array $data): string
    {
        return $this->jsonSerializer->serialize($data);
    }
}
