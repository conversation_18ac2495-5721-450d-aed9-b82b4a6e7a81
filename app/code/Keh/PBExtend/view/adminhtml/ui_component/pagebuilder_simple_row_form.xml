<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd" extends="pagebuilder_base_form">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">pagebuilder_simple_row_form.pagebuilder_simple_row_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Content wrapper</item>
    </argument>

    <settings>
        <deps>
            <dep>pagebuilder_simple_row_form.pagebuilder_simple_row_form_data_source</dep>
        </deps>
        <namespace>pagebuilder_simple_row_form</namespace>
    </settings>

    <dataSource name="pagebuilder_simple_row_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_PageBuilder/js/form/provider</item>
            </item>
        </argument>
        <dataProvider name="pagebuilder_simple_row_form_data_source" class="Magento\PageBuilder\Model\ContentType\DataProvider">
            <settings>
                <requestFieldName/>
                <primaryFieldName/>
            </settings>
        </dataProvider>
    </dataSource>

    <fieldset name="background" sortOrder="30">
        <settings>
            <label translate="true">Background</label>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
        <field name="background_color" sortOrder="10" formElement="colorPicker">
            <settings>
                <label translate="true">Background Color</label>
                <componentType>colorPicker</componentType>
                <placeholder translate="true">No Color</placeholder>
                <validation>
                    <rule name="validate-color" xsi:type="boolean">true</rule>
                </validation>
                <dataScope>background_color</dataScope>
                <additionalClasses>
                    <class name="admin__field-medium">true</class>
                </additionalClasses>
            </settings>
            <formElements>
                <colorPicker>
                    <settings>
                        <colorPickerMode>full</colorPickerMode>
                        <colorFormat>hex</colorFormat>
                    </settings>
                </colorPicker>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="appearance_fieldset" component="Magento_PageBuilder/js/form/element/dependent-fieldset">
        <settings>
            <label translate="true">Appearance</label>
            <additionalClasses>
                <class name="admin__fieldset-visual-select-large">true</class>
            </additionalClasses>
            <collapsible>false</collapsible>
            <opened>true</opened>
            <imports>
                <link name="hideFieldset">${$.name}.appearance:options</link>
                <link name="hideLabel">${$.name}.appearance:options</link>
            </imports>
        </settings>
        <field name="appearance" formElement="select" sortOrder="10" component="Magento_PageBuilder/js/form/element/dependent-visual-select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="string">default</item>
                </item>
            </argument>
            <settings>
                <additionalClasses>
                    <class name="admin__field-wide">true</class>
                    <class name="admin__field-visual-select-container">true</class>
                </additionalClasses>
                <dataType>text</dataType>
                <elementTmpl>Magento_PageBuilder/form/element/visual-select</elementTmpl>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="AppearanceSourceSimpleRow" />
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
    <fieldset name="general" sortOrder="20">
        <settings>
            <label/>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
    </fieldset>
</form>
