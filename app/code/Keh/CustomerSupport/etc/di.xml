<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Sales\Block\Adminhtml\Order\Create\Search\Grid" type="Keh\CustomerSupport\Block\Adminhtml\Order\Create\Search\Grid"/>

    <!-- Logger -->
    <virtualType name="CustomerSupportLogHandler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/customer_support.log</argument>
        </arguments>
    </virtualType>
    <type name="Keh\CustomerSupport\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">CustomerSupportLogger</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">CustomerSupportLogHandler</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="customer_support_rma_listing_data_source" xsi:type="string">Mirasvit\Rma\Model\ResourceModel\Rma\Grid\Collection</item>
                <item name="customer_support_orders_listing_data_source" xsi:type="string">Magento\Sales\Model\ResourceModel\Order\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
</config>
