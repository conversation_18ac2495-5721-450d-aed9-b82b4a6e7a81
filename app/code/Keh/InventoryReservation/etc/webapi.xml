<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/keh/inventory/reserve" method="POST">
        <service class="Keh\InventoryReservation\Api\ReserveProductInterface" method="execute"/>
        <resources>
            <resource ref="Keh_InventoryReservation::api"/>
        </resources>
    </route>
    <route url="/V1/keh/inventory/delete-reservation" method="POST">
        <service class="Keh\InventoryReservation\Api\DeleteReservationInterface" method="execute"/>
        <resources>
            <resource ref="Keh_InventoryReservation::api"/>
        </resources>
    </route>
</routes>
