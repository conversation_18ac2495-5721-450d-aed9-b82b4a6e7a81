<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="keh_inventory_reservation" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
        <label>Keh Inventory Reservation</label>
        <tab>keh_core</tab>
        <class>separator-top</class>
        <resource>Keh_InventoryReservation::admin</resource>
            <group id="reservation" sortOrder="10" translate="label" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Reservations</label>
                <field id="days" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Reservation Days</label>
                    <comment>Provide a number</comment>
                    <validate>validate-greater-than-zero</validate>
                </field>
            </group>
        </section>
    </system>
</config>
