<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Keh_InventoryReservation::admin" title="KEH Inventory Reservation Configuration"/>
                        </resource>
                    </resource>
                </resource>
                <resource id="Keh_InventoryReservation::inventory_reservation"
                          title="KEH Inventory Reservation"
                          translate="title"
                          sortOrder="10">
                    <resource id="Keh_InventoryReservation::api" title="KEH Inventory Reservation API"/>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
