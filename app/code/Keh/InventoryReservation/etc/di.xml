<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Keh Inventory Reservation preferences -->
    <preference for="Keh\InventoryReservation\Api\Data\KehInventoryReservationInterface" type="Keh\InventoryReservation\Model\KehInventoryReservation"/>
    <preference for="Keh\InventoryReservation\Api\Data\ReservationRequestInterface" type="Keh\InventoryReservation\Model\Data\ReservationRequest"/>
    <preference for="Keh\InventoryReservation\Api\Data\ReservationResponseInterface" type="Keh\InventoryReservation\Model\Data\ReservationResponse"/>
    <preference for="Keh\InventoryReservation\Api\Data\ReservationInterface" type="Keh\InventoryReservation\Model\InventoryReservation"/>
    <preference for="Keh\InventoryReservation\Api\Data\DeleteReservationRequestInterface" type="Keh\InventoryReservation\Model\Data\DeleteReservationRequest"/>
    <preference for="Keh\InventoryReservation\Api\KehInventoryReservationRepositoryInterface" type="Keh\InventoryReservation\Model\KehInventoryReservationRepository"/>
    <preference for="Keh\InventoryReservation\Api\InventoryReservationRepositoryInterface" type="Keh\InventoryReservation\Model\InventoryReservationRepository"/>
    <preference for="Keh\InventoryReservation\Api\ReserveProductInterface" type="Keh\InventoryReservation\Service\ReserveProduct"/>
    <preference for="Keh\InventoryReservation\Api\DeleteReservationInterface" type="Keh\InventoryReservation\Service\DeleteReservation"/>
</config>
