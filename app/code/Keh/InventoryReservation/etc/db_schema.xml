<?xml version="1.0"?>
<!--
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="keh_inventory_reservation" resource="default" engine="innodb" comment="Keh Manual Inventory Reservation">
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="varchar" name="sku" nullable="false" length="64" comment="SKU"/>
        <column xsi:type="decimal" name="quantity" scale="4" precision="10" unsigned="false" nullable="false" default="0" comment="Quantity"/>
        <column xsi:type="varchar" name="customer_email" length="255" nullable="false" comment="Customer Email"/>
        <column xsi:type="varchar" name="admin_email" length="255" nullable="false" comment="Admin Email"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Created At"/>
        <column xsi:type="date" name="cancel_at" nullable="false" comment="Cancellation Date"/>
        <column xsi:type="int" name="reservation_id" unsigned="true" nullable="false" comment="inventory_reservation.reservation_id"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="KEH_INVENTORY_RESERVATION_RESERVATION_ID"
                    table="keh_inventory_reservation" column="reservation_id"
                    referenceTable="inventory_reservation" referenceColumn="reservation_id"
                    onDelete="CASCADE"/>
        <index referenceId="KEH_INVENTORY_RESERVATION_SKU_CUSTOMER_EMAIL" indexType="btree">
            <column name="customer_email"/>
            <column name="sku"/>
        </index>
        <index referenceId="KEH_INVENTORY_RESERVATION_CANCEL_AT" indexType="btree">
            <column name="cancel_at"/>
        </index>
    </table>
</schema>
