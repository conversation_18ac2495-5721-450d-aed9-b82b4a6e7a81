<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model;

use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation as ResourceModel;
use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation\CollectionFactory;
use Keh\InventoryReservation\Api\Data\KehInventoryReservationInterface;
use Keh\InventoryReservation\Api\KehInventoryReservationRepositoryInterface;
use Keh\InventoryReservation\Exceptions\ReservationCouldNotSaveException;
use Keh\InventoryReservation\Exceptions\ReservationCouldNotDeleteException;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SearchResultsInterfaceFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Keh Inventory Reservation Repository
 */
class KehInventoryReservationRepository implements KehInventoryReservationRepositoryInterface
{
    private array $reservationById = [];
    private array $reservationsBySku = [];

    public function __construct(
        private readonly ResourceModel $resourceModel,
        private readonly KehInventoryReservationFactory $reservationFactory,
        private readonly CollectionFactory $collectionFactory,
        private readonly SearchResultsInterfaceFactory $searchResultsFactory,
        private readonly CollectionProcessorInterface $collectionProcessor
    ) { }

    /**
     * @inheritdoc
     */
    public function save(KehInventoryReservationInterface $reservation): KehInventoryReservationInterface
    {
        try {
            $this->resourceModel->save($reservation);
        } catch (\Exception $exception) {
            throw new ReservationCouldNotSaveException(__(
                'Could not save the reservation: %1',
                $exception->getMessage()
            ));
        }
        unset($this->reservationById[$reservation->getEntityId()]);
        unset($this->reservationsBySku[$reservation->getSku()]);
        return $reservation;
    }

    /**
     * @inheritdoc
     */
    public function getById(int $entityId): KehInventoryReservationInterface
    {
        if (!isset($this->reservationById[$entityId])) {
            $reservation = $this->reservationFactory->create();
            $this->resourceModel->load($reservation, $entityId);
            if (!$reservation->getEntityId()) {
                throw new NoSuchEntityException(__('Reservation with id "%1" does not exist.', $entityId));
            }
            $this->reservationById[$reservation->getEntityId()] = $reservation;
            $this->reservationsBySku[$reservation->getSku()] = $reservation;
        }

        return $this->reservationById[$entityId];
    }

    /**
     * @inheritdoc
     */
    public function getBySku(string $sku): array
    {
        if (!isset($this->reservationsBySku[$sku])) {
            $collection = $this->collectionFactory->create();
            $collection->addSkuFilter($sku);
            $this->reservationsBySku[$sku] = $collection->getItems();
        }

        return $this->reservationsBySku[$sku];
    }

    /**
     * @inheritdoc
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface
    {
        $collection = $this->collectionFactory->create();

        $this->collectionProcessor->process($searchCriteria, $collection);

        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }

    /**
     * @inheritdoc
     */
    public function delete(KehInventoryReservationInterface $reservation): bool
    {
        try {
            $this->resourceModel->delete($reservation);
        } catch (\Exception $exception) {
            throw new ReservationCouldNotDeleteException(__(
                'Could not delete the keh_inventory_reservation row : %1',
                $exception->getMessage()
            ));
        }
        unset($this->reservationById[$reservation->getEntityId()]);
        unset($this->reservationsBySku[$reservation->getSku()]);
        return true;
    }

    /**
     * @inheritdoc
     */
    public function deleteById(int $entityId): bool
    {
        return $this->delete($this->getById($entityId));
    }
}
