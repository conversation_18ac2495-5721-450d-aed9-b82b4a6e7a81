<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model;

use Keh\InventoryReservation\Api\Data\ReservationInterfaceFactory;
use Keh\InventoryReservation\Model\ResourceModel\InventoryReservation as ResourceModel;
use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation\CollectionFactory;
use Keh\InventoryReservation\Api\Data\ReservationInterface;
use Keh\InventoryReservation\Api\InventoryReservationRepositoryInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Inventory Reservation Repository, does not exists in a native module.
 */
class InventoryReservationRepository implements InventoryReservationRepositoryInterface
{
    private array $reservationById = [];

    public function __construct(
        private readonly ResourceModel $resourceModel,
        private readonly ReservationInterfaceFactory $reservationInterfaceFactory
    ) { }

    /**
     * @inherhitDoc
     */
    public function save(ReservationInterface $reservation): ReservationInterface
    {
        try {
            $this->resourceModel->save($reservation);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the inventory reservation: %1',
                $exception->getMessage()
            ));
        }
        return $reservation;
    }

    /**
     * @inherhitDoc
     */
    public function getById(int $reservationId): ReservationInterface
    {
        if (!isset($this->reservationById[$reservationId])) {
            /** @var ReservationInterface $reservation */
            $reservation = $this->reservationInterfaceFactory->create();
            $this->resourceModel->load($reservation, $reservationId);
            if (!$reservation->getReservationId()) {
                throw new NoSuchEntityException(
                    __('Inventory reservation with id "%1" does not exist.', $reservationId)
                );
            }
            $this->reservationById[$reservation->getReservationId()] = $reservation;
        }

        return $this->reservationById[$reservationId];
    }

    /**
     * @inherhitDoc
     */
    public function deleteById(int $reservationId): bool
    {
        return $this->delete($this->getById($reservationId));
    }

    /**
     * @inherhitDoc
     */
    public function delete(ReservationInterface $reservation): bool
    {
        try {
            $this->resourceModel->delete($reservation);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the inventory_reservation row : %1',
                $exception->getMessage()
            ));
        }
        unset($this->reservationById[$reservation->getEntityId()]);
        return true;
    }
}
