<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model\Data;

use Keh\InventoryReservation\Api\Data\ReservationResponseInterface;
use Magento\Framework\DataObject;

/**
 * Reservation Response Model
 */
class ReservationResponse extends DataObject implements ReservationResponseInterface
{
    /**
     * @inheritdoc
     */
    public function getStatus(): bool
    {
        return (bool)$this->getData(self::STATUS);
    }

    /**
     * @inheritdoc
     */
    public function setStatus(bool $status): ReservationResponseInterface
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * @inheritdoc
     */
    public function getMessage(): ?string
    {
        return $this->getData(self::MESSAGE);
    }

    /**
     * @inheritdoc
     */
    public function setMessage(?string $message): ReservationResponseInterface
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * @inheritdoc
     */
    public function getReservation(): string
    {
        return (string)$this->getData(self::RESERVATION);
    }

    /**
     * @inheritdoc
     */
    public function setReservation(string $reservation): ReservationResponseInterface
    {
        return $this->setData(self::RESERVATION, $reservation);
    }
}
