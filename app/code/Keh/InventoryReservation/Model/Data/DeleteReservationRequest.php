<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model\Data;

use Keh\InventoryReservation\Api\Data\DeleteReservationRequestInterface;
use Magento\Framework\DataObject;

/**
 * Delete reservation request data model
 */
class DeleteReservationRequest extends DataObject implements DeleteReservationRequestInterface
{
    /**
     * @inheritdoc
     */
    public function getSku(): string
    {
        return $this->getData(self::SKU);
    }

    /**
     * @inheritdoc
     */
    public function setSku(string $sku): DeleteReservationRequestInterface
    {
        return $this->setData(self::SKU, $sku);
    }

    /**
     * @inheritdoc
     */
    public function getCustomerEmail(): string
    {
        return $this->getData(self::CUSTOMER_EMAIL);
    }

    /**
     * @inheritdoc
     */
    public function setCustomerEmail(string $customerEmail): DeleteReservationRequestInterface
    {
        return $this->setData(self::CUSTOMER_EMAIL, $customerEmail);
    }
}
