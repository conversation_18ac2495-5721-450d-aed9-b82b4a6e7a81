<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model\Data;

use Keh\InventoryReservation\Api\Data\ReservationRequestInterface;
use Magento\Framework\DataObject;

/**
 * Reservation Request Model
 */
class ReservationRequest extends DataObject implements ReservationRequestInterface
{
    /**
     * @inheritdoc
     */
    public function getSku(): string
    {
        return $this->getData(self::SKU);
    }

    /**
     * @inheritdoc
     */
    public function setSku(string $sku): ReservationRequestInterface
    {
        return $this->setData(self::SKU, $sku);
    }

    /**
     * @inheritdoc
     */
    public function getQuantity(): float
    {
        return $this->getData(self::QUANTITY);
    }

    /**
     * @inheritdoc
     */
    public function setQuantity(float $quantity): ReservationRequestInterface
    {
        return $this->setData(self::QUANTITY, $quantity);
    }

    /**
     * @inheritdoc
     */
    public function getCancelAt(): ?string
    {
        return $this->getData(self::CANCEL_AT);
    }

    /**
     * @inheritdoc
     */
    public function setCancelAt(?string $cancelAt): ReservationRequestInterface
    {
        return $this->setData(self::CANCEL_AT, $cancelAt);
    }

    /**
     * @inheritdoc
     */
    public function getCustomerEmail(): string
    {
        return $this->getData(self::CUSTOMER_EMAIL);
    }

    /**
     * @inheritdoc
     */
    public function setCustomerEmail(string $customerEmail): ReservationRequestInterface
    {
        return $this->setData(self::CUSTOMER_EMAIL, $customerEmail);
    }

    /**
     * @inheritdoc
     */
    public function getAdminEmail(): string
    {
        return $this->getData(self::ADMIN_EMAIL);
    }

    /**
     * @inheritdoc
     */
    public function setAdminEmail(string $adminEmail): ReservationRequestInterface
    {
        return $this->setData(self::ADMIN_EMAIL, $adminEmail);
    }
}
