<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model;

use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation as ResourceModel;
use Keh\InventoryReservation\Api\Data\KehInventoryReservationInterface;
use Magento\Framework\Model\AbstractModel;

/**
 * Keh Inventory Reservation Model
 */
class KehInventoryReservation extends AbstractModel implements KehInventoryReservationInterface
{
    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        $this->_init(ResourceModel::class);
    }

    /**
     * @inheritdoc
     */
    public function getEntityId(): ?int
    {
        return $this->getData(self::ENTITY_ID) === null
            ? null
            : (int)$this->getData(self::ENTITY_ID);
    }

    /**
     * @inheritdoc
     * @param int $entityId
     * @return $this
     */
    public function setEntityId($entityId): self
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * @inheritdoc
     */
    public function getSku(): string
    {
        return (string)$this->getData(self::SKU);
    }

    /**
     * @inheritdoc
     */
    public function setSku(string $sku): self
    {
        return $this->setData(self::SKU, $sku);
    }

    /**
     * @inheritdoc
     */
    public function getQuantity(): float
    {
        return (float)$this->getData(self::QUANTITY);
    }

    /**
     * @inheritdoc
     */
    public function setQuantity(float $quantity): self
    {
        return $this->setData(self::QUANTITY, $quantity);
    }

    /**
     * @inheritdoc
     */
    public function getCustomerEmail(): string
    {
        return (string)$this->getData(self::CUSTOMER_EMAIL);
    }

    /**
     * @inheritdoc
     */
    public function setCustomerEmail(string $customerEmail): self
    {
        return $this->setData(self::CUSTOMER_EMAIL, $customerEmail);
    }

    /**
     * @inheritdoc
     */
    public function getAdminEmail(): string
    {
        return (string)$this->getData(self::ADMIN_EMAIL);
    }

    /**
     * @inheritdoc
     */
    public function setAdminEmail(string $adminEmail): self
    {
        return $this->setData(self::ADMIN_EMAIL, $adminEmail);
    }

    /**
     * @inheritdoc
     */
    public function getCreatedAt(): string
    {
        return (string)$this->getData(self::CREATED_AT);
    }

    /**
     * @inheritdoc
     */
    public function setCreatedAt(string $createdAt): self
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheritdoc
     */
    public function getCancelAt(): string
    {
        return (string)$this->getData(self::CANCEL_AT);
    }

    /**
     * @inheritdoc
     */
    public function setCancelAt(string $cancelAt): self
    {
        return $this->setData(self::CANCEL_AT, $cancelAt);
    }

    /**
     * @inheritdoc
     */
    public function getReservationId(): int
    {
        return (int)$this->getData(self::RESERVATION_ID);
    }

    /**
     * @inheritdoc
     */
    public function setReservationId(int $reservationId): self
    {
        return $this->setData(self::RESERVATION_ID, $reservationId);
    }

    /**
     * @inheritdoc
     */
    public function toObjectArray(): array
    {
        return [
            self::ENTITY_ID => $this->getEntityId(),
            self::SKU => $this->getSku(),
            self::QUANTITY => $this->getQuantity(),
            self::CUSTOMER_EMAIL => $this->getCustomerEmail(),
            self::ADMIN_EMAIL => $this->getAdminEmail(),
            self::CREATED_AT => $this->getCreatedAt(),
            self::CANCEL_AT => $this->getCancelAt(),
            self::RESERVATION_ID => $this->getReservationId(),
        ];
    }
}
