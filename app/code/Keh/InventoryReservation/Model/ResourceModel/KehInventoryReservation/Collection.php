<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation;

use Keh\InventoryReservation\Model\KehInventoryReservation;
use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation as ResourceModel;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Keh Inventory Reservation Collection
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'entity_id';

    /**
     * Collection initialization
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            KehInventoryReservation::class,
            ResourceModel::class
        );
    }


    /**
     * Add filter by SKU
     *
     * @param string $sku
     * @return $this
     */
    public function addSkuFilter(string $sku): self
    {
        return $this->addFieldToFilter('sku', $sku);
    }

    /**
     * Add filter by cancel date
     *
     * @param string $date
     * @return $this
     */
    public function addCancelDateFilter(string $date): self
    {
        return $this->addFieldToFilter('cancel_at', ['lteq' => $date]);
    }
}
