<?php

declare(strict_types=1);

namespace Keh\InventoryReservation\Model;

use Keh\InventoryReservation\Model\ResourceModel\InventoryReservation as ResourceModel;
use Keh\InventoryReservation\Api\Data\ReservationInterface;
use Magento\Framework\Model\AbstractModel;

class InventoryReservation extends AbstractModel implements ReservationInterface
{
    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        $this->_init(ResourceModel::class);
    }

    /**
     * @inheritdoc
     */
    public function setReservationId(?int $reservationId): ReservationInterface
    {
        return $this->setData(self::RESERVATION_ID, $reservationId);
    }

    /**
     * @inheritdoc
     */
    public function getReservationId(): ?int
    {
        return $this->getData(self::RESERVATION_ID) === null
            ? null
            : (int)$this->getData(self::RESERVATION_ID);
    }

    /**
     * @inheritdoc
     */
    public function setStockId(int $stockId): ReservationInterface
    {
        return $this->setData(self::STOCK_ID, $stockId);
    }

    /**
     * @inheritdoc
     */
    public function getStockId(): int
    {
        return (int)$this->getData(self::STOCK_ID);
    }

    /**
     * @inheritdoc
     */
    public function setSku(string $sku): ReservationInterface
    {
        return $this->setData(self::SKU, $sku);
    }

    /**
     * @inheritdoc
     */
    public function getSku(): string
    {
        return (string)$this->getData(self::SKU);
    }

    /**
     * @inheritdoc
     */
    public function setQuantity(float $quantity): ReservationInterface
    {
        return $this->setData(self::QUANTITY, $quantity);
    }

    /**
     * @inheritdoc
     */
    public function getQuantity(): float
    {
        return (float)$this->getData(self::QUANTITY);
    }

    /**
     * @inheritdoc
     */
    public function setMetadata(?string $metaData): ReservationInterface
    {
        return $this->setData(self::METADATA, $metaData);
    }
    /**
     * @inheritdoc
     */
    public function getMetadata(): ?string
    {
        return $this->getData(self::METADATA) === null ? null : (string)$this->getData(self::METADATA);
    }
}
