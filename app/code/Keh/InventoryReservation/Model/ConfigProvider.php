<?php

declare(strict_types=1);

namespace Keh\InventoryReservation\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

readonly class ConfigProvider
{
    /** General configs */
    public const string XML_PATH_TO_KEH_CUSTOMER_SUPPORT_RESERVATION_DAYS =
        'keh_inventory_reservation/reservation/days';

    public function __construct(
        private ScopeConfigInterface $scopeConfig
    ) {}

    /**
     * @param int|null $storeId
     * @return int
     */
    public function getReservationDays(?int $storeId = null): int
    {
        return (int)$this->scopeConfig->getValue(
            self::XML_PATH_TO_KEH_CUSTOMER_SUPPORT_RESERVATION_DAYS,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
