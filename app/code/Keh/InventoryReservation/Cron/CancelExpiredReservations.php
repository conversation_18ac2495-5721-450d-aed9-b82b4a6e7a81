<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Cron;

use Keh\InventoryReservation\Api\Data\DeleteReservationRequestInterface;
use Keh\InventoryReservation\Api\Data\DeleteReservationRequestInterfaceFactory;
use Keh\InventoryReservation\Api\DeleteReservationInterface;
use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation\CollectionFactory;
use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation\Collection as KehInventoryReservationCollection;
use Keh\InventoryReservation\Api\Data\KehInventoryReservationInterface;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Cron job to cancel expired reservations
 */
readonly class CancelExpiredReservations
{
    public function __construct(
        private CollectionFactory $collectionFactory,
        private DeleteReservationInterface $deleteReservation,
        private DeleteReservationRequestInterfaceFactory $deleteReservationRequestFactory,
        private LoggerInterface $logger
    ) { }

    /**
     * @return void
     * @throws \DateMalformedStringException
     */
    public function execute(): void
    {
        foreach ($this->loadReservations() as $kehReservation) {
            $this->deleteReservation($kehReservation);
        }
    }

    private function loadReservations(): array
    {
        try {
            $currentDate = new \DateTime();

            /** @var KehInventoryReservationCollection $collection */
            $collection = $this->collectionFactory->create();
            $collection->addCancelDateFilter($currentDate->format('Y-m-d'));
            return $collection->getItems();
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    '%s Failed to load keh_inventory_reservation rows, message: %s',
                    __METHOD__,
                    $e->getMessage()
                )
            );
            return [];
        }
    }

    private function deleteReservation(KehInventoryReservationInterface $kehReservation): void
    {
        /** @var DeleteReservationRequestInterface $request */
        $request = $this->deleteReservationRequestFactory->create();
        $request->setSku($kehReservation->getSku());
        $request->setCustomerEmail($kehReservation->getCustomerEmail());

        try {
            $result = $this->deleteReservation->execute($request);
            if (!$result) {
                throw new LocalizedException(__("Failed to delete reservation."));
            }
        } catch (\Exception $e) {
            $this->logger->error(
                sprintf(
                    '%s Failed to delete reservation in cron, reservation ID: %s, message: %s',
                    __METHOD__,
                    $kehReservation->getEntityId(),
                    $e->getMessage()
                )
            );
        }
    }
}
