<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api;

use Keh\InventoryReservation\Api\Data\ReservationInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Interface for Keh Inventory Reservation Repository
 * @api
 */
interface InventoryReservationRepositoryInterface
{
    /**
     * Save Keh Inventory Reservation
     *
     * @param ReservationInterface $reservation
     * @return ReservationInterface
     * @throws CouldNotSaveException
     */
    public function save(ReservationInterface $reservation): ReservationInterface;

    /**
     * Get Keh Inventory Reservation by ID
     *
     * @param int $reservationId
     * @return ReservationInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $reservationId): ReservationInterface;

    /**
     * Delete Keh Inventory Reservation
     *
     * @param ReservationInterface $reservation
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(ReservationInterface $reservation): bool;

    /**
     * Delete Keh Inventory Reservation by ID
     *
     * @param int $reservationId
     * @return bool
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function deleteById(int $reservationId): bool;
}
