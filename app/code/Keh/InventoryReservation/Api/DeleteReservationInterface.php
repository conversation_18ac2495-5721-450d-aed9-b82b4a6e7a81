<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api;

use Keh\InventoryReservation\Api\Data\DeleteReservationRequestInterface;

/**
 * Interface for deleting product reservations
 * @api
 */
interface DeleteReservationInterface
{
    /**
     * Delete reservation by request
     *
     * @param DeleteReservationRequestInterface $request
     * @return bool
     */
    public function execute(
        DeleteReservationRequestInterface $request
    ): bool;
}
