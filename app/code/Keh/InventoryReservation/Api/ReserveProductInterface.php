<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api;

use Keh\InventoryReservation\Api\Data\ReservationRequestInterface;
use Keh\InventoryReservation\Api\Data\ReservationResponseInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Interface for reserving products by SKU in magento MSI.
 * @api
 */
interface ReserveProductInterface
{
    public const string KEH_MANUAL_ITEM_RESERVATION = 'keh_reservation';

    /**
     * Reserve a product by SKU.
     * It will save entity in inventory_reservation AND keh_inventory_reservation.
     *
     * @param ReservationRequestInterface $request The reservation request containing SKU, quantity, and other details
     *
     * @return ReservationResponseInterface Response containing status, message, and reservation data
     * @throws InputException If the input is invalid
     * @throws NoSuchEntityException If the product does not exist
     * @throws CouldNotSaveException If the reservation could not be saved
     */
    public function execute(
        ReservationRequestInterface $request
    ): ReservationResponseInterface;
}
