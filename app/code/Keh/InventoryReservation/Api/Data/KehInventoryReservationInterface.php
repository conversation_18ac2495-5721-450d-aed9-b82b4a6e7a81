<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api\Data;

/**
 * Interface for Keh Inventory Reservation entity
 * @api
 */
interface KehInventoryReservationInterface
{
    public const string ENTITY_ID = 'entity_id';
    public const string SKU = 'sku';
    public const string QUANTITY = 'quantity';
    public const string CUSTOMER_EMAIL = 'customer_email';
    public const string ADMIN_EMAIL = 'admin_email';
    public const string CREATED_AT = 'created_at';
    public const string CANCEL_AT = 'cancel_at';
    public const string RESERVATION_ID = 'reservation_id';

    /**
     * Get Entity ID
     *
     * @return int|null
     */
    public function getEntityId(): ?int;

    /**
     * Set Entity ID
     *
     * @param int $entityId
     * @return $this
     */
    public function setEntityId(int $entityId): self;

    /**
     * Get SKU
     *
     * @return string
     */
    public function getSku(): string;

    /**
     * Set SKU
     *
     * @param string $sku
     * @return $this
     */
    public function setSku(string $sku): self;

    /**
     * Get Quantity
     *
     * @return float
     */
    public function getQuantity(): float;

    /**
     * Set Quantity
     *
     * @param float $quantity
     * @return $this
     */
    public function setQuantity(float $quantity): self;

    /**
     * Get Customer Email
     *
     * @return string
     */
    public function getCustomerEmail(): string;

    /**
     * Set Customer Email
     *
     * @param string $customerEmail
     * @return $this
     */
    public function setCustomerEmail(string $customerEmail): self;

    /**
     * Get Admin Email
     *
     * @return string
     */
    public function getAdminEmail(): string;

    /**
     * Set Admin Email
     *
     * @param string $adminEmail
     * @return $this
     */
    public function setAdminEmail(string $adminEmail): self;

    /**
     * Get Created At
     *
     * @return string
     */
    public function getCreatedAt(): string;

    /**
     * Set Created At
     *
     * @param string $createdAt
     * @return $this
     */
    public function setCreatedAt(string $createdAt): self;

    /**
     * Get Cancel At
     *
     * @return string
     */
    public function getCancelAt(): string;

    /**
     * Set Cancel At
     *
     * @param string $cancelAt
     * @return $this
     */
    public function setCancelAt(string $cancelAt): self;

    /**
     * Get data inventory_reservation.reservation_id
     *
     * @return int
     */
    public function getReservationId(): int;

    /**
     * Set data from inventory_reservation.reservation_id
     *
     * @param int $reservationId
     * @return self
     */
    public function setReservationId(int $reservationId): self;

    /**
     * Method to avoid constructing dataObject for api response.
     *
     * @return array
     */
    public function toObjectArray(): array;
}
