<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api\Data;

/**
 * Interface for reservation response data
 * @api
 */
interface ReservationResponseInterface
{
    public const string STATUS = 'status';
    public const string MESSAGE = 'message';
    public const string RESERVATION = 'reservation';

    /**
     * @return bool
     */
    public function getStatus(): bool;

    /**
     * @param bool $status
     * @return ReservationResponseInterface
     */
    public function setStatus(bool $status): ReservationResponseInterface;

    /**
     * @return string|null
     */
    public function getMessage(): ?string;

    /**
     * @param string|null $message
     * @return ReservationResponseInterface
     */
    public function setMessage(?string $message): ReservationResponseInterface;

    /**
     * @return string
     */
    public function getReservation(): string;

    /**
     * @param string $reservation
     * @return ReservationResponseInterface
     */
    public function setReservation(string $reservation): ReservationResponseInterface;
}
