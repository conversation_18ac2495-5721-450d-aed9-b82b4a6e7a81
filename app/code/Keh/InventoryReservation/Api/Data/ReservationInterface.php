<?php

declare(strict_types=1);

namespace Keh\InventoryReservation\Api\Data;

use Magento\InventoryReservationsApi\Model\ReservationInterface as ParentReservationInterface;

interface ReservationInterface extends ParentReservationInterface
{
    /**
     * @param int|null $reservationId
     * @return ReservationInterface
     */
    public function setReservationId(?int $reservationId): ReservationInterface;

    /**
     * @param int $stockId
     * @return ReservationInterface
     */
    public function setStockId(int $stockId): ReservationInterface;

    /**
     * @param string $sku
     * @return ReservationInterface
     */
    public function setSku(string $sku): ReservationInterface;

    /**
     * @param float $quantity
     * @return ReservationInterface
     */
    public function setQuantity(float $quantity): ReservationInterface;

    /**
     * @param string|null $metaData
     * @return ReservationInterface
     */
    public function setMetadata(?string $metaData): ReservationInterface;
}
