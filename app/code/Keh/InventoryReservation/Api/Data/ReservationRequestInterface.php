<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api\Data;

/**
 * Interface for reservation request data
 * @api
 */
interface ReservationRequestInterface
{
    public const string SKU = 'sku';
    public const string QUANTITY = 'quantity';
    public const string CANCEL_AT = 'cancel_at';
    public const string CUSTOMER_EMAIL = 'customer_email';
    public const string ADMIN_EMAIL = 'admin_email';

    /**
     * Get SKU
     *
     * @return string
     */
    public function getSku(): string;

    /**
     * Set SKU
     *
     * @param string $sku
     * @return ReservationRequestInterface
     */
    public function setSku(string $sku): ReservationRequestInterface;

    /**
     * Get Quantity
     *
     * @return float
     */
    public function getQuantity(): float;

    /**
     * Set Quantity
     *
     * @param float $quantity
     * @return ReservationRequestInterface
     */
    public function setQuantity(float $quantity): ReservationRequestInterface;

    /**
     * Get Cancel At
     *
     * @return string|null
     */
    public function getCancelAt(): ?string;

    /**
     * Set Cancel At
     *
     * @param string|null $cancelAt
     * @return ReservationRequestInterface
     */
    public function setCancelAt(?string $cancelAt): ReservationRequestInterface;

    /**
     * Get Customer Email
     *
     * @return string
     */
    public function getCustomerEmail(): string;

    /**
     * Set Customer Email
     *
     * @param string $customerEmail
     * @return ReservationRequestInterface
     */
    public function setCustomerEmail(string $customerEmail): ReservationRequestInterface;

    /**
     * Get Admin Email
     *
     * @return string
     */
    public function getAdminEmail(): string;

    /**
     * Set Admin Email
     *
     * @param string $adminEmail
     * @return ReservationRequestInterface
     */
    public function setAdminEmail(string $adminEmail): ReservationRequestInterface;
}
