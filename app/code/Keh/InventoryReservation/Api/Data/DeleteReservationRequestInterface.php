<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api\Data;

/**
 * Interface for delete reservation request data
 * @api
 */
interface DeleteReservationRequestInterface
{
    public const string SKU = 'sku';
    public const string CUSTOMER_EMAIL = 'customer_email';

    /**
     * Get SKU
     *
     * @return string
     */
    public function getSku(): string;

    /**
     * Set SKU
     *
     * @param string $sku
     * @return DeleteReservationRequestInterface
     */
    public function setSku(string $sku): DeleteReservationRequestInterface;

    /**
     * Get Customer Email
     *
     * @return string
     */
    public function getCustomerEmail(): string;

    /**
     * Set Customer Email
     *
     * @param string $customerEmail
     * @return DeleteReservationRequestInterface
     */
    public function setCustomerEmail(string $customerEmail): DeleteReservationRequestInterface;
}
