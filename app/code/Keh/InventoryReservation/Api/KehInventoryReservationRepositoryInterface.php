<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Api;

use Keh\InventoryReservation\Api\Data\KehInventoryReservationInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Interface for Keh Inventory Reservation Repository
 * @api
 */
interface KehInventoryReservationRepositoryInterface
{
    /**
     * Save Keh Inventory Reservation
     *
     * @param KehInventoryReservationInterface $reservation
     * @return KehInventoryReservationInterface
     * @throws CouldNotSaveException
     */
    public function save(KehInventoryReservationInterface $reservation): KehInventoryReservationInterface;

    /**
     * Get Keh Inventory Reservation by ID
     *
     * @param int $entityId
     * @return KehInventoryReservationInterface
     * @throws NoSuchEntityException
     */
    public function getById(int $entityId): KehInventoryReservationInterface;

    /**
     * Get Keh Inventory Reservation by SKU
     *
     * @param string $sku
     * @return KehInventoryReservationInterface[]
     */
    public function getBySku(string $sku): array;

    /**
     * Get list of Keh Inventory Reservations
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     */
    public function getList(SearchCriteriaInterface $searchCriteria): SearchResultsInterface;

    /**
     * Delete Keh Inventory Reservation
     *
     * @param KehInventoryReservationInterface $reservation
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(KehInventoryReservationInterface $reservation): bool;

    /**
     * Delete Keh Inventory Reservation by ID
     *
     * @param int $entityId
     * @return bool
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function deleteById(int $entityId): bool;
}
