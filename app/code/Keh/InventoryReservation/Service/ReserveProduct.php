<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Service;

use Keh\InventoryReservation\Api\Data\ReservationInterfaceFactory;
use Keh\InventoryReservation\Api\Data\ReservationResponseInterfaceFactory;
use Keh\InventoryReservation\Api\Data\KehInventoryReservationInterface;
use Keh\InventoryReservation\Api\Data\ReservationInterface;
use Keh\InventoryReservation\Api\Data\ReservationRequestInterface;
use Keh\InventoryReservation\Api\Data\ReservationResponseInterface;
use Keh\InventoryReservation\Api\KehInventoryReservationRepositoryInterface;
use Keh\InventoryReservation\Api\ReserveProductInterface;
use Keh\InventoryReservation\Model\ConfigProvider;
use Keh\InventoryReservation\Model\InventoryReservationRepository;
use Keh\InventoryReservation\Model\KehInventoryReservationFactory;
use Keh\InventoryReservation\Exceptions\ReservationCouldNotSaveException;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogInventory\Api\StockConfigurationInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Validation\ValidationException;
use Magento\InventoryConfigurationApi\Api\GetStockItemConfigurationInterface;
use Magento\InventoryConfigurationApi\Exception\SkuIsNotAssignedToStockException;
use Magento\InventorySalesApi\Api\Data\SalesChannelInterface;
use Magento\InventorySalesApi\Api\Data\SalesChannelInterfaceFactory;
use Magento\InventorySalesApi\Api\GetProductSalableQtyInterface;
use Magento\InventorySalesApi\Api\GetStockBySalesChannelInterface;
use Magento\InventoryIndexer\Indexer\InventoryIndexer;
use Magento\InventoryApi\Api\GetSourceItemsBySkuInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for reserving products by SKU
 */
class ReserveProduct implements ReserveProductInterface
{
    private ?int $cachedStockId = null;

    public function __construct(
        private readonly KehInventoryReservationRepositoryInterface $kehReservationRepository,
        private readonly KehInventoryReservationFactory $reservationFactory,
        private readonly ReservationResponseInterfaceFactory $reservationResponseFactory,
        private readonly ProductRepositoryInterface $productRepository,
        private readonly InventoryReservationRepository $inventoryReservationRepository,
        private readonly SerializerInterface $serializer,
        private readonly ConfigProvider $configProvider,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly Json $json,
        private readonly GetStockBySalesChannelInterface $getStockBySalesChannel,
        private readonly SalesChannelInterfaceFactory $salesChannelFactory,
        private readonly GetProductSalableQtyInterface $getProductSalableQty,
        private readonly ReservationInterfaceFactory $reservationInterfaceFactory,
        private readonly GetStockItemConfigurationInterface $getStockItemConfiguration,
        private readonly InventoryIndexer $inventoryIndexer,
        private readonly GetSourceItemsBySkuInterface $getSourceItemsBySku,
        private readonly StockConfigurationInterface $stockConfiguration,
        private readonly LoggerInterface $logger
    ) { }

    /**
     * @inheritdoc
     */
    public function execute(
        ReservationRequestInterface $request
    ): ReservationResponseInterface {
        /** @var ReservationResponseInterface $response */
        $response = $this->reservationResponseFactory->create();

        try {
            $this->validateReservationRequest($request);
            $this->validateAvailability($request);
            $this->validateConfigurations($request);

            // Create and save MSI reservation.
            $msiInventoryReservation = $this->createMsiReservations($request);
            // Create and save Keh reservation.
            $kehReservation = $this->saveKehReservation($request, $msiInventoryReservation);
            //Reindex stock
            $this->reindexProduct($request);

            $response->setStatus(true);
            $response->setReservation($this->json->serialize($kehReservation->toObjectArray()));
        } catch (ReservationCouldNotSaveException $e) {
            $response->setStatus(false);
            $response->setMessage($e->getMessage());
            $this->logger->critical(
                sprintf(
                    '%s KEH reservation is not created, while msi reservation is created, ACTION required!,
                    inventory_reservation.reservation_id: %s , message: %s',
                    __METHOD__,
                    $msiInventoryReservation->getReservationId(),
                    $e->getMessage()
                )
            );
        } catch (InputException | NoSuchEntityException | LocalizedException | SkuIsNotAssignedToStockException $e) {
            $response->setStatus(false);
            $response->setMessage($e->getMessage());
        } catch (\Exception $e) {
            $response->setStatus(false);
            $response->setMessage(sprintf('Could not save reservation: %1', $e->getMessage()));
        }

        return $response;
    }

    private function validateReservationRequest(ReservationRequestInterface $request): void
    {
        // Verify that the product exists
        try {
            $this->productRepository->get($request->getSku());
        } catch (NoSuchEntityException) {
            throw new NoSuchEntityException(__('Product with SKU "%1" does not exist', $request->getSku()));
        }
        // check if a product is already reserved for this customer.
        try {
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter(KehInventoryReservationInterface::CUSTOMER_EMAIL, $request->getCustomerEmail())
                ->addFilter(KehInventoryReservationInterface::SKU, $request->getSku())
                ->create();
            $reservations = $this->kehReservationRepository->getList($searchCriteria);
        } catch (\Exception $e) {
            throw new LocalizedException(
                __('Could not check for duplicate reservations, error: %1', $e->getMessage())
            );
        }
        if (!empty($reservations->getItems())) {
            throw new LocalizedException(
                __('Reservation already exists for this SKU and customer email')
            );
        }
    }

    /**
     * @param ReservationRequestInterface $request
     * @return void
     * @throws InputException
     * @throws LocalizedException
     */
    private function validateAvailability(ReservationRequestInterface $request): void
    {
        $stockId = $this->getStockId($request);
        $availableQty = $this->getProductSalableQty->execute($request->getSku(), $stockId);
        if ($availableQty < 1) {
            throw new LocalizedException(__('Product is out of stock, you can`t reserve it.'));
        }
    }

    /**
     * This method mimics native functionality:
     * module-inventory-sales/Plugin/InventoryReservationsApi/PreventAppendReservationOnNotManageItemsInStockPlugin.php
     *
     * @param ReservationRequestInterface $request
     * @return void
     * @throws LocalizedException
     * @throws SkuIsNotAssignedToStockException
     */
    private function validateConfigurations(ReservationRequestInterface $request): void
    {
        if (!$this->stockConfiguration->canSubtractQty()) {
            throw new LocalizedException(__('Config "cataloginventory/options/can_subtract" is disabled'));
        }
        $stockItemConfiguration = $this->getStockItemConfiguration->execute(
            $request->getSku(),
            $this->getStockId($request)
        );

        if (!$stockItemConfiguration->isManageStock()) {
            throw new LocalizedException(__('"Manage Stock" is disabled for this SKU.'));
        }
    }

    private function getStockId(ReservationRequestInterface $request): int
    {
        if (!$this->cachedStockId) {
            $salesChannel = $this->salesChannelFactory->create([
                'data' => [
                    'type' => SalesChannelInterface::TYPE_WEBSITE,
                    'code' => 'shop'
                ]
            ]);
            $stockId = $this->getStockBySalesChannel->execute($salesChannel)->getStockId();

            if (!$stockId) {
                throw new LocalizedException(__('Could not find stock ID for the SKU "%1"', $request->getSku()));
            }
            $this->cachedStockId = (int)$stockId;
        }

        return $this->cachedStockId;
    }

    /**
     * @param ReservationRequestInterface $request
     * @param ReservationInterface $reservation
     * @return KehInventoryReservationInterface
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws \DateMalformedStringException
     */
    private function saveKehReservation(
        ReservationRequestInterface $request,
        ReservationInterface $reservation
    ): KehInventoryReservationInterface {
        /** @var KehInventoryReservationInterface $kehReservation */
        $kehReservation = $this->reservationFactory->create();
        $kehReservation
            ->setSku($request->getSku())
            ->setQuantity($request->getQuantity())
            ->setCustomerEmail($request->getCustomerEmail())
            ->setAdminEmail($request->getAdminEmail())
            ->setCancelAt($this->getCancelDate($request->getCancelAt()))
            ->setReservationId((int)$reservation->getReservationId());

        return $this->kehReservationRepository->save($kehReservation);
    }

    /**
     * @param string|null $cancelAt
     * @return string
     * @throws InputException
     * @throws \DateMalformedStringException
     */
    private function getCancelDate(?string $cancelAt): string
    {
        // Calculate cancel date if not provided
        if ($cancelAt === null) {
            $newDate = new \DateTime();
            $newDate->modify(sprintf('+%d days', $this->configProvider->getReservationDays()));
            $cancelAt = $newDate->format('Y-m-d');
        } else {
            // Validate date format
            $date = new \DateTime($cancelAt);
            if ($date->format('Y-m-d') !== $cancelAt) {
                throw new InputException(__('Invalid date format. Use Y-m-d'));
            }
        }
        return $cancelAt;
    }


    /**
     * It saves through a custom keh object and custom Repo since a native way of doing it
     * (vendor/magento/module-inventory-sales/Model/PlaceReservationsForSalesEvent.php:111) forces us to do additional
     *  requests to the DB to validate insertions, since we don`t know if it were inserted or not.
     *
     * @param ReservationRequestInterface $request
     * @return ReservationInterface
     * @throws CouldNotSaveException
     * @throws InputException
     * @throws LocalizedException
     * @throws ValidationException
     */
    private function createMsiReservations(
        ReservationRequestInterface $request,
    ): ReservationInterface {
        $inventoryReservation = $this->reservationInterfaceFactory->create();
        $inventoryReservation
            ->setSku($request->getSku())
            ->setQuantity(-$request->getQuantity())
            ->setStockId($this->getStockId($request))
            ->setMetadata(
                $this->serializer->serialize(
                    [
                        'event_type' => self::KEH_MANUAL_ITEM_RESERVATION,
                        'object_type' => 'product',
                        'object_id' => '',
                        'object_increment_id' => ''
                    ]
                )
            );
        $inventoryReservation = $this->inventoryReservationRepository->save($inventoryReservation);
        if (!$inventoryReservation->getReservationId()) {
            throw new CouldNotSaveException(__('Could not save inventory_reservation.'));
        }

        return $inventoryReservation;
    }

    private function reindexProduct(ReservationRequestInterface $request): void
    {
        $sourceItems = $this->getSourceItemsBySku->execute($request->getSku());
        $oldAttrOptionIds = array_map(function ($sourceItem) {
            return (int)$sourceItem->getSourceItemId();
        }, $sourceItems);
        $this->inventoryIndexer->executeList(array_values($oldAttrOptionIds));
    }
}
