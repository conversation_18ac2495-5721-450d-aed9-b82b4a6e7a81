<?php
/**
 * Copyright © Keh, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace Keh\InventoryReservation\Service;

use Keh\InventoryReservation\Api\DeleteReservationInterface;
use Keh\InventoryReservation\Api\Data\DeleteReservationRequestInterface;
use Keh\InventoryReservation\Api\InventoryReservationRepositoryInterface;
use Keh\InventoryReservation\Api\KehInventoryReservationRepositoryInterface;
use Keh\InventoryReservation\Model\ResourceModel\KehInventoryReservation\CollectionFactory;
use Keh\InventoryReservation\Exceptions\ReservationCouldNotDeleteException;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\InventoryIndexer\Indexer\InventoryIndexer;
use Magento\InventoryApi\Api\GetSourceItemsBySkuInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for deleting product reservations
 */
readonly class DeleteReservation implements DeleteReservationInterface
{
    public function __construct(
        private KehInventoryReservationRepositoryInterface $kehReservationRepository,
        private InventoryReservationRepositoryInterface $inventoryReservationRepository,
        private SearchCriteriaBuilder $searchCriteriaBuilder,
        private GetSourceItemsBySkuInterface $getSourceItemsBySku,
        private InventoryIndexer $inventoryIndexer,
        private LoggerInterface $logger
    ) { }

    /**
     * Delete reservation by request
     *
     * @param DeleteReservationRequestInterface $request
     * @return bool
     * @throws NoSuchEntityException
     * @throws CouldNotDeleteException
     */
    public function execute(
        DeleteReservationRequestInterface $request
    ): bool
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('sku', $request->getSku())
            ->addFilter('customer_email', $request->getCustomerEmail())
            ->create();

        $reservations = $this->kehReservationRepository->getList($searchCriteria);

        if (empty($reservations->getItems())) {
            throw new NoSuchEntityException(
                __('No reservation found for SKU "%1" and customer email "%2"',
                    $request->getSku(),
                    $request->getCustomerEmail()
                )
            );
        }

        // Get the first reservation (should be only one based on our search criteria)
        $kehReservation = current($reservations->getItems());

        // Delete from msi inventory_reservation
        $this->inventoryReservationRepository->deleteById($kehReservation->getReservationId());

        // Delete from keh_inventory_reservation
        try {
            $kehReservationId = $kehReservation->getEntityId();
            $this->kehReservationRepository->delete($kehReservation);
        } catch (ReservationCouldNotDeleteException $exception) {
            $this->logger->critical(
                sprintf(
                    '%s KEH reservation is not deleted, while msi reservation is deleted, ACTION required!,
                    keh_inventory_reservation.entity_id: %s, message: %s',
                    __METHOD__,
                    $kehReservationId,
                    $exception->getMessage()
                )
            );
        }


        $this->reindexProduct($request->getSku());

        return true;
    }

    /**
     * Reindex product after reservation deletion
     *
     * @param string $sku
     * @return void
     */
    private function reindexProduct(string $sku): void
    {
        $sourceItems = $this->getSourceItemsBySku->execute($sku);
        $sourceItemIds = array_map(function ($sourceItem) {
            return (int)$sourceItem->getSourceItemId();
        }, $sourceItems);

        if (!empty($sourceItemIds)) {
            $this->inventoryIndexer->executeList(array_values($sourceItemIds));
        }
    }
}
