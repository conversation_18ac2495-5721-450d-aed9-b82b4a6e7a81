<?php
declare(strict_types=1);

namespace Keh\TradeVoucher\Plugin;

use Keh\TradeVoucher\Api\OrderTradeVoucherRepositoryInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

readonly class OrderSaveTradeVouchers
{
    public function __construct(
        private OrderTradeVoucherRepositoryInterface $orderTradeVoucherRepository,
    ) {

    }

    public function afterSave(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ) {
        $tradeVouchers = $order->getExtensionAttributes()?->getTradeVouchers() ?? [];

        foreach ($tradeVouchers as $tradeVoucher) {
            if (!$tradeVoucher->getOrderId()) {
                $tradeVoucher->setOrderId((int)$order->getId());
            }
        }

        if ($tradeVouchers) {
            $this->orderTradeVoucherRepository->save($tradeVouchers);
        }

        return $order;
    }
}
