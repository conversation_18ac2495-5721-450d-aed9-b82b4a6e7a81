<?php

declare(strict_types=1);

namespace Keh\Kafka\Api\Data;

interface KafkaConsumerResponseInterface
{
    public const string RESPONSE_STATUS_SUCCESS = 'success';
    public const string RESPONSE_STATUS_ERROR = 'error';
    public const string FIELD_MESSAGE = 'message';
    public const string FIELD_TOPIC = 'topic';
    public const string FIELD_STATUS = 'status';
    public const string FIELD_PARTITION = 'partition';
    public const string FIELD_OFFSET = 'offset';
    public const string FIELD_MESSAGE_DATA = 'message_data';

    /**
     * @return string|null
     */
    public function getMessage(): ?string;

    /**
     * @param string $message
     * @return self
     */
    public function setMessage(string $message): self;

    /**
     * @param string $topic
     * @return self
     */
    public function setTopic(string $topic): self;

    /**
     * @return string|null
     */
    public function getTopic(): ?string;

    /**
     * @param string $status
     * @return self
     */
    public function setStatus(string $status): self;

    /**
     * @return string
     */
    public function getStatus(): string;

    /**
     * @param string $partition
     * @return self
     */
    public function setPartition(string $partition): self;

    /**
     * @return string|null
     */
    public function getPartition(): ?string;

    /**
     * @param string $offset
     * @return self
     */
    public function setOffset(string $offset): self;

    /**
     * @return string|null
     */
    public function getOffset(): ?string;

    /**
     * @param array $messageData
     * @return self
     */
    public function setMessageData(array $messageData): self;

    /**
     * @return array|null
     */
    public function getMessageData(): ?array;
}
