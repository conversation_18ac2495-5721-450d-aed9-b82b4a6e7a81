<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Plugin\Magento\Sales\Order\Email\Sender;

use Magento\Sales\Model\Order\Email\Sender\InvoiceSender;
use Magento\Sales\Model\Order\Invoice;
use Keh\BloomreachEngagementEmails\Model\Sales\InvoiceEmail;

/**
 * Send invoice email through Bloomreach Engagement email campaign instead of default email system.
 */
class InvoiceSenderPlugin
{
    public function __construct(
        private readonly InvoiceEmail $invoiceEmail
    ) {}

    /**
     * Intercept invoice email and send via Bloomreach API when applicable.
     *
     * @param InvoiceSender $subject
     * @param callable $proceed
     * @param Invoice $invoice
     * @param bool $forceSyncMode
     * @return bool
     */
    public function aroundSend(
        InvoiceSender $subject,
        callable $proceed,
        Invoice $invoice,
        bool $forceSyncMode = false
    ): bool {
        die;
        try {
            $result = $this->invoiceEmail->sendEmail($invoice);
            if ($result) {
                // If Bloomreach email was sent successfully, we consider the job done.
                return true;
            }
        } catch (\Exception) {
            // No process stop, errors are logged within the InvoiceEmail model.
            // We will proceed with the default Magento email sending in case of an exception.
        }

        // If Bloomreach email sending is disabled, template not configured, or an error occurred,
        // proceed with the default Magento behavior.
        return $proceed($invoice, $forceSyncMode);
    }
}
