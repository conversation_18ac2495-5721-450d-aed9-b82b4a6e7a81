<?php
declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Plugin\Magento\Customer\Model;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\AccountManagement;
use Keh\BloomreachEngagementEmails\Model\Customer\ResetPasswordConfirmationEmail;
use Keh\BloomreachEngagementEmails\Model\Customer\CustomerUpdateConfirmationEmail;

/**
 * Send password reset confirmation email via Bloomreach when password is successfully reset.
 */
class AccountManagementPlugin
{
    public function __construct(
        private readonly ResetPasswordConfirmationEmail $resetPasswordConfirmationEmail,
        private readonly CustomerUpdateConfirmationEmail $customerUpdateConfirmationEmail,
        private readonly CustomerRepositoryInterface $customerRepository
    ) {}

    /**
     * Send password reset confirmation email after successful password reset.
     *
     * @param AccountManagement $subject
     * @param bool $result
     * @param string $email
     * @param string $resetToken
     * @param string $newPassword
     * @return bool
     */
    public function afterResetPassword(
        AccountManagement $subject,
        bool $result,
        string $email,
        string $resetToken,
        string $newPassword
    ): bool {
        // Only send confirmation if the password reset was successful
        if ($result) {
            try {
                $customer = $this->customerRepository->get($email);
                $this->resetPasswordConfirmationEmail->sendEmail($customer);
            } catch (\Exception) {
                // No process stop, errors already logged into bloomreach.log
            }
        }
        
        return $result;
    }
}
