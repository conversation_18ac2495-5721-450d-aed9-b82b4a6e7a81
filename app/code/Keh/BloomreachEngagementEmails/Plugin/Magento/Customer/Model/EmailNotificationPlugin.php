<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Plugin\Magento\Customer\Model;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\EmailNotificationInterface;
use Ke<PERSON>\BloomreachEngagementEmails\Model\Customer\ResetPasswordEmail;
use Keh\BloomreachEngagementEmails\Model\Customer\AccountConfirmationEmail;
use Keh\BloomreachEngagementEmails\Model\Customer\CustomerUpdateConfirmationEmail;

/**
 * Send emails through Bloomreach Engagement email campaign instead of default email system.
 */
class EmailNotificationPlugin
{
    public function __construct(
        private readonly ResetPasswordEmail $resetPasswordEmail,
        private readonly AccountConfirmationEmail $accountConfirmationEmail,
        private readonly CustomerUpdateConfirmationEmail $customerUpdateConfirmationEmail
    ) {}

    /**
     * Disable default password reset confirmation email and send via Bloomreach API instead.
     *
     * @param EmailNotificationInterface $subject
     * @param callable $proceed
     * @param CustomerInterface $customer
     * @return void
     */
    public function aroundPasswordResetConfirmation(
        EmailNotificationInterface $subject,
        callable $proceed,
        CustomerInterface $customer
    ) {
        try {
            $result = $this->resetPasswordEmail->sendEmail($customer);
            if (!$result) {
                // If Bloomreach email sending is disabled or template not configured, proceed with default behavior
                return $proceed($customer);
            }
        } catch (\Exception) {
            // No process stop, errors already logged into bloomreach.log
        }
    }

    /**
     * Disable default password reset request email and send via Bloomreach API instead.
     *
     * @param EmailNotificationInterface $subject
     * @param callable $proceed
     * @param CustomerInterface $customer
     * @return void
     */
    public function aroundPasswordReminder(
        EmailNotificationInterface $subject,
        callable $proceed,
        CustomerInterface $customer
    ) {
        try {
            $result = $this->resetPasswordEmail->sendEmail($customer);
            if (!$result) {
                // If Bloomreach email sending is disabled or template not configured, proceed with default behavior
                return $proceed($customer);
            }
        } catch (\Exception) {
            // No process stop, errors already logged into bloomreach.log
        }
    }

    /**
     * Intercept new account emails and send account confirmation via Bloomreach API when applicable.
     *
     * @param EmailNotificationInterface $subject
     * @param callable $proceed
     * @param CustomerInterface $customer
     */
    public function aroundNewAccount(
        EmailNotificationInterface $subject,
        callable $proceed,
        CustomerInterface $customer
    ) {
        try {
            $result = $this->accountConfirmationEmail->sendEmail($customer);
            if (!$result) {
                // If Bloomreach email sending is disabled or template not configured, proceed with default behavior
                return $proceed($customer);
            }
        } catch (\Exception) {
            // No process stop, errors already logged into bloomreach.log
        }
    }

    /**
     * Intercept credentials changed emails and send via Bloomreach API instead.
     * This prevents default "Change Email Template" and "Change Email and Password Template" emails.
     *
     * @param EmailNotificationInterface $subject
     * @param callable $proceed
     * @param CustomerInterface $savedCustomer
     * @param string $origCustomerEmail
     * @param bool $isPasswordChanged
     * @return void
     */
    public function aroundCredentialsChanged(
        EmailNotificationInterface $subject,
        callable $proceed,
        CustomerInterface $savedCustomer,
        $origCustomerEmail,
        $isPasswordChanged = false
    ): void {
        $emailChanged = $origCustomerEmail !== $savedCustomer->getEmail();
        
        if (!$emailChanged && !$isPasswordChanged) {
            // No changes to email or password, nothing to do
            return;
        }
        
        try {
            // Determine what was updated
            $updatedFields = [];
            if ($emailChanged) {
                $updatedFields[] = 'email';
            }
            if ($isPasswordChanged) {
                $updatedFields[] = 'password';
            }
            
            $updatedInfo = implode(', ', $updatedFields);
            
            // Send our custom Bloomreach email
            $result = $this->customerUpdateConfirmationEmail->sendEmail($savedCustomer, $origCustomerEmail, $updatedInfo);

            if (!$result) {
                // If Bloomreach email sending is disabled or template not configured, 
                // proceed with default behavior
                $proceed($savedCustomer, $origCustomerEmail, $isPasswordChanged);
                return;
            }
            
            // Successfully sent via Bloomreach, don't call the default method
            
        } catch (\Exception) {
            // If there's an error with Bloomreach email, fall back to default behavior
            // Errors are already logged in bloomreach.log
            $proceed($savedCustomer, $origCustomerEmail, $isPasswordChanged);
        }
    }
}
