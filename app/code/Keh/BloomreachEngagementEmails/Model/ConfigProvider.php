<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class ConfigProvider
{
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_ENABLED
        = 'bloomreach/engagement_emails/enabled';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_API_TARGET
        = 'bloomreach/engagement_emails/api_target';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_PROJECT_TOKEN
        = 'bloomreach/engagement_emails/project_token';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_PRIVATE_API_KEY
        = 'bloomreach/engagement_emails/private_api_key';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_PRIVATE_API_SECRET
        = 'bloomreach/engagement_emails/private_api_secret';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_GIFT_CARD_CAPTURE_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/gift_card_capture_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_GIFT_CARD_HOLD_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/gift_card_hold_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_GIFT_CARD_CANCEL_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/gift_card_cancel_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_RESET_PASSWORD_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/customer_reset_password_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_ADMIN_RESET_PASSWORD_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/admin_reset_password_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_RESET_PASSWORD_CONFIRMATION_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/customer_reset_password_confirmation_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_RMA_REQUEST_INVOICE_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/rma_request_invoice_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_ACCOUNT_CONFIRMATION_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/customer_account_confirmation_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_UPDATE_CONFIRMATION_EMAIL_TEMPLATE_ID
        = 'bloomreach/engagement_emails/customer_update_confirmation_template_id';
    public const string XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_INVOICE_EMAIL_ID
        = 'bloomreach/engagement_emails/invoice_template_id';


    public function __construct(
        private readonly ScopeConfigInterface $scopeConfig
    ) {}

    /**
     * @param int|null $storeId
     * @return bool
     */
    public function getIsEngagementEmailsEnabled(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     *
     * @return string
     */
    public function getEngagementEmailsApiTarget(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_API_TARGET,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     *
     * @return string
     */
    public function getEngagementEmailsProjectToken(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_PROJECT_TOKEN,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     *
     * @return string
     */
    public function getEngagementEmailsPrivateApiKey(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_PRIVATE_API_KEY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     *
     * @return string
     */
    public function getEngagementEmailsPrivateApiSecret(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_PRIVATE_API_SECRET,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getGiftCardCaptureTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_GIFT_CARD_CAPTURE_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getGiftCardHoldTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_GIFT_CARD_HOLD_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getGiftCardCancelTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_GIFT_CARD_CANCEL_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getCustomerResetPasswordTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_RESET_PASSWORD_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getAdminResetPasswordTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_ADMIN_RESET_PASSWORD_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getCustomerResetPasswordConfirmationTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_RESET_PASSWORD_CONFIRMATION_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getRmaRequestInvoiceTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_RMA_REQUEST_INVOICE_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getCustomerAccountConfirmationTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_ACCOUNT_CONFIRMATION_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getCustomerUpdateConfirmationTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_CUSTOMER_UPDATE_CONFIRMATION_EMAIL_TEMPLATE_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param int|null $storeId
     * @return string
     */
    public function getInvoiceTemplateId(?int $storeId = null): string
    {
        return (string)$this->scopeConfig->getValue(
            self::XML_PATH_TO_BLOOMREACH_ENGAGEMENT_EMAILS_INVOICE_EMAIL_ID,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
