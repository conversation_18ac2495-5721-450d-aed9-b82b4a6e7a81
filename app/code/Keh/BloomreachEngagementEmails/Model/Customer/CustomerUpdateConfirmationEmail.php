<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Model\Customer;

use Keh\BloomreachEngagementEmails\Api\Data\ApiEmailsRequestInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Keh\BloomreachEngagementEmails\Api\Data\ApiEmailsRequestInterfaceFactory;
use Keh\BloomreachEngagementEmails\Service\ApiEmails;
use Keh\BloomreachEngagementEmails\Model\ConfigProvider as EmailsConfigProvider;
use Keh\Bloomreach\Logger\Logger;

/**
 * Send customer update confirmation email through Bloomreach Engagement email campaign
 */
readonly class CustomerUpdateConfirmationEmail
{
    public function __construct(
        private ApiEmails $apiEmails,
        private ApiEmailsRequestInterfaceFactory $apiEmailsRequestInterfaceFactory,
        private EmailsConfigProvider $emailsConfigProvider,
        private Logger $logger
    ) {}

    /**
     * Send customer update confirmation email via Bloomreach Engagement
     *
     * @param CustomerInterface $customer
     * @param string $originalEmail Original email before update
     * @param string $updatedInfo What was updated: "email", "password" or "email, password"
     * @return bool
     * @throws \Exception
     */
    public function sendEmail(CustomerInterface $customer, string $originalEmail, string $updatedInfo): bool
    {
        $templateId = $this->emailsConfigProvider->getCustomerUpdateConfirmationTemplateId();
        if (!$this->emailsConfigProvider->getIsEngagementEmailsEnabled() || !$templateId) {
            $this->logger->info(sprintf(
                "%s, Bloomreach Engagement Emails is disabled or template ID not configured for customer update confirmation",
                __METHOD__
            ));
            return false;
        }

        /** @var ApiEmailsRequestInterface $request */
        $request = $this->apiEmailsRequestInterfaceFactory->create();
        $request
            ->setProjectPath('email/v2/projects')
            ->setEndpointPath('sync')
            ->setProperties($this->getBodyProperties($customer, $templateId, $originalEmail, $updatedInfo));

        $this->sendRequest($request);
        return true;
    }

    /**
     * Prepare email data for customer update confirmation email
     *
     * @param CustomerInterface $customer
     * @param string $templateId
     * @param string $originalEmail Original email before update
     * @param string $updatedInfo
     * @return array
     */
    private function getBodyProperties(CustomerInterface $customer, string $templateId, string $originalEmail, string $updatedInfo): array
    {
        return [
            'campaign_name' => 'Customer Update Confirmation',
            'recipient' => [
                'customer_ids' => [
                    'email_id' => $originalEmail,
                ],
                'email' => $originalEmail
            ],
            'email_content' => [
                'template_id' => $templateId,
                'params' => [
                    'customer' => [
                        'email' => $originalEmail
                    ],
                    'updated_info' => $updatedInfo
                ]
            ],
            'settings' => [
                'transfer_identity' => 'enabled'
            ]
        ];
    }

    /**
     * Send request and log the result
     *
     * @param ApiEmailsRequestInterface $request
     * @return void
     */
    private function sendRequest(ApiEmailsRequestInterface $request): void
    {
        $recipient = $request->getProperties()['recipient']['email'];
        $templateId = $request->getProperties()['email_content']['template_id'];
        $updatedInfo = $request->getProperties()['email_content']['params']['updated_info'];
        
        $this->logger->info(sprintf(
            "%s, try to send customer update confirmation email to: %s, template id: %s, updated info: %s",
            __METHOD__,
            $recipient,
            $templateId,
            $updatedInfo
        ));

        // send email through Bloomreach Engagement
        $response = $this->apiEmails->postEmail($request);

        if ($response->getStatus() == 200) {
            $this->logger->info(sprintf(
                "%s, sent customer update confirmation email to: %s, template id: %s, updated info: %s",
                __METHOD__,
                $recipient,
                $templateId,
                $updatedInfo
            ));
        } else {
            $this->logger->error(sprintf(
                "%s, failed to send customer update confirmation email to: %s, template id: %s, updated info: %s, code: %s, message: %s",
                __METHOD__,
                $recipient,
                $templateId,
                $updatedInfo,
                $response->getStatus(),
                $response->getMessage()
            ));
        }
    }
}
