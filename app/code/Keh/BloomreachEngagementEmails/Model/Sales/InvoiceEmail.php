<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Model\Sales;

use Keh\BloomreachEngagementEmails\Model\AbstractEmailService;
use Keh\BloomreachEngagementEmails\Api\Data\ApiEmailsRequestInterfaceFactory;
use Ke<PERSON>\BloomreachEngagementEmails\Model\ConfigProvider as EmailsConfigProvider;
use Keh\BloomreachEngagementEmails\Service\ApiEmails;
use Keh\Bloomreach\Logger\Logger;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Framework\Url;
use Magento\Sales\Model\Order\Invoice;
// START: New dependency for PDF generation
use Magento\Sales\Model\Order\Pdf\Invoice as PdfInvoice;
// END: New dependency for PDF generation
use Magento\Store\Model\StoreManagerInterface;

/**
 * Send invoice email through Bloomreach Engagement email campaign
 */
readonly class InvoiceEmail extends AbstractEmailService
{
    public function __construct(
        ApiEmails $apiEmails,
        ApiEmailsRequestInterfaceFactory $apiEmailsRequestInterfaceFactory,
        EmailsConfigProvider $emailsConfigProvider,
        Logger $logger,
        Url $frontendUrlBuilder,
        StoreManagerInterface $storeManager,
        protected PriceCurrencyInterface $priceCurrency,
        // START: New dependency for PDF generation
        private PdfInvoice $pdfInvoice
        // END: New dependency for PDF generation
    ) {
        parent::__construct(
            $apiEmails,
            $apiEmailsRequestInterfaceFactory,
            $emailsConfigProvider,
            $logger,
            $frontendUrlBuilder,
            $storeManager
        );
    }

    protected function getTemplateId(): string
    {
        return $this->emailsConfigProvider->getInvoiceTemplateId();
    }

    protected function getCampaignName(): string
    {
        die;
        return 'Invoice Email';
    }

    /**
     * @param Invoice $invoice
     * @param string $templateId
     * @return array
     */
    protected function getBodyProperties($invoice, string $templateId): array
    {
        $order = $invoice->getOrder();
        $currencyCode = $order->getOrderCurrencyCode();

        $items = [];
        foreach ($invoice->getAllItems() as $item) {
            if ($item->getOrderItem()->getParentItem() || $item->isDeleted()) {
                continue;
            }
            $items[] = [
                'name' => $item->getName(),
                'sku' => $item->getSku(),
                'qty' => (int)$item->getQty(),
                'price' => $this->priceCurrency->format($item->getPrice(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode),
                'row_total' => $this->priceCurrency->format($item->getRowTotal(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode),
            ];
        }

        $payload = [
            'campaign_name' => $this->getCampaignName(),
            'recipient' => [
                'customer_ids' => [
                    'email_id' => $order->getCustomerEmail(),
                ],
                'email' => $order->getCustomerEmail()
            ],
            'email_content' => [
                'template_id' => $templateId,
                'params' => [
                    'customer' => [
                        'email' => $order->getCustomerEmail(),
                        'name' => $order->getCustomerName(),
                        'firstname' => $order->getCustomerFirstname(),
                        'lastname' => $order->getCustomerLastname(),
                    ],
                    'order' => [
                        'increment_id' => $order->getIncrementId(),
                        'created_at' => $order->getCreatedAt(),
                        'status' => $order->getStatus(),
                        'state' => $order->getState(),
                        'currency_code' => $currencyCode,
                    ],
                    'invoice' => [
                        'increment_id' => $invoice->getIncrementId(),
                        'created_at' => $invoice->getCreatedAt(),
                        'state' => $invoice->getStateName(),
                    ],
                    'billing_address' => $order->getBillingAddress() ? $order->getBillingAddress()->format('html') : '',
                    'shipping_address' => $order->getShippingAddress() ? $order->getShippingAddress()->format('html') : '',
                    'payment_method' => $order->getPayment()->getMethodInstance()->getTitle(),
                    'items' => $items,
                    'totals' => [
                        'subtotal' => $this->priceCurrency->format($invoice->getSubtotal(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode),
                        'shipping' => $this->priceCurrency->format($invoice->getShippingAmount(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode),
                        'tax' => $this->priceCurrency->format($invoice->getTaxAmount(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode),
                        'discount' => $this->priceCurrency->format($invoice->getDiscountAmount(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode),
                        'grand_total' => $this->priceCurrency->format($invoice->getGrandTotal(), false, PriceCurrencyInterface::DEFAULT_PRECISION, null, $currencyCode)
                    ]
                ]
            ],
            'settings' => [
                'transfer_identity' => 'enabled'
            ]
        ];

        // START: Add PDF invoice attachment
        try {
            // Generate PDF content using Magento's native PDF generator
            $pdfContent = $this->pdfInvoice->getPdf([$invoice])->render();

            if ($pdfContent) {
                $payload['email_content']['attachments'] = [
                    [
                        'filename' => 'invoice-' . $invoice->getIncrementId() . '.pdf',
                        'content' => base64_encode($pdfContent),
                        'content_type' => 'application/pdf'
                    ]
                ];
            }
        } catch (\Exception $e) {
            $this->logger->error(sprintf(
                'Failed to generate PDF attachment for invoice %s. Error: %s',
                $invoice->getIncrementId(),
                $e->getMessage()
            ));
            // Continue to send email without attachment
        }
        // END: Add PDF invoice attachment

        return $payload;
    }
}
