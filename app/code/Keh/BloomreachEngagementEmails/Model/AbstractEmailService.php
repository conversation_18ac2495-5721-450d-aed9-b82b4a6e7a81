<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Model;

use Keh\BloomreachEngagementEmails\Api\Data\ApiEmailsRequestInterface;
use Keh\BloomreachEngagementEmails\Api\Data\ApiEmailsRequestInterfaceFactory;
use Keh\BloomreachEngagementEmails\Service\ApiEmails;
use Keh\BloomreachEngagementEmails\Model\ConfigProvider as EmailsConfigProvider;
use Keh\Bloomreach\Logger\Logger;
use Magento\Framework\Url;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Customer\Api\Data\CustomerInterface;

abstract readonly class AbstractEmailService
{
    public function __construct(
        protected ApiEmails $apiEmails,
        protected ApiEmailsRequestInterfaceFactory $apiEmailsRequestInterfaceFactory,
        protected EmailsConfigProvider $emailsConfigProvider,
        protected Logger $logger,
        protected Url $frontendUrlBuilder,
        protected StoreManagerInterface $storeManager
    ) { }

    /**
     * Prepare email data properties
     *
     * @param mixed $entity The entity (customer, RMA, etc.)
     * @param string $templateId
     * @return array
     */
    abstract protected function getBodyProperties(mixed $entity, string $templateId): array;

    /**
     * Get the template ID for this email type
     *
     * @return string|null
     */
    abstract protected function getTemplateId(): string;

    /**
     * Get the campaign name for logging
     *
     * @return string
     */
    abstract protected function getCampaignName(): string;

    /**
     * Common email sending logic
     *
     * @param mixed $entity
     * @return bool
     */
    public function sendEmail($entity): bool
    {
        $templateId = $this->getTemplateId();
        if (!$this->emailsConfigProvider->getIsEngagementEmailsEnabled() || !$templateId) {
            return false;
        }

        /** @var ApiEmailsRequestInterface $request */
        $request = $this->apiEmailsRequestInterfaceFactory->create();
        $request
            ->setProjectPath('email/v2/projects')
            ->setEndpointPath('sync')
            ->setProperties($this->getBodyProperties($entity, $templateId));

        $this->sendRequest($request);
        return true;
    }

    /**
     * Send request and log the result
     *
     * @param ApiEmailsRequestInterface $request
     * @return void
     */
    protected function sendRequest(ApiEmailsRequestInterface $request): void
    {
        $recipient = $request->getProperties()['recipient']['email'];
        $templateId = $request->getProperties()['email_content']['template_id'];

        $this->logger->info(sprintf(
            "%s, try to send %s email to: %s, template id: %s",
            __METHOD__,
            $this->getCampaignName(),
            $recipient,
            $templateId
        ));

        $response = $this->apiEmails->postEmail($request);

        if ($response->getStatus() == 200) {
            $this->logger->info(sprintf(
                "%s, sent %s email to: %s, template id: %s",
                __METHOD__,
                $this->getCampaignName(),
                $recipient,
                $templateId
            ));
        } else {
            $this->logger->error(sprintf(
                "%s, failed to send %s email to: %s, template id: %s, code: %s, message: %s",
                __METHOD__,
                $this->getCampaignName(),
                $recipient,
                $templateId,
                $response->getStatus(),
                $response->getMessage()
            ));
        }
    }

    protected function getFrontendUrl(string $route, array $params = [], ?int $storeId = null): string
    {
        if ($storeId !== null) {
            $this->frontendUrlBuilder->setScope($storeId);
        }

        return $this->frontendUrlBuilder->getUrl($route, $params);
    }

    /**
     * Resolve the appropriate store ID for the customer
     */
    protected function resolveStoreId(CustomerInterface $customer): int
    {
        $storeId = $customer->getStoreId();
        if ($storeId === null) {
            $storeId = $this->getWebsiteStoreId($customer);
        }
        return (int)$storeId;
    }

    /**
     * Get either first store ID from a set website or the provided as default
     */
    protected function getWebsiteStoreId(CustomerInterface $customer, $defaultStoreId = null): int
    {
        if ($customer->getWebsiteId() != 0 && empty($defaultStoreId)) {
            $stores = $this->storeManager->getStores(false);
            foreach ($stores as $store) {
                if ($store->getWebsiteId() == $customer->getWebsiteId()) {
                    $defaultStoreId = $store->getId();
                    break;
                }
            }
        }
        return (int)$defaultStoreId;
    }
}
