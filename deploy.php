<?php

namespace Deployer;

require 'recipe/common.php';

set('application', "KEH");
set('writable_use_sudo', false);
set('writable_mode', "chmod");
set('repository', '**************:Inchoo/www.keh.com.git');
set('release_name', function () {
    return date('YmdHis');
});
set('default_stage', 'staging');

set('shared_dirs', [
 /*   'var/composer_home',
    'var/log',
    'var/export',
    'var/import_history',
    'var/session',
    'var/importexport',
    'var/backups',
    'var/tmp',*/
    'var',
    'pub/sitemaps',
    'pub/media',
    'pub/feeds'
]);
set('shared_files', [
    'app/etc/env.php',
    'pub/robots.txt',
    'pub/klevu_sell_export.xml',
    'pub/klevu_blog_export.xml'
]);

set('writable_dirs', [
    'var',
    'pub/static',
    'pub/media',
    'generated'
]);

set('clear_paths', [
    'var/cache/*'
]);

host('keh_apache')
    ->stage('staging')
    ->user('inchoo')
    ->set('deploy_path', '/var/www');

 host('keh1_apache')
    ->stage('staging1')
    ->user('inchoo')
    ->set('deploy_path', '/var/www');

desc('Uploading deployment artifact');
task('deploy:upload_artifact', function () {
    $stage = 'staging';
    if (input()->hasArgument('stage')) {
        $stage = input()->getArgument('stage');
    }
    if($stage == 'staging') {
        $host = 'keh_apache';
    } else {
        $host = 'keh1_apache';
    }

    run("scp artifact.tar.gz inchoo@".$host.":/var/www/");
})->local();

desc('Enable maintenance mode');
task('magento:maintenance:enable', function () {
    run("if [ -d $(echo {{deploy_path}}/current) ]; then {{bin/php}} {{deploy_path}}/current/bin/magento maintenance:enable; fi");
});

desc('Disable maintenance mode');
task('magento:maintenance:disable', function () {
    run("if [ -d $(echo {{deploy_path}}/current) ]; then {{bin/php}} {{deploy_path}}/current/bin/magento maintenance:disable; fi");
});

desc('Upgrade magento database');
task('magento:upgrade:db', function () {
    run("{{bin/php}} {{deploy_path}}/current/bin/magento setup:upgrade --keep-generated");
});

desc('Flush Magento Cache');
task('magento:cache:flush', function () {
    run("{{bin/php}} {{deploy_path}}/current/bin/magento cache:flush");
});

desc('Extract artifact');
task('deploy:extract_artifact', function () {
    run("tar -zxvf {{deploy_path}}/artifact.tar.gz -C {{release_path}}");
});

desc('Deploy your project');
task('deploy', [
    'deploy:info',
    'deploy:prepare',
    'deploy:lock',
    'deploy:release',
    'deploy:upload_artifact',
    'deploy:shared',
    'deploy:extract_artifact',
    'deploy:writable',
    'deploy:clear_paths',
    'magento:maintenance:enable',
    'deploy:symlink',
    'magento:upgrade:db',
    'deploy:unlock',
    'cleanup',
    'success',
    'magento:maintenance:disable'
]);


task('reload:php-fpm', function () {
    $stage = "staging";
    if (input()->hasArgument('stage')) {
        $stage = input()->getArgument('stage');
    }
    if ($stage == 'staging') {
        run('sudo /etc/init.d/php7.4-fpm reload');
    } else {
        run('sudo /etc/init.d/php7.3-fpm reload');
    }
});

after('deploy', 'reload:php-fpm');
