diff --git a/vendor/paypal/module-braintree-core/Plugin/OrderCancellation.php b/vendor/paypal/module-braintree-core/Plugin/OrderCancellation.php
index 61d768ecd..2751afbc3 100644
--- a/vendor/paypal/module-braintree-core/Plugin/OrderCancellation.php
+++ b/vendor/paypal/module-braintree-core/Plugin/OrderCancellation.php
@@ -72,7 +72,7 @@ class OrderCancellation
                 PayPalConfigProvider::PAYPAL_VAULT_CODE
             ];
             if (in_array($payment->getMethod(), $paymentCodes)) {
-                $incrementId = $quote->getReservedOrderId();
+                $incrementId = (string)$quote->getReservedOrderId();
                 if ($incrementId) {
                     $this->orderCancellationService->execute($incrementId);
                 }
