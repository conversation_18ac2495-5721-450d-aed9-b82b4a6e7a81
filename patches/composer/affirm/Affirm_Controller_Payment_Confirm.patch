--- vendor/affirm/magento2/Controller/Payment/Confirm.php	2019-06-25 20:18:03.000000000 +0000
+++ patches/composer/affirm/magento2/Controller/Payment/Confirm.php	2019-07-03 12:08:31.247650088 +0000
@@ -43,13 +43,16 @@
 use Magento\Checkout\Model\Session;
 use Astound\Affirm\Model\Checkout;
 use Magento\Framework\Exception\LocalizedException;
+use Magento\Framework\App\CsrfAwareActionInterface;
+use Magento\Framework\App\RequestInterface;
+use Magento\Framework\App\Request\InvalidRequestException;
 
 /**
  * Class Confirm
  *
  * @package Astound\Affirm\Controller\Payment
  */
-class Confirm extends Action
+class Confirm extends Action implements CsrfAwareActionInterface
 {
     /**
      * Checkout session
@@ -101,6 +104,23 @@
     }
 
     /**
+     * @inheritDoc
+     */
+    public function createCsrfValidationException(
+        RequestInterface $request
+    ): ?InvalidRequestException {
+        return null;
+    }
+
+    /**
+     * @inheritDoc
+     */
+    public function validateForCsrf(RequestInterface $request): ?bool
+    {
+        return true;
+    }
+
+    /**
      * Dispatch request
      *
      * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
