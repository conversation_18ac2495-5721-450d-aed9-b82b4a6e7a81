diff --git a/Service/Menu/SaveRequestProcessor.php b/Service/Menu/SaveRequestProcessor.php
index 219a80729..83de3f3ae 100644
--- a/Service/Menu/SaveRequestProcessor.php
+++ b/Service/Menu/SaveRequestProcessor.php
@@ -201,6 +201,7 @@ class SaveRequestProcessor
         $nodeObject->setIsActive($nodeData['is_active'] ?? '0');
         $nodeObject->setLevel((string) $level);
         $nodeObject->setPosition((string) $position);
+        $nodeObject->setStoreId(array_key_exists('store_id', $nodeData) ? (int)$nodeData['store_id'] : 0);

         $this->processImageParameters($nodeData, $nodeObject);

