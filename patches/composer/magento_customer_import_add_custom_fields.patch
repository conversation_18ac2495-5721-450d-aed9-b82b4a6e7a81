diff --git a/vendor/magento/module-customer-import-export/Model/Import/Customer.php b/vendor/magento/module-customer-import-export/Model/Import/Customer.php
index fd3d4131..81e89589 100644
--- a/vendor/magento/module-customer-import-export/Model/Import/Customer.php
+++ b/vendor/magento/module-customer-import-export/Model/Import/Customer.php
@@ -164,7 +164,12 @@ class Customer extends AbstractCustomer
         'failures_num',
         'first_failure',
         'lock_expires',
-        CustomerInterface::DISABLE_AUTO_GROUP_CHANGE,
+        CustomerInterface::DISABLE_AUTO_GROUP_CHANGE,
+        "affirm_customer_mfp",
+        "inchoodev_socialconnect_facebookid",
+        "inchoodev_socialconnect_facebooktoken",
+        "inchoodev_socialconnect_googleid",
+        "inchoodev_socialconnect_googletoken"
     ];

     /**
