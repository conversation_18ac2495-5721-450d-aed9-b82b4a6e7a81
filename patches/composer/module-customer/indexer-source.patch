diff --git a/Model/Indexer/Source.php b/Model/Indexer/Source.php
index b48e7e5ad..fa04223b8 100644
--- a/Model/Indexer/Source.php
+++ b/Model/Indexer/Source.php
@@ -31,7 +31,7 @@ class Source implements \IteratorAggregate, \Countable, SourceProviderInterface
      */
     public function __construct(
         CollectionFactory $collectionFactory,
-        $batchSize = 10000
+        $batchSize = 25000
     ) {
         $this->customerCollection = $collectionFactory->create();
         $this->batchSize = $batchSize;
@@ -96,17 +96,33 @@ class Source implements \IteratorAggregate, \Countable, SourceProviderInterface
     #[\ReturnTypeWillChange]
     public function getIterator()
     {
-        $this->customerCollection->setPageSize($this->batchSize);
-        $lastPage = $this->customerCollection->getLastPageNumber();
-        $pageNumber = 1;
-        do {
+        $connection = $this->customerCollection->getConnection();
+        $where = $this->customerCollection->getSelect()->getPart('where');
+        $query = $connection->select()
+            ->from(
+                $this->customerCollection->getMainTable(),
+                'max(entity_id) as max_id'
+            );
+
+        $maxId = (int)$connection->fetchCol($query)[0];
+        $lastPage = ceil($maxId / $this->batchSize);
+
+        for ($pageNumber = 1; $pageNumber <= $lastPage; $pageNumber++) {
             $this->customerCollection->clear();
-            $this->customerCollection->setCurPage($pageNumber);
-            foreach ($this->customerCollection->getItems() as $key => $value) {
-                yield $key => $value;
+            $this->customerCollection->getSelect()->reset(\Magento\Framework\DB\Select::WHERE);
+            $this->customerCollection->getSelect()->setPart('where', $where);
+            $this->customerCollection->addFieldToFilter(
+                'entity_id',
+                [
+                    'from' => ($pageNumber-1)*$this->batchSize + 1,
+                    'to' => ($pageNumber)*$this->batchSize
+                ]
+            );
+
+            foreach ($this->customerCollection->getItems() as $key => &$item) {
+                yield $key => $item;
             }
-            $pageNumber++;
-        } while ($pageNumber <= $lastPage);
+        }
     }

     /**
