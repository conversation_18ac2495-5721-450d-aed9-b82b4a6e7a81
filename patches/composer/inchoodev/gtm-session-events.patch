diff --git a/Model/EnhancedLayer.php b/Model/EnhancedLayer.php
index 59925d6..162b2a7 100644
--- a/Model/EnhancedLayer.php
+++ b/Model/EnhancedLayer.php
@@ -159,7 +159,7 @@ class EnhancedLayer extends DataObject
                     'ecomm_totalvalue' => $totalValue
                 ];
 
-                $data = array_merge($data['enhancedEventsCollection']['ecommerce'], $ecommData);
+                $data['enhancedEventsCollection']['ecommerce'] = array_merge($data['enhancedEventsCollection']['ecommerce'], $ecommData);
             }
 
             $this->addEvent($data);
