diff --git a/lib/web/jquery/ui-modules/effects/effect-blind.js b/lib/web/jquery/ui-modules/effects/effect-blind.js
index 95c1874c5..442da4efb 100644
--- a/lib/web/jquery/ui-modules/effects/effect-blind.js
+++ b/lib/web/jquery/ui-modules/effects/effect-blind.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define('jquery/ui-modules/effects/effect-blind' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-bounce.js b/lib/web/jquery/ui-modules/effects/effect-bounce.js
index af627f1f2..921ab2977 100644
--- a/lib/web/jquery/ui-modules/effects/effect-bounce.js
+++ b/lib/web/jquery/ui-modules/effects/effect-bounce.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-bounce' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-clip.js b/lib/web/jquery/ui-modules/effects/effect-clip.js
index f34f45c4e..460461da9 100644
--- a/lib/web/jquery/ui-modules/effects/effect-clip.js
+++ b/lib/web/jquery/ui-modules/effects/effect-clip.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-clip' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-drop.js b/lib/web/jquery/ui-modules/effects/effect-drop.js
index f34f45c4e..015f8f4c1 100644
--- a/lib/web/jquery/ui-modules/effects/effect-drop.js
+++ b/lib/web/jquery/ui-modules/effects/effect-drop.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-drop' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-explode.js b/lib/web/jquery/ui-modules/effects/effect-explode.js
index 4ce3cd162..fef01ed7a 100644
--- a/lib/web/jquery/ui-modules/effects/effect-explode.js
+++ b/lib/web/jquery/ui-modules/effects/effect-explode.js
@@ -21,7 +21,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-explode' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-fade.js b/lib/web/jquery/ui-modules/effects/effect-fade.js
index 798baa9d4..d7e28c837 100644
--- a/lib/web/jquery/ui-modules/effects/effect-fade.js
+++ b/lib/web/jquery/ui-modules/effects/effect-fade.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-fade' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-fold.js b/lib/web/jquery/ui-modules/effects/effect-fold.js
index 77a872a7e..159ebc229 100644
--- a/lib/web/jquery/ui-modules/effects/effect-fold.js
+++ b/lib/web/jquery/ui-modules/effects/effect-fold.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-fold' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-highlight.js b/lib/web/jquery/ui-modules/effects/effect-highlight.js
index 34838a6b9..c226d6672 100644
--- a/lib/web/jquery/ui-modules/effects/effect-highlight.js
+++ b/lib/web/jquery/ui-modules/effects/effect-highlight.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-highlight' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-puff.js b/lib/web/jquery/ui-modules/effects/effect-puff.js
index ec52765cf..bc7715bd6 100644
--- a/lib/web/jquery/ui-modules/effects/effect-puff.js
+++ b/lib/web/jquery/ui-modules/effects/effect-puff.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-puff' ,[
             "jquery",
             "../version",
             "../effect",
diff --git a/lib/web/jquery/ui-modules/effects/effect-pulsate.js b/lib/web/jquery/ui-modules/effects/effect-pulsate.js
index 47638e50f..bd2d51cf1 100644
--- a/lib/web/jquery/ui-modules/effects/effect-pulsate.js
+++ b/lib/web/jquery/ui-modules/effects/effect-pulsate.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-pulsate' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-scale.js b/lib/web/jquery/ui-modules/effects/effect-scale.js
index e62f5cf74..7c677e6da 100644
--- a/lib/web/jquery/ui-modules/effects/effect-scale.js
+++ b/lib/web/jquery/ui-modules/effects/effect-scale.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-scale' ,[
             "jquery",
             "../version",
             "../effect",
diff --git a/lib/web/jquery/ui-modules/effects/effect-shake.js b/lib/web/jquery/ui-modules/effects/effect-shake.js
index 660ac1e24..8681948b3 100644
--- a/lib/web/jquery/ui-modules/effects/effect-shake.js
+++ b/lib/web/jquery/ui-modules/effects/effect-shake.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-shake' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-size.js b/lib/web/jquery/ui-modules/effects/effect-size.js
index a531c8401..18cfc50f9 100644
--- a/lib/web/jquery/ui-modules/effects/effect-size.js
+++ b/lib/web/jquery/ui-modules/effects/effect-size.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-size' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-slide.js b/lib/web/jquery/ui-modules/effects/effect-slide.js
index 78b7f59b8..70c7bf6ea 100644
--- a/lib/web/jquery/ui-modules/effects/effect-slide.js
+++ b/lib/web/jquery/ui-modules/effects/effect-slide.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-slide' ,[
             "jquery",
             "../version",
             "../effect"
diff --git a/lib/web/jquery/ui-modules/effects/effect-transfer.js b/lib/web/jquery/ui-modules/effects/effect-transfer.js
index 23c95996f..39151a551 100644
--- a/lib/web/jquery/ui-modules/effects/effect-transfer.js
+++ b/lib/web/jquery/ui-modules/effects/effect-transfer.js
@@ -19,7 +19,7 @@
     if ( typeof define === "function" && define.amd ) {

         // AMD. Register as an anonymous module.
-        define( [
+        define( 'jquery/ui-modules/effects/effect-transfer' ,[
             "jquery",
             "../version",
             "../effect"
