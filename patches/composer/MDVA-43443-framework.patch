diff --git a/vendor/magento/framework/Filter/DirectiveProcessor/DependDirective.php b/vendor/magento/framework/Filter/DirectiveProcessor/DependDirective.php
index f557f7465b5..83345acd6e5 100644
--- a/vendor/magento/framework/Filter/DirectiveProcessor/DependDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/DependDirective.php
@@ -32,9 +32,13 @@ class DependDirective implements DirectiveProcessorInterface
     }

     /**
-     * @inheritdoc
+     * @param array $construction
+     * @param Template $filter
+     * @param array $templateVariables
+     *
+     * @return string
      */
-    public function process(array $construction, Template $filter, array $templateVariables): string
+    private function resolve(array $construction, Template $filter, array $templateVariables): string
     {
         if (empty($templateVariables)) {
             // If template processing
@@ -48,6 +52,16 @@ class DependDirective implements DirectiveProcessorInterface
         }
     }

+    /**
+     * @inheritdoc
+     */
+    public function process(array $construction, Template $filter, array $templateVariables): string
+    {
+        $result = $this->resolve($construction, $filter, $templateVariables);
+
+        return str_replace(['{', '}'], '', (string) $result);
+    }
+
     /**
      * @inheritdoc
      */
diff --git a/vendor/magento/framework/Filter/DirectiveProcessor/ForDirective.php b/vendor/magento/framework/Filter/DirectiveProcessor/ForDirective.php
index 2b51185b1b5..41cd58118fd 100644
--- a/vendor/magento/framework/Filter/DirectiveProcessor/ForDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/ForDirective.php
@@ -36,14 +36,13 @@ class ForDirective implements DirectiveProcessorInterface
     }

     /**
-     * Filter the string as template.
-     *
      * @param array $construction
      * @param Template $filter
      * @param array $templateVariables
+     *
      * @return string
      */
-    public function process(array $construction, Template $filter, array $templateVariables): string
+    private function resolve(array $construction, Template $filter, array $templateVariables): string
     {
         if (!$this->isValidLoop($construction)) {
             return $construction[0];
@@ -67,6 +66,16 @@ class ForDirective implements DirectiveProcessorInterface
         return $construction[0];
     }

+    /**
+     * @inheritdoc
+     */
+    public function process(array $construction, Template $filter, array $templateVariables): string
+    {
+        $result = $this->resolve($construction, $filter, $templateVariables);
+
+        return str_replace(['{', '}'], '', (string) $result);
+    }
+
     /**
      * Check if the matched construction is valid.
      *
diff --git a/vendor/magento/framework/Filter/DirectiveProcessor/IfDirective.php b/vendor/magento/framework/Filter/DirectiveProcessor/IfDirective.php
index 7fedc7946f2..469dae71d06 100644
--- a/vendor/magento/framework/Filter/DirectiveProcessor/IfDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/IfDirective.php
@@ -32,9 +32,13 @@ class IfDirective implements DirectiveProcessorInterface
     }

     /**
-     * @inheritdoc
+     * @param array $construction
+     * @param Template $filter
+     * @param array $templateVariables
+     *
+     * @return string
      */
-    public function process(array $construction, Template $filter, array $templateVariables): string
+    private function resolve(array $construction, Template $filter, array $templateVariables): string
     {
         if (empty($templateVariables)) {
             return $construction[0];
@@ -50,6 +54,16 @@ class IfDirective implements DirectiveProcessorInterface
         }
     }

+    /**
+     * @inheritdoc
+     */
+    public function process(array $construction, Template $filter, array $templateVariables): string
+    {
+        $result = $this->resolve($construction, $filter, $templateVariables);
+
+        return str_replace(['{', '}'], '', (string) $result);
+    }
+
     /**
      * @inheritdoc
      */
diff --git a/vendor/magento/framework/Filter/DirectiveProcessor/SimpleDirective.php b/vendor/magento/framework/Filter/DirectiveProcessor/SimpleDirective.php
index 9f4b30d0c96..b9280aec283 100644
--- a/vendor/magento/framework/Filter/DirectiveProcessor/SimpleDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/SimpleDirective.php
@@ -68,7 +68,7 @@ class SimpleDirective implements DirectiveProcessorInterface
                 ->get($construction['directiveName']);
         } catch (\InvalidArgumentException $e) {
             // This directive doesn't have a SimpleProcessor
-            return $construction[0];
+            return '';
         }

         $parameters = $this->extractParameters($construction, $filter, $templateVariables);
@@ -79,6 +79,8 @@ class SimpleDirective implements DirectiveProcessorInterface
             !empty($construction['content']) ? $filter->filter($construction['content']) : null
         );

+        $value = str_replace(['{', '}'], '', (string) $value);
+
         $value = $this->filterApplier->applyFromRawParam(
             $construction['filters'] ?? '',
             $value,
diff --git a/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php b/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php
index 78034d70ba5..a7d6790acc7 100644
--- a/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php
+++ b/vendor/magento/framework/Filter/DirectiveProcessor/VarDirective.php
@@ -55,10 +55,7 @@ class VarDirective implements DirectiveProcessorInterface
             $result = $this->filterApplier->applyFromRawParam($construction['filters'], $result);
         }

-        $pattern = '/{{.*?}}/';
-        do {
-            $result = preg_replace($pattern, '', (string)$result);
-        } while (preg_match($pattern, $result));
+        $result = str_replace(['{', '}'], '', (string) $result);

         return $result;
     }