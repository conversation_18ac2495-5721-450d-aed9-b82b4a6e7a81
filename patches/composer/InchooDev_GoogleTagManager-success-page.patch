diff --git a/Model/EnhancedLayer.php b/Model/EnhancedLayer.php
index b6ec975..4119b23 100644
--- a/Model/EnhancedLayer.php
+++ b/Model/EnhancedLayer.php
@@ -203,7 +203,7 @@ class EnhancedLayer extends DataObject
          * @var \Magento\Sales\Model\Order\Item $item
          */
         foreach ($order->getItems() as $item) {
-            if ($item->isDeleted() || $item->getParentItemId()) {
+            if ($item->isDeleted() || $item->getParentItemId() || !$item->getProduct()) {
                 continue;
             }

diff --git a/Model/OnLoadEventLayer.php b/Model/OnLoadEventLayer.php
index b22499e..cade4e4 100644
--- a/Model/OnLoadEventLayer.php
+++ b/Model/OnLoadEventLayer.php
@@ -124,6 +124,10 @@ class OnLoadEventLayer extends DataObject
         $productData = ['ecomm_totalvalue' => 0];
         foreach ($order->getAllVisibleItems() as $orderItem) {
             $product = $orderItem->getProduct();
+            if (!$product) {
+                continue;
+            }
+
             $price = $this->productHelper->getProductPrice($product);
             $productData['ecomm_prodid'][] = $this->remarketingHelper->getProductIdenfiticationValue($product);
             $productData['ecomm_pvalue'][] = $price;
@@ -135,10 +139,10 @@ class OnLoadEventLayer extends DataObject
             'event' => 'fireRemarketingTag',
             'google_tag_params' => array_filter([
                 'ecomm_pagetype' => $pageType,
-                'ecomm_prodid' => $productData['ecomm_prodid'],
-                'ecomm_pvalue' => $productData['ecomm_pvalue'],
-                'ecomm_pname' => $productData['ecomm_pname'],
-                'ecomm_totalvalue' => $productData['ecomm_totalvalue'],
+                'ecomm_prodid' => isset($productData['ecomm_prodid']) ? $productData['ecomm_prodid'] : [],
+                'ecomm_pvalue' => isset($productData['ecomm_pvalue']) ? $productData['ecomm_pvalue'] : [],
+                'ecomm_pname' => isset($productData['ecomm_pname']) ? $productData['ecomm_pname'] : [],
+                'ecomm_totalvalue' => isset($productData['ecomm_totalvalue']) ? $productData['ecomm_totalvalue'] : [],
             ])
         ];

--
2.36.1

