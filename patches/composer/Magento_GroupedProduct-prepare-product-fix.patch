diff --git a/Model/Product/Type/Grouped.php b/Model/Product/Type/Grouped.php
index 24e4e49..c36f082 100644
--- a/Model/Product/Type/Grouped.php
+++ b/Model/Product/Type/Grouped.php
@@ -386,7 +386,7 @@ class Grouped extends \Magento\Catalog\Model\Product\Type\AbstractType
         }
         $associatedProducts = !$isStrictProcessMode || !empty($productsInfo)
             ? $this->getAssociatedProducts($product)
-            : false;
+            : [];

         foreach ($associatedProducts as $subProduct) {
             $qty = $productsInfo[$subProduct->getId()];
