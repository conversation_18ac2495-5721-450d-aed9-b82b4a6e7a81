diff --git a/Plugin/Model/ResourceModel/Order/OrderGridCollectionFilter.php b/Plugin/Model/ResourceModel/Order/OrderGridCollectionFilter.php
index 995bb83..63807c3 100644
--- a/Plugin/Model/ResourceModel/Order/OrderGridCollectionFilter.php
+++ b/Plugin/Model/ResourceModel/Order/OrderGridCollectionFilter.php
@@ -52,11 +52,11 @@ class OrderGridCollectionFilter
                 }
             }

-            $fieldName = $subject->getConnection()->quoteIdentifier($field);
-            $condition = $subject->getConnection()->prepareSqlCondition($fieldName, $condition);
-            $subject->getSelect()->where($condition, null, Select::TYPE_CONDITION);
-
-            return $subject;
+//            $fieldName = $subject->getConnection()->quoteIdentifier($field);
+//            $condition = $subject->getConnection()->prepareSqlCondition($fieldName, $condition);
+//            $subject->getSelect()->where($condition, null, Select::TYPE_CONDITION);
+//
+//            return $subject;
         }

         return $proceed($field, $condition);
