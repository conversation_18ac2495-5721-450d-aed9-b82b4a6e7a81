diff --git a/vendor/magento/module-page-builder/view/adminhtml/templates/stage/render.phtml b/vendor/magento/module-page-builder/view/adminhtml/templates/stage/render.phtml
index 2586df2b3..30406e8d7 100644
--- a/vendor/magento/module-page-builder/view/adminhtml/templates/stage/render.phtml
+++ b/vendor/magento/module-page-builder/view/adminhtml/templates/stage/render.phtml
@@ -20,19 +20,21 @@ $pageBuilderConfig = $block->getPageBuilderConfig();
  * get overridden as our template engine cannot load.
  */
 $script = <<<SCRIPT
-    require([
-        'ko',
-        'Magento_Ui/js/lib/knockout/template/engine'
-    ], function (ko, templateEngine) {
-        'use strict';
-
-        ko.uid = 0;
-        ko.setTemplateEngine(templateEngine);
-    });
-
-    require(['Magento_PageBuilder/js/master-format/render/frame'], function (listen) {
-        listen({$pageBuilderConfig});
-    });
+    window.onload = function() {
+        require([
+            'ko',
+            'Magento_Ui/js/lib/knockout/template/engine'
+        ], function (ko, templateEngine) {
+            'use strict';
+    
+            ko.uid = 0;
+            ko.setTemplateEngine(templateEngine);
+        });
+    
+        require(['Magento_PageBuilder/js/master-format/render/frame'], function (listen) {
+            listen({$pageBuilderConfig});
+        });
+    }
 SCRIPT;
 ?>
 <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $script, false) ?>
