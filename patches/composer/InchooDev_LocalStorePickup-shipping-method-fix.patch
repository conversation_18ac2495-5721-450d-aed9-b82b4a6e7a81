diff --git a/Plugin/Order/Email/SenderBuilderPlugin.php b/Plugin/Order/Email/SenderBuilderPlugin.php
index c5268a4..96af1ba 100644
--- a/Plugin/Order/Email/SenderBuilderPlugin.php
+++ b/Plugin/Order/Email/SenderBuilderPlugin.php
@@ -70,7 +70,7 @@ class SenderBuilderPlugin
     {
         $order = $this->templateContainer->getTemplateVars()['order'];
         $shippingMethod = $order->getShippingMethod(true);
-        $carrierCode = $shippingMethod->getCarrierCode();
+        $carrierCode = $shippingMethod ? $shippingMethod->getCarrierCode() : false;
         $isGuest = $order->getCustomerIsGuest();
         $areCustomEmailsAllowed = $this->scopeConfig->isSetFlag(self::CUSTOM_EMAILS_ENABLED);
         $entityType = $order->getEntityType();
