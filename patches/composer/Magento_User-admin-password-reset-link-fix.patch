diff --git a/Model/Notificator.php b/Model/Notificator.php
index 3e36cd1..66b77d3 100644
--- a/Model/Notificator.php
+++ b/Model/Notificator.php
@@ -82,12 +82,16 @@ class Notificator implements NotificatorInterface
         string $toEmail,
         string $toName
     ): void {
+
+        $store = $this->storeManager->getStore(Store::DEFAULT_STORE_ID);
+        $store->setDisableStoreInUrl(true);
+
         $transport = $this->transportBuilder
             ->setTemplateIdentifier($this->config->getValue($templateConfigId))
             ->setTemplateModel(BackendTemplate::class)
             ->setTemplateOptions([
                 'area' => FrontNameResolver::AREA_CODE,
-                'store' => Store::DEFAULT_STORE_ID
+                'store' => $store->getId()
             ])
             ->setTemplateVars($templateVars)
             ->setFrom(
