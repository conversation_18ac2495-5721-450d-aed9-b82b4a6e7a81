diff --git a/Indexer/IndexMutexException.php b/Indexer/IndexMutexException.php
new file mode 100644
index 0000000..150f9a2
--- /dev/null
+++ b/Indexer/IndexMutexException.php
@@ -0,0 +1,24 @@
+<?php
+/**
+ * Copyright Â© Magento, Inc. All rights reserved.
+ * See COPYING.txt for license details.
+ */
+declare(strict_types=1);
+
+namespace Magento\Framework\Indexer;
+
+use RuntimeException;
+
+/**
+ * Exception thrown when index lock could not be acquired
+ */
+class IndexMutexException extends RuntimeException
+{
+    /**
+     * @param string $indexerName
+     */
+    public function __construct(string $indexerName)
+    {
+        parent::__construct('Could not acquire lock for index: ' . $indexerName);
+    }
+}
diff --git a/Indexer/IndexMutexInterface.php b/Indexer/IndexMutexInterface.php
new file mode 100644
index 0000000..4b79254
--- /dev/null
+++ b/Indexer/IndexMutexInterface.php
@@ -0,0 +1,23 @@
+<?php
+/**
+ * Copyright Â© Magento, Inc. All rights reserved.
+ * See COPYING.txt for license details.
+ */
+declare(strict_types=1);
+
+namespace Magento\Framework\Indexer;
+
+/**
+ * Intended to prevent race conditions between indexers using the same index table.
+ */
+interface IndexMutexInterface
+{
+    /**
+     * Acquires a lock for an indexer, executes callable and releases the lock after.
+     *
+     * @param string $indexerName
+     * @param callable $callback
+     * @throws IndexMutexException
+     */
+    public function execute(string $indexerName, callable $callback): void;
+}
