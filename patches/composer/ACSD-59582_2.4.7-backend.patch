diff --git a/ViewModel/RequireJsConfigModifierInterface.php b/ViewModel/RequireJsConfigModifierInterface.php
new file mode 100644
index 000000000000..07bd7ca989e6
--- /dev/null
+++ b/ViewModel/RequireJsConfigModifierInterface.php
@@ -0,0 +1,22 @@
+<?php
+/**
+ * Copyright 2024 Adobe
+ * All Rights Reserved.
+ */
+declare(strict_types=1);
+
+namespace Magento\Backend\ViewModel;
+
+/**
+ * View model interface for requirejs configuration modifier
+ */
+interface RequireJsConfigModifierInterface
+{
+    /**
+     * Modifies requirejs configuration
+     *
+     * @param array $config requirejs configuration
+     * @return array
+     */
+    public function modify(array $config): array;
+}
diff --git a/view/adminhtml/templates/page/js/require_js.phtml b/view/adminhtml/templates/page/js/require_js.phtml
index 6fa41e107995..1b1a5d7fcaf3 100644
--- a/view/adminhtml/templates/page/js/require_js.phtml
+++ b/view/adminhtml/templates/page/js/require_js.phtml
@@ -5,12 +5,20 @@
  */

 /** @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer */
+/** @var \Magento\Backend\Block\Page\RequireJs $block */
+
+$requireJsConfig = [
+    'baseUrl' => $block->getViewFileUrl('/'),
+];
+
+$configModifier = $block->getConfigModifier();
+$requireJsConfig = $configModifier instanceof \Magento\Backend\ViewModel\RequireJsConfigModifierInterface
+    ? $configModifier->modify($requireJsConfig)
+    : $requireJsConfig;

 $scriptString = '
     var BASE_URL = \'' . /* @noEscape */ $block->getUrl('*') . '\';
     var FORM_KEY = \'' . /* @noEscape */ $block->getFormKey() . '\';
-    var require = {
-        \'baseUrl\': \'' . /* @noEscape */ $block->getViewFileUrl('/') . '\'
-    };';
-
-echo /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false);
+    var require = ' . /* @noEscape */ json_encode($requireJsConfig) .';';
+?>
+<?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
