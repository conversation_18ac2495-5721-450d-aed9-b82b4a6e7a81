diff --git a/Model/Carrier.php b/Model/Carrier.php
index fa59d618c..39c5fad0d 100644
--- a/Model/Carrier.php
+++ b/Model/Carrier.php
@@ -718,6 +718,7 @@ class Carrier extends AbstractCarrierOnline implements \Magento\Shipping\Model\C
                 'INTERNATIONAL_FIRST' => __('International First'),
                 'INTERNATIONAL_GROUND' => __('International Ground'),
                 'INTERNATIONAL_PRIORITY' => __('International Priority'),
+                'FEDEX_INTERNATIONAL_PRIORITY_EXPRESS' => __('International Priority Express'),
                 'INTERNATIONAL_PRIORITY_FREIGHT' => __('Intl Priority Freight'),
                 'PRIORITY_OVERNIGHT' => __('Priority Overnight'),
                 'SMART_POST' => __('Smart Post'),
