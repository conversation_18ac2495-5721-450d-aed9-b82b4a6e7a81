diff --git a/vendor/magento/module-catalog-search/Model/Indexer/Fulltext/Action/Full.php b/vendor/magento/module-catalog-search/Model/Indexer/Fulltext/Action/Full.php
index 569cb0370..e47faa782 100644
--- a/vendor/magento/module-catalog-search/Model/Indexer/Fulltext/Action/Full.php
+++ b/vendor/magento/module-catalog-search/Model/Indexer/Fulltext/Action/Full.php
@@ -420,8 +420,31 @@ class Full
      */
     private function getRelatedProducts($products)
     {
+        $groupedProducts = [];
+        $otherProducts = [];
+
+        foreach ($products as $product) {
+            if ($product['type_id'] == 'grouped') {
+                $groupedProducts[] = $product;
+            } else {
+                $otherProducts[] = $product;
+            }
+        }
+
+        $query = $this->connection
+            ->select()
+            ->from('catalog_product_link')
+            ->where('product_id IN (?)', array_column($groupedProducts, 'entity_id'));
+
+        $result = $this->connection->fetchAssoc($query);
+
         $relatedProducts = [];
-        foreach ($products as $productData) {
+
+        foreach ($result as $row) {
+            $relatedProducts[$row['product_id']][] = $row['linked_product_id'];
+        }
+
+        foreach ($otherProducts as $productData) {
             $relatedProducts[$productData['entity_id']] = $this->dataProvider->getProductChildIds(
                 $productData['entity_id'],
                 $productData['type_id']
