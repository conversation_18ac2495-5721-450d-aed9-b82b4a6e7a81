diff --git a/Model/EmailNotification.php b/Model/EmailNotification.php
index 55d82e0..8312f6a 100644
--- a/Model/EmailNotification.php
+++ b/Model/EmailNotification.php
@@ -308,7 +308,7 @@ class EmailNotification implements EmailNotificationInterface
             $storeIds = $this->storeManager->getWebsite($customer->getWebsiteId())->getStoreIds();
             $defaultStoreId = reset($storeIds);
         }
-        return $defaultStoreId;
+        return (int)$defaultStoreId;
     }

     /**
@@ -320,7 +320,7 @@ class EmailNotification implements EmailNotificationInterface
     public function passwordReminder(CustomerInterface $customer): void
     {
         $storeId = $customer->getStoreId();
-        if ($storeId === null) {
+        if ($storeId === null || $storeId == 0) {
             $storeId = $this->getWebsiteStoreId($customer);
         }

@@ -344,7 +344,7 @@ class EmailNotification implements EmailNotificationInterface
     public function passwordResetConfirmation(CustomerInterface $customer): void
     {
         $storeId = $customer->getStoreId();
-        if ($storeId === null) {
+        if ($storeId === null || $storeId == 0) {
             $storeId = $this->getWebsiteStoreId($customer);
         }
