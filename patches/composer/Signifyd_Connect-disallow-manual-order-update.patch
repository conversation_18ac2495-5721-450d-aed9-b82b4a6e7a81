diff --git a/Controller/Webhooks/Index.php b/Controller/Webhooks/Index.php
index 4106b413b..5c44e64f0 100644
--- a/Controller/Webhooks/Index.php
+++ b/Controller/Webhooks/Index.php
@@ -8,6 +8,7 @@ namespace Signifyd\Connect\Controller\Webhooks;
 use Magento\Framework\App\Action\Context;
 use Magento\Framework\App\Response\Http;
 use Magento\Framework\Exception\LocalizedException;
+use Magento\Sales\Model\Order;
 use Magento\Sales\Model\OrderFactory;
 use Magento\Sales\Model\ResourceModel\Order as OrderResourceModel;
 use Magento\Store\Model\StoreManagerInterface;
@@ -305,6 +306,10 @@ class Index extends Action
             $order = $this->orderFactory->create();
             $this->signifydOrderResourceModel->load($order, $case->getData('order_id'));

+            if (in_array($order->getState(), [Order::STATE_CANCELED, Order::STATE_COMPLETE, Order::STATE_CLOSED])) {
+                return;
+            }
+
             if ($order->isEmpty()) {
                 $httpCode = Http::STATUS_CODE_400;
                 throw new LocalizedException(__("Order not found"));
