diff --git a/vendor/paypal/module-braintree-core/Plugin/SalesOrderGridPlugin.php b/vendor/paypal/module-braintree-core/Plugin/SalesOrderGridPlugin.php
index 61d768ecd..2751afbc3 100644333
--- a/vendor/paypal/module-braintree-core/Plugin/SalesOrderGridPlugin.php
+++ b/vendor/paypal/module-braintree-core/Plugin/SalesOrderGridPlugin.php
@@ -23,23 +23,23 @@
      */
     public function beforeLoad(Collection $subject, bool $printQuery = false, bool $logQuery = false): array
     {
-        if (!$subject->isLoaded()) {
-            $primaryKey = $subject->getResource()->getIdFieldName();
-            $tableName = $subject->getResource()->getTable('braintree_transaction_details');
-            $salesOrderTable = $subject->getResource()->getTable('sales_order');
+//        if (!$subject->isLoaded()) {
+//            $primaryKey = $subject->getResource()->getIdFieldName();
+//            $tableName = $subject->getResource()->getTable('braintree_transaction_details');
+//            $salesOrderTable = $subject->getResource()->getTable('sales_order');
+//
+//            $subject->getSelect()->joinLeft(
+//                $tableName,
+//                $tableName . '.order_id = main_table.' . $primaryKey,
+//                $tableName . '.transaction_source'
+//            );
+//            $subject->getSelect()->joinLeft(
+//                $salesOrderTable,
+//                $salesOrderTable . '.entity_id = main_table.' . $primaryKey,
+//                $salesOrderTable . '.dispute_status'
+//            );
+//        }

-            $subject->getSelect()->joinLeft(
-                $tableName,
-                $tableName . '.order_id = main_table.' . $primaryKey,
-                $tableName . '.transaction_source'
-            );
-            $subject->getSelect()->joinLeft(
-                $salesOrderTable,
-                $salesOrderTable . '.entity_id = main_table.' . $primaryKey,
-                $salesOrderTable . '.dispute_status'
-            );
-        }
-
         return [$printQuery, $logQuery];
     }
 }
diff --git a/vendor/paypal/module-braintree-core/view/adminhtml/ui_component/sales_order_grid.xml b/vendor/paypal/module-braintree-core/view/adminhtml/ui_component/sales_order_grid.xml
index 61d768ecd..2751afbc3 100644555
--- a/vendor/paypal/module-braintree-core/view/adminhtml/ui_component/sales_order_grid.xml
+++ b/vendor/paypal/module-braintree-core/view/adminhtml/ui_component/sales_order_grid.xml
@@ -8,17 +8,17 @@
 <listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
     <columns name="sales_order_columns">
-        <column name="transaction_source">
-            <settings>
-                <filter>text</filter>
-                <label translate="true">Braintree Transaction Source</label>
-            </settings>
-        </column>
-        <column name="dispute_status">
-            <settings>
-                <filter>text</filter>
-                <label translate="true">Dispute State</label>
-            </settings>
-        </column>
+<!--        <column name="transaction_source">-->
+<!--            <settings>-->
+<!--                <filter>text</filter>-->
+<!--                <label translate="true">Braintree Transaction Source</label>-->
+<!--            </settings>-->
+<!--        </column>-->
+<!--        <column name="dispute_status">-->
+<!--            <settings>-->
+<!--                <filter>text</filter>-->
+<!--                <label translate="true">Dispute State</label>-->
+<!--            </settings>-->
+<!--        </column>-->
     </columns>
 </listing>
