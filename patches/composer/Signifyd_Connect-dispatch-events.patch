diff --git a/Model/UpdateOrder/Cancel.php b/Model/UpdateOrder/Cancel.php
index 65196d6..d56c44e 100644
--- a/Model/UpdateOrder/Cancel.php
+++ b/Model/UpdateOrder/Cancel.php
@@ -5,6 +5,7 @@
  */
 namespace Signifyd\Connect\Model\UpdateOrder;

+use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
 use Magento\Framework\Exception\LocalizedException;
 use Magento\Sales\Model\OrderFactory;
 use Magento\Sales\Model\ResourceModel\Order as OrderResourceModel;
@@ -48,6 +49,11 @@ class Cancel
      */
     protected $signifydOrderResourceModel;

+    /**
+     * @var EventManagerInterface
+     */
+    protected $eventManager;
+
     /**
      * @param ConfigHelper $configHelper
      * @param OrderHelper $orderHelper
@@ -55,6 +61,7 @@ class Cancel
      * @param OrderResourceModel $orderResourceModel
      * @param OrderFactory $orderFactory
      * @param SignifydOrderResourceModel $signifydOrderResourceModel
+     * @param EventManagerInterface $eventManager
      */
     public function __construct(
         ConfigHelper $configHelper,
@@ -62,7 +69,8 @@ class Cancel
         Logger $logger,
         OrderResourceModel $orderResourceModel,
         OrderFactory $orderFactory,
-        SignifydOrderResourceModel $signifydOrderResourceModel
+        SignifydOrderResourceModel $signifydOrderResourceModel,
+        EventManagerInterface $eventManager
     ) {
         $this->configHelper = $configHelper;
         $this->orderHelper = $orderHelper;
@@ -70,6 +78,7 @@ class Cancel
         $this->orderResourceModel = $orderResourceModel;
         $this->orderFactory = $orderFactory;
         $this->signifydOrderResourceModel = $signifydOrderResourceModel;
+        $this->eventManager = $eventManager;
     }

     public function __invoke($order, $case, $orderAction, $completeCase)
@@ -89,6 +98,11 @@ class Cancel
                     $order,
                     "Signifyd: order canceled, {$orderAction["reason"]}"
                 );
+
+                $this->eventManager->dispatch(
+                    'signifyd_order_process',
+                    ['order' => $order, 'action' => 'cancel']
+                );
             } catch (\Exception $e) {
                 $this->logger->debug($e->__toString(), ['entity' => $order]);
                 $case->setEntries('fail', 1);
diff --git a/Model/UpdateOrder/Capture.php b/Model/UpdateOrder/Capture.php
index 63e50e1..6cf9e29 100644
--- a/Model/UpdateOrder/Capture.php
+++ b/Model/UpdateOrder/Capture.php
@@ -6,6 +6,7 @@
 namespace Signifyd\Connect\Model\UpdateOrder;

 use Magento\Framework\DB\TransactionFactory;
+use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
 use Magento\Sales\Model\Order\Email\Sender\InvoiceSender;
 use Magento\Sales\Model\OrderFactory;
 use Magento\Sales\Model\ResourceModel\Order as OrderResourceModel;
@@ -71,6 +72,11 @@ class Capture
      */
     protected $signifydOrderResourceModel;

+    /**
+     * @var EventManagerInterface
+     */
+    protected $eventManager;
+
     /**
      * @param ConfigHelper $configHelper
      * @param OrderHelper $orderHelper
@@ -82,6 +88,7 @@ class Capture
      * @param TransactionFactory $transactionFactory
      * @param OrderFactory $orderFactory
      * @param SignifydOrderResourceModel $signifydOrderResourceModel
+     * @param EventManagerInterface $eventManager
      */
     public function __construct(
         ConfigHelper $configHelper,
@@ -93,7 +100,8 @@ class Capture
         InvoiceResourceModel $invoiceResourceModel,
         TransactionFactory $transactionFactory,
         OrderFactory $orderFactory,
-        SignifydOrderResourceModel $signifydOrderResourceModel
+        SignifydOrderResourceModel $signifydOrderResourceModel,
+        EventManagerInterface $eventManager
     ) {
         $this->configHelper = $configHelper;
         $this->orderHelper = $orderHelper;
@@ -105,6 +113,7 @@ class Capture
         $this->transactionFactory = $transactionFactory;
         $this->orderFactory = $orderFactory;
         $this->signifydOrderResourceModel = $signifydOrderResourceModel;
+        $this->eventManager = $eventManager;
     }

     public function __invoke($order, $case, $enableTransaction, $completeCase)
@@ -147,6 +156,11 @@ class Capture
                 $this->sendInvoice($invoice, $order);

                 $completeCase = true;
+
+                $this->eventManager->dispatch(
+                    'signifyd_order_process',
+                    ['order' => $order, 'action' => 'capture']
+                );
             } elseif ($order->getInvoiceCollection()->count() > 0) {
                 $this->logger->info("Invoice already created");
                 $completeCase = true;
diff --git a/Observer/Purchase.php b/Observer/Purchase.php
index 64aff6b6b..cc4e23d9b 100755
--- a/Observer/Purchase.php
+++ b/Observer/Purchase.php
@@ -6,6 +6,7 @@

 namespace Signifyd\Connect\Observer;

+use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
 use Magento\Framework\Event\Observer;
 use Magento\Framework\Event\ObserverInterface;
 use Signifyd\Connect\Model\Registry;
@@ -129,6 +130,11 @@ class Purchase implements ObserverInterface
      */
     public $client;

+    /**
+     * @var EventManagerInterface
+     */
+    protected $eventManager;
+
     /**
      * @var PaymentVerificationFactory
      */
@@ -157,6 +163,7 @@ class Purchase implements ObserverInterface
      * @param RecipientFactory $recipientFactory
      * @param SaleOrderFactory $saleOrderFactory
      * @param Client $client
+     * @param EventManagerInterface $eventManager
      * @param PaymentVerificationFactory $paymentVerificationFactory
      * @param Registry $registry
      */
@@ -177,6 +184,7 @@ class Purchase implements ObserverInterface
         RecipientFactory $recipientFactory,
         SaleOrderFactory $saleOrderFactory,
         Client $client,
+        EventManagerInterface $eventManager,
         PaymentVerificationFactory $paymentVerificationFactory,
         Registry $registry
     ) {
@@ -196,6 +204,7 @@ class Purchase implements ObserverInterface
         $this->recipientFactory = $recipientFactory;
         $this->saleOrderFactory = $saleOrderFactory;
         $this->client = $client;
+        $this->eventManager = $eventManager;
         $this->paymentVerificationFactory = $paymentVerificationFactory;
         $this->registry = $registry;
     }
@@ -641,6 +650,10 @@ class Purchase implements ObserverInterface

             if ($isPassive === false) {
                 $order->hold();
+                $this->eventManager->dispatch(
+                    'signifyd_order_process',
+                    ['order' => $order, 'action' => 'hold']
+                );
             }

             $order->addCommentToStatusHistory("Signifyd: after order place");
