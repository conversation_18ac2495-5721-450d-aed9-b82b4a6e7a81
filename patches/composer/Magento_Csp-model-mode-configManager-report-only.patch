diff --git a/vendor/magento/module-csp/Model/Mode/ConfigManager.php b/vendor/magento/module-csp/Model/Mode/ConfigManager.php
index bc31a4198..0b1dc7c36 100644
--- a/vendor/magento/module-csp/Model/Mode/ConfigManager.php
+++ b/vendor/magento/module-csp/Model/Mode/ConfigManager.php
@@ -81,24 +81,11 @@ class ConfigManager implements ModeConfigManagerInterface
         }

         $reportOnly = $this->config->getValue(
-            sprintf(
-                'csp/mode/%s_%s/report_only',
-                $configArea,
-                $this->request->getFullActionName()
-            ),
+            'csp/mode/' . $configArea .'/report_only',
             ScopeInterface::SCOPE_STORE,
             $this->storeModel->getStore()
         );

-        if ($reportOnly === null) {
-            // Fallback to default configuration.
-            $reportOnly = $this->config->getValue(
-                'csp/mode/' . $configArea .'/report_only',
-                ScopeInterface::SCOPE_STORE,
-                $this->storeModel->getStore()
-            );
-        }
-
         $reportUri = $this->config->getValue(
             sprintf(
                 'csp/mode/%s_%s/report_uri',
