diff --git a/Model/ResourceModel/Quote/Item/Collection.php b/Model/ResourceModel/Quote/Item/Collection.php
index 01a4e0c..4b3c9ec 100644
--- a/Model/ResourceModel/Quote/Item/Collection.php
+++ b/Model/ResourceModel/Quote/Item/Collection.php
@@ -211,6 +211,7 @@ class Collection extends \Magento\Framework\Model\ResourceModel\Db\VersionContro
          * Assign options and products
          */
         $this->_assignOptions();
+        $this->_removeWarrantyItemsForAbsentProducts();
         $this->_assignProducts();
         $this->resetItemsDataChanged();

@@ -388,7 +389,22 @@ class Collection extends \Magento\Framework\Model\ResourceModel\Db\VersionContro
             foreach ($absentProductsIds as $productIdToExclude) {
                 /** @var \Magento\Quote\Model\Quote\Item $quoteItem */
                 $quoteItem = $this->getItemByColumnValue('product_id', $productIdToExclude);
-                $this->removeItemByKey($quoteItem->getId());
+                $quoteItem->isDeleted(true);
+            }
+        }
+    }
+
+    /**
+     * @return void
+     */
+    protected function _removeWarrantyItemsForAbsentProducts()
+    {
+        foreach ($this as $item) {
+            if ($item->getProductType() == \Extend\Warranty\Model\Product\Type::TYPE_CODE) {
+                $parent = $this->getItemByColumnValue('sku', $item->getOptionByCode('associated_product')->getValue());
+                if (!$parent || $parent->isDeleted()) {
+                    $item->isDeleted(true);
+                }
             }
         }
     }
