diff --git a/Model/PageRepository.php b/Model/PageRepository.php
index 7e84b93..bbb14cb 100644
--- a/Model/PageRepository.php
+++ b/Model/PageRepository.php
@@ -190,7 +190,7 @@ class PageRepository implements PageRepositoryInterface
                 $page->setStoreId($storeId);
             }
             $this->validateLayoutUpdate($page);
-            $this->validateRoutesDuplication($page);
+            //$this->validateRoutesDuplication($page);
             $this->resource->save($page);
             $this->identityMap->add($page);
         } catch (LocalizedException $exception) {
         