diff --git a/Model/Reorder/Reorder.php b/Model/Reorder/Reorder.php
index c763669..388c0ee 100644
--- a/Model/Reorder/Reorder.php
+++ b/Model/Reorder/Reorder.php
@@ -182,11 +182,32 @@ class Reorder
             if ($item->getParentItem() === null) {
                 $orderItemProductIds[] = $item->getProductId();
                 $orderItemsByProductId[$item->getProductId()][$item->getId()] = $item;
+
+                if ($item->getProductType() == \Extend\Warranty\Model\Product\Type::TYPE_CODE) {
+                    $orderItemsWarrantySkuToProductSku[$item->getSku()] = (string)$item->getProductOptionByCode('associated_product');
+                    $orderItemsWarrantySkuToId[$item->getSku()] = $item->getProductId();
+                }
             }
         }
 
         $products = $this->getOrderProducts($storeId, $orderItemProductIds);
 
+        $skus = [];
+        foreach ($products as $product) {
+            $skus[] = $product->getSku();
+        }
+
+        $warrantiesToRemove = [];
+        foreach ($orderItemsWarrantySkuToProductSku as $warrantySku => $productSku) {
+            if (in_array($warrantySku, $skus) && !in_array($productSku, $skus)) {
+                $warrantiesToRemove[] = $orderItemsWarrantySkuToId[$warrantySku];
+            }
+        }
+
+        foreach ($warrantiesToRemove as $id) {
+            unset($products[$id]);
+        }
+
         // compare founded products and throw an error if some product not exists
         $productsNotFound = array_diff($orderItemProductIds, array_keys($products));
         if (!empty($productsNotFound)) {
