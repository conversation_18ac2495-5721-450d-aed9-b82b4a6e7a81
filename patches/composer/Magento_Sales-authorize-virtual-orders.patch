diff --git a/Model/Order/Payment.php b/Model/Order/Payment.php
index 9daa444..3811ecc 100644
--- a/Model/Order/Payment.php
+++ b/Model/Order/Payment.php
@@ -370,6 +370,10 @@ class Payment extends Info implements OrderPaymentInterface
         $methodInstance->validate();
         $action = $methodInstance->getConfigPaymentAction();

+        if ($order->getIsVirtual() && $action == \Magento\Payment\Model\Method\AbstractMethod::ACTION_AUTHORIZE_CAPTURE) {
+            $action = \Magento\Payment\Model\Method\AbstractMethod::ACTION_AUTHORIZE;
+        }
+
         if ($action) {
             if ($methodInstance->isInitializeNeeded()) {
                 $stateObject = new \Magento\Framework\DataObject();
