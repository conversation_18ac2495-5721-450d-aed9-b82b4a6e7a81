diff --git a/Model/Ui/ConfigProvider.php b/Model/Ui/ConfigProvider.php
index 0eda18a..34fb9b4 100644
--- a/Model/Ui/ConfigProvider.php
+++ b/Model/Ui/ConfigProvider.php
@@ -84,9 +84,9 @@ class ConfigProvider implements ConfigProviderInterface
      */
     public function getConfig(): array
     {
-        if (!$this->config->isActive()) {
-            return [];
-        }
+//        if (!$this->config->isActive()) {
+//            return [];
+//        }

         $config = [
             'payment' => [
