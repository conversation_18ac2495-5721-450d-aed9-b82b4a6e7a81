diff --git a/pub/.htaccess b/pub/.htaccess
index d30951e..1e846ca 100644
--- a/pub/.htaccess
+++ b/pub/.htaccess
@@ -128,6 +128,42 @@

     #RewriteBase /magento/

+###########################################
+## sell/faq to shop/faq
+RewriteRule ^sell/faq$ /shop/faq [R=301,L,NE]
+
+## non-www to www
+RewriteCond %{HTTP_HOST} ^keh.com$ [NC]
+RewriteCond %{REQUEST_METHOD} (GET|HEAD|OPTIONS)
+RewriteRule ^(.*)$ https://www.keh.com/$1 [R=301,L,NE]
+
+###########################################
+## non-HTTPS with trailing slash
+RewriteCond %{HTTPS} !=on
+RewriteCond %{HTTP:X-Forwarded-Proto} !https
+RewriteCond %{REQUEST_FILENAME} !-d [NC]
+RewriteCond %{REQUEST_URI} !^/kehmin [NC]
+RewriteCond %{REQUEST_URI} !^/health_check.php [NC]
+RewriteCond %{REQUEST_METHOD} (GET|HEAD|OPTIONS)
+RewriteRule ^(.*)/$ https://%{HTTP_HOST}/$1 [L,R=301,NE]
+
+############################################
+## non-HTTPS
+RewriteCond %{HTTPS} !=on
+RewriteCond %{HTTP:X-Forwarded-Proto} !https
+RewriteCond %{REQUEST_URI} !^/health_check.php [NC]
+RewriteCond %{REQUEST_METHOD} (GET|HEAD|OPTIONS)
+RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301,NE]
+
+############################################
+## HTTPS with trailing slash
+RewriteCond %{REQUEST_FILENAME} !-d [NC]
+RewriteCond %{REQUEST_URI} !^/kehmin [NC]
+RewriteCond %{REQUEST_URI} !^/health_check.php [NC]
+RewriteCond %{REQUEST_METHOD} (GET|HEAD|OPTIONS)
+RewriteRule ^(.*)/$ https://%{HTTP_HOST}/$1 [L,R=301]
+
+
 ############################################
 ## Workaround for HTTP authorization
 ## in CGI environment
 diff --git a/pub/.htaccess b/pub/.htaccess
 index 2f5b3436f..1e435aeae 100644
 --- a/pub/.htaccess
 +++ b/pub/.htaccess
 @@ -313,6 +313,7 @@ ErrorDocument 403 /errors/404.php
  <IfModule mod_headers.c>
      ############################################
      Header set X-UA-Compatible "IE=edge"
 +    Header add Access-Control-Allow-Origin "i.keh.com"

      # `mod_headers` cannot match based on the content-type, however,
      # the `X-UA-Compatible` response header should be send only for
